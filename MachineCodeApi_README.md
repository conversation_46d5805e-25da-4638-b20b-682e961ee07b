# 机器码获取Web API

这是一个简单的C# ASP.NET Core Web API，用于提供机器码获取服务。

## 项目结构

```
MachineCodeApi/
├── Program.cs
├── Controllers/
│   └── MachineCodeController.cs
├── Models/
│   └── MachineCodeRequest.cs
│   └── MachineCodeResponse.cs
└── appsettings.json
```

## 快速启动指南

### 步骤1: 创建并启动C# API服务

1. **打开新的命令提示符或PowerShell窗口**
2. **导航到API项目目录**:
   ```bash
   cd c:\mylog-web\MachineCodeApi
   ```
3. **恢复依赖包**:
   ```bash
   dotnet restore
   ```
4. **编译项目**:
   ```bash
   dotnet build
   ```
5. **启动API服务**:
   ```bash
   dotnet run --urls "http://localhost:5000"
   ```

### 步骤2: 验证API服务运行状态

1. **检查控制台输出** - 应该看到类似以下信息:
   ```
   info: Microsoft.Hosting.Lifetime[14]
         Now listening on: http://localhost:5000
   info: Microsoft.Hosting.Lifetime[0]
         Application started. Press Ctrl+C to shutdown.
   ```

2. **测试健康检查端点**:
   - 在浏览器中访问: http://localhost:5000/api/machinecode/health
   - 应该返回: `{"status":"healthy","timestamp":"2025-09-04T03:08:00.000Z"}`

3. **查看Swagger文档**:
   - 在浏览器中访问: http://localhost:5000/swagger
   - 可以看到API文档和测试界面

### 步骤3: 测试Java应用中的机器码功能

1. **确保Spring Boot应用正在运行** (http://localhost:8080)
2. **访问项目页面**: http://localhost:8080/projects  
3. **点击右上角的"获取机器码"按钮**
4. **查看结果** - 如果一切正常，应该会显示机器码

创建 `Models/MachineCodeRequest.cs`:

```csharp
namespace MachineCodeApi.Models
{
    public class MachineCodeRequest
    {
        public string Key { get; set; } = string.Empty;
    }
}
```

创建 `Models/MachineCodeResponse.cs`:

```csharp
namespace MachineCodeApi.Models
{
    public class MachineCodeResponse
    {
        public bool Success { get; set; }
        public string MachineCode { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }
}
```

### 4. 创建控制器

创建 `Controllers/MachineCodeController.cs`:

```csharp
using Microsoft.AspNetCore.Mvc;
using MachineCodeApi.Models;
using System.Runtime.InteropServices;

namespace MachineCodeApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MachineCodeController : ControllerBase
    {
        private readonly ILogger<MachineCodeController> _logger;

        public MachineCodeController(ILogger<MachineCodeController> logger)
        {
            _logger = logger;
        }

        [HttpPost]
        public ActionResult<MachineCodeResponse> GetMachineCode([FromBody] MachineCodeRequest request)
        {
            try
            {
                _logger.LogInformation("收到机器码获取请求，参数: {Key}", request.Key);

                // 加载ClassPs.dll并调用方法
                var simple3Des = new ClassPs.Simple3Des();
                var machineCode = simple3Des.EnumDC(request.Key ?? "tomsorvision");

                _logger.LogInformation("成功获取机器码");

                return Ok(new MachineCodeResponse
                {
                    Success = true,
                    MachineCode = machineCode,
                    Message = "成功获取机器码"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取机器码时发生错误");
                
                return Ok(new MachineCodeResponse
                {
                    Success = false,
                    MachineCode = string.Empty,
                    Message = $"获取机器码失败: {ex.Message}"
                });
            }
        }

        [HttpGet("health")]
        public ActionResult<object> HealthCheck()
        {
            return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
        }
    }
}
```

### 5. 配置Program.cs

```csharp
var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 配置CORS以允许Java应用访问
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowJavaApp", policy =>
    {
        policy.WithOrigins("http://localhost:8080")
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("AllowJavaApp");
app.UseAuthorization();
app.MapControllers();

app.Run();
```

### 6. 运行API

```bash
dotnet run
```

API将在 http://localhost:5000 上运行。

## API端点

### POST /api/machinecode
获取机器码

请求体:
```json
{
    "key": "tomsorvision"
}
```

响应:
```json
{
    "success": true,
    "machineCode": "生成的机器码",
    "message": "成功获取机器码"
}
```

### GET /api/machinecode/health
健康检查

响应:
```json
{
    "status": "healthy",
    "timestamp": "2025-09-04T02:42:00.000Z"
}
```

## 注意事项

1. 确保ClassPs.dll在API项目的输出目录中
2. API默认运行在端口5000，如需修改请更新Java代码中的配置
3. 建议在生产环境中添加身份验证和授权机制
4. 可以考虑添加API版本控制和限流功能
