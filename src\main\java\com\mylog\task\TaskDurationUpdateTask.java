package com.mylog.task;

import com.mylog.model.UserActivityLog;
import com.mylog.service.TaskService;
import com.mylog.service.UserActivityLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 任务工期更新定时任务
 * 每天早上7点自动执行，更新所有进行中任务的实际工期
 */
@Component
public class TaskDurationUpdateTask {
    
    private static final Logger logger = LoggerFactory.getLogger(TaskDurationUpdateTask.class);
    
    @Autowired
    private TaskService taskService;
    
    @Autowired
    private UserActivityLogService userActivityLogService;
    
    /**
     * 每天上午7点5分自动更新所有进行中任务的实际工期
     */
    @Scheduled(cron = "0 5 7 * * ?") // 每天上午7点5分执行
    public void updateAllTasksDuration() {
        try {
            logger.info("开始执行每日任务工期自动更新");
            String result = taskService.updateAllInProgressTasksDuration();
            logger.info("每日任务工期自动更新完成: {}", result);
            
            // 记录活动日志
            userActivityLogService.logSettingsChange(
                0L, // 系统操作，用户ID为0
                "System", // 系统用户名
                "系统自动更新所有进行中任务的实际工期: " + result, // 描述
                "127.0.0.1", // 本地IP
                "Task", // 实体类型
                null, // 不针对特定实体ID
                "System" // 系统访问类型
            );
        } catch (Exception e) {
            logger.error("执行每日任务工期自动更新时发生错误", e);
        }
    }
    
}