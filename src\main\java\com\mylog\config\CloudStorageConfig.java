package com.mylog.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 云存储配置类
 * 用于配置各种云存储服务的参数
 */
@Component
@ConfigurationProperties(prefix = "cloud.storage")
public class CloudStorageConfig {
    
    /** 是否启用云存储 */
    private boolean enabled = true;
    
    /** 云存储类型 */
    private String type = "disabled";
    
    /** 存储桶名称 */
    private String bucketName;
    
    /** 地域 */
    private String region;
    
    /** 端点URL */
    private String endpoint;
    
    /** 访问配置 */
    private Access access = new Access();
    
    /** 存储桶配置 */
    private Bucket bucket = new Bucket();
    
    /** 临时文件配置 */
    private Temp temp = new Temp();
    
    /** URL配置 */
    private Url url = new Url();
    
    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getBucketName() {
        return bucketName;
    }
    
    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
        this.bucket.setName(bucketName); // 同步设置
    }
    
    public String getRegion() {
        return region;
    }
    
    public void setRegion(String region) {
        this.region = region;
    }
    
    public String getEndpoint() {
        return endpoint;
    }
    
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
    
    public Access getAccess() {
        return access;
    }
    
    public void setAccess(Access access) {
        this.access = access;
    }
    
    public Bucket getBucket() {
        return bucket;
    }
    
    public void setBucket(Bucket bucket) {
        this.bucket = bucket;
    }
    
    public Temp getTemp() {
        return temp;
    }
    
    public void setTemp(Temp temp) {
        this.temp = temp;
    }
    
    public Url getUrl() {
        return url;
    }
    
    public void setUrl(Url url) {
        this.url = url;
    }
    
    /**
     * 访问凭证配置
     */
    public static class Access {
        private Key key = new Key();
        
        public Key getKey() {
            return key;
        }
        
        public void setKey(Key key) {
            this.key = key;
        }
        
        public static class Key {
            private String id;
            private String secret;
            
            public String getId() {
                return id;
            }
            
            public void setId(String id) {
                this.id = id;
            }
            
            public String getSecret() {
                return secret;
            }
            
            public void setSecret(String secret) {
                this.secret = secret;
            }
        }
    }
    
    /**
     * 存储桶配置
     */
    public static class Bucket {
        private String name;
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
    }
    
    /**
     * 临时文件配置
     */
    public static class Temp {
        private Expire expire = new Expire();
        
        public Expire getExpire() {
            return expire;
        }
        
        public void setExpire(Expire expire) {
            this.expire = expire;
        }
        
        public static class Expire {
            private int hours = 24;
            
            public int getHours() {
                return hours;
            }
            
            public void setHours(int hours) {
                this.hours = hours;
            }
        }
    }
    
    /**
     * URL配置
     */
    public static class Url {
        private String prefix;
        
        public String getPrefix() {
            return prefix;
        }
        
        public void setPrefix(String prefix) {
            this.prefix = prefix;
        }
    }
}
