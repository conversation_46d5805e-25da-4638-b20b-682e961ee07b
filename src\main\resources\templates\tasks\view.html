<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head(${task.taskName} + ' - 任务详情')}">
    <meta charset="UTF-8">
    <title>任务详情</title>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <script id="customScript" th:inline="javascript">
        /*<![CDATA[*/
        // 这是一个空的脚本块，用于确保布局正确加载
        /*]]>*/
    </script>
    <!-- isForceApprovalNeeded 变量定义已移至 customScript 脚本块顶部 -->
    <div class="content-wrapper">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <div class="d-flex align-items-center">
                <h1 class="h2 mb-0" th:text="${task.taskName}">任务详情</h1>
            </div>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button onclick="handleSmartBack()" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> 返回上一页
                    </button> <!-- 编辑按钮 - 权限检查，已归档项目不能编辑 -->
                    <a th:if="${(#authorization.expression('hasRole(''ADMIN'')') ||
                                (#authorization.expression('hasRole(''MANAGER'')') &&
                                 (#authentication.name == task.responsible ||
                                  (#authentication.name == task.project?.responsible))) ||
                                #authentication.name == task.createdBy ||
                                #authentication.name == '邓利鹏')
                               && (task.project == null || task.project.archive == null || task.project.archive != 1)}"
                        th:href="@{/tasks/{id}/edit(id=${task.taskId})}" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-pencil"></i> 编辑
                    </a> <!-- 提交按钮 - 对任务负责人和管理员可见，经理只能对自己负责的任务操作，已归档项目不能操作，已完成或已暂停的任务不能提交 -->
                    <button
                        th:if="${(#authentication.name == task.responsible ||
                                      #authorization.expression('hasRole(''ADMIN'')') ||
                                      (#authorization.expression('hasRole(''MANAGER'')') && #authentication.name == task.responsible)) &&
                                     task.status != '已完成' && task.status != '已暂停' &&
                                     (task.project == null || task.project.archive == null || task.project.archive != 1)}"
                        type="button" class="btn btn-sm btn-outline-success" th:attr="data-task-id=${task.taskId}"
                        data-bs-toggle="modal" data-bs-target="#completeModal">
                        <i class="bi bi-check-circle"></i> 提交任务
                    </button>
                    <!-- 删除按钮 - 权限检查，已归档项目不能删除 -->
                    <button
                        th:if="${(#authorization.expression('hasRole(''ADMIN'')') ||
                                    (#authorization.expression('hasRole(''MANAGER'')') &&
                                     (#authentication.name == task.responsible ||
                                      (#authentication.name == task.project?.responsible))) ||
                                    #authentication.name == task.createdBy)
                                   && (task.project == null || task.project.archive == null || task.project.archive != 1)}"
                        type="button" class="btn btn-sm btn-outline-danger" th:attr="data-task-id=${task.taskId}"
                        data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </div>
            </div>
        </div>

        <!-- 消息提示区域 -->
        <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i> <span th:text="${message}">操作成功</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i> <span th:text="${error}">操作失败</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <!-- 任务基本信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">基本信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 35%">任务名称：</th>
                                <td th:text="${task.taskName}">示例任务</td>
                            </tr>
                            <tr>
                                <th>任务ID：</th>
                                <td th:text="${task.taskId}">0</td>
                            </tr>
                            <tr>
                                <th>负责人：</th>
                                <td th:text="${task.responsible}">张三</td>
                            </tr>
                            <tr>
                                <th>状态：</th>
                                <td>
                                    <div th:switch="${task.status}"> <span th:case="'进行中'" class="badge bg-primary"
                                            th:text="${task.status}">进行中</span>
                                        <span th:case="'已完成'" class="badge bg-success"
                                            th:text="${task.status}">已完成</span>
                                        <span th:case="'已暂停'" class="badge bg-dark" th:text="${task.status}">已暂停</span>
                                        <span th:case="'未开始'" class="badge bg-secondary"
                                            th:text="${task.status}">未开始</span>
                                        <span th:case="*" class="badge bg-info" th:text="${task.status}">其他状态</span>
                                    </div>
                                    <!-- 显示审批状态 -->
                                    <div th:if="${task.approvalStatus != null && task.approvalStatus > 0}" class="mt-1">
                                        <span th:class="${'badge ' +
                                            (task.approvalStatus == 1 ? 'bg-warning' :
                                            (task.approvalStatus == 2 ? 'bg-success' :
                                            (task.approvalStatus == 3 ? 'bg-danger' : 'bg-secondary')))}">
                                            <span th:text="${task.approvalStatus == 1 ? '审批中' :
                                                (task.approvalStatus == 2 ? '审批通过' :
                                                (task.approvalStatus == 3 ? '审批拒绝' : '无需审批'))}">
                                                审批状态
                                            </span>
                                        </span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th>风险等级：</th>
                                <td>
                                    <span
                                        th:class="${task.risk == '高' ? 'badge bg-danger' : (task.risk == '中' ? 'badge bg-warning' : 'badge bg-success')}"
                                        th:text="${task.risk}">风险</span>
                                </td>
                            </tr>
                            <tr>
                                <th>任务类型：</th>
                                <td>
                                    <span th:if="${task.type != null && task.type != ''}" class="badge bg-info"
                                        th:text="${task.type}">任务类型</span>
                                    <span th:unless="${task.type != null && task.type != ''}">-</span>
                                </td>
                            </tr>
                            <tr>
                                <th>进度：</th>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar"
                                            th:style="'width: ' + (${task.progressPercentage} ?: 0) + '%'" th:class="${'progress-bar ' +
                                              ((task.progressPercentage ?: 0) <= 30 ? 'bg-danger' :
                                              ((task.progressPercentage ?: 0) <= 70 ? 'bg-warning' : 'bg-success'))}"
                                            th:text="(${task.progressPercentage} ?: 0) + '%'">0%</div>
                                    </div>
                                </td>
                            </tr>

                            <tr>
                                <th>备注：</th>

                                <td th:text="${task.remarks ?: '-'}">备注内容</td>
                            </tr>

                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 35%">所属项目：</th>
                                <td>
                                    <div th:if="${task.projectId == null || task.projectId == 0}" class="alert alert-secondary py-1 mb-0">
                                        <i class="bi bi-dash-circle"></i> 
                                        未关联项目
                                    </div>
                                    <div th:if="${task.projectId != null && task.projectId > 0 && task.project == null}" class="alert alert-warning py-1 mb-0">
                                        <i class="bi bi-exclamation-triangle-fill"></i> 
                                        项目不存在（ID: <span th:text="${task.projectId}"></span>）
                                    </div>
                                    <a th:if="${task.project != null}" 
                                        th:href="@{/projects/{id}(id=${task.projectId})}"
                                        th:text="${task.project.projectName}">项目名称</a>
                                </td>
                            </tr>
                            <tr>
                                <th>创建时间：</th>
                                <td th:text="${task.createdDate}">2023-01-01 12:00</td>
                            </tr>
                            <tr>
                                <th>上次评论时间：</th>
                                <td th:text="${task.lastCommentDate != null ? task.lastCommentDate : '-'}">-</td>
                            </tr>

                            <tr>
                                <th>创建者：</th>
                                <td th:text="${task.createdBy ?: '-'}">创建者</td>
                            </tr>
                            <tr>
                                <th>绩效分：</th>
                                <td
                                    th:text="${task.bonus != null ? #numbers.formatDecimal(task.bonus, 1, 2) + ' 元' : '-'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>额定工期（天）：</th>
                                <td
                                    th:text="${task.ratedDurationDays != null ? #numbers.formatDecimal(task.ratedDurationDays, 1, 2) + ' 天' : '-'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>累计工期（天）：</th>
                                <td
                                    th:text="${task.cumulativeDurationDays != null ? #numbers.formatDecimal(task.cumulativeDurationDays, 1, 2) + ' 天' : '-'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>剩余工期（天）：</th>
                                <td th:text="${task.remainingDurationDays != null ? #numbers.formatDecimal(task.remainingDurationDays, 1, 2) + ' 天' : '-'}"
                                    th:style="${task.remainingDurationDays != null && task.remainingDurationDays < 0 ? 'color: red; font-weight: bold;' : ''}">
                                    -</td>
                            </tr>

                            <tr>
                                <th>比例：</th>
                                <td th:text="${task.ratio != null ? #numbers.formatPercent(task.ratio, 1, 2) : '-'}">-
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 时间信息部分 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">时间信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="fw-bold">实际开始时间：</label>
                            <div th:text="${task.actualStartDate ?: '尚未开始'}">-</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="fw-bold">实际结束时间：</label>
                            <div th:text="${task.actualEndDate ?: '尚未完成'}">-</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="fw-bold">工期（天）：</label>
                            <div
                                th:text="${task.durationDays != null ? #numbers.formatDecimal(task.durationDays, 1, 2) + ' 天' : '-'}">
                                -</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务评论列表 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">任务评论列表 (<span th:text="${subTaskPage.totalElements}">0</span>)</h5>
                <a th:if="${task.project == null || task.project.archive == null || task.project.archive != 1}"
                    th:href="@{/subtasks/new(taskId=${task.taskId})}" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus-circle"></i> 添加评论
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 8%">序号</th>
                                <th style="width: 40%">评论内容</th>
                                <th style="width: 12%">创建时间</th>
                                <th style="width: 10%">创建者</th>
                                <th style="width: 10%">状态</th>
                                <th style="width: 10%">实际开始时间</th>
                                <th style="width: 10%">实际结束时间</th>
                                <th style="width: 5%" th:if="${#authorization.expression('hasRole(''ADMIN'')')}">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="subTask : ${subTaskPage.content}">
                                <td th:text="${subTask.sequenceNumber}">1</td>
                                <td>
                                    <div style="white-space: pre-wrap;" th:text="${subTask.logContent}">评论内容</div>
                                </td>
                                <td th:text="${subTask.createdDate}">2023-01-01 12:00:00</td>
                                <td th:text="${subTask.createdBy ?: '-'}">创建者</td>
                                <td>
                                    <div class="d-flex">
                                        <select class="form-select form-select-sm me-2" th:value="${subTask.status}" th:attr="data-subtask-id=${subTask.subTaskId},data-original-status=${subTask.status}" onchange="handleStatusChange(this)">
                                            <option th:value="NOT_STARTED" th:selected="${subTask.status == 'NOT_STARTED'}">未开始</option>
                                            <option th:value="IN_PROGRESS" th:selected="${subTask.status == 'IN_PROGRESS'}">进行中</option>
                                            <option th:value="COMPLETED" th:selected="${subTask.status == 'COMPLETED'}">已完成</option>
                                        </select>
                                        <button class="btn btn-sm btn-primary save-status-btn" style="display:none;" onclick="saveSubTaskStatus(this)">保存</button>
                                    </div>
                                </td>
                                <td th:text="${subTask.actualStartDate ?: '-'}">-</td>
                                <td th:text="${subTask.actualEndDate ?: '-'}">-</td>
                                <td th:if="${#authorization.expression('hasRole(''ADMIN'')')}">
                                    <button th:if="${(task.project == null || task.project.archive == null || task.project.archive != 1)}" type="button" class="btn btn-sm btn-outline-danger" th:attr="data-subtask-id=${subTask.subTaskId}, data-task-id=${task.taskId}" data-bs-toggle="modal" data-bs-target="#deleteSubTaskModal">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr th:if="${subTaskPage.totalElements == 0}">
                                <td colspan="8" class="text-center">暂无评论</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 评论分页 -->
                <div th:if="${subTaskPage.totalPages > 1}" class="d-flex justify-content-center mt-3">
                    <div th:replace="~{fragments/pagination :: pagination(${subTaskPage}, '/tasks/' + ${task.taskId} + '?page=' + ${page} + '&size=' + ${size} + '&submitPage=' + ${submitPage} + '&submitSize=' + ${submitSize})}">
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务提交列表 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">任务提交记录 (<span th:text="${submit2Page.totalElements}">0</span>)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 12%">提交时间</th>
                                <th style="width: 16%">提交名称</th>
                                <th style="width: 24%">备注</th>
                                <th style="width: 12%">提交者</th>
                                <th style="width: 16%">附件</th>
                                <th style="width: 10%">审批流程ID</th>
                                <th style="width: 10%" th:if="${#authorization.expression('hasRole(''ADMIN'')')}">操作
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="submit : ${submit2Page.content}">
                                <td th:text="${submit.submitDate}">2023-01-01 12:00:00</td>
                                <td th:text="${submit.submitName ?: (task.taskName)}">提交名称</td>
                                <td>
                                    <div style="white-space: pre-wrap;" th:text="${submit.remarks}">提交备注</div>
                                </td>
                                <td th:text="${submit.submitter ?: '-'}">提交者</td>
                                <td>
                                    <!-- 检查备注是否包含"审批撤回"或"审批拒绝"，如果包含则只对管理员显示附件 -->
                                    <div th:if="${!(submit.remarks != null && (#strings.contains(submit.remarks, '审批撤回') || #strings.contains(submit.remarks, '审批拒绝'))) || #authorization.expression('hasRole(''ADMIN'')')}" 
                                         class="d-flex flex-column gap-1">
                                        <button th:if="${submit.filePath1 != null && !submit.filePath1.isEmpty()}"
                                            th:onclick="'handleViewClick(this, ' + ${submit.submitId} + ', 1)'"
                                            class="btn btn-sm btn-outline-success d-flex align-items-center">
                                            <i class="bi bi-eye me-1"></i> 在线查看文件1
                                        </button>
                                        <button th:if="${submit.filePath2 != null && !submit.filePath2.isEmpty()}"
                                            th:onclick="'handleViewClick(this, ' + ${submit.submitId} + ', 2)'"
                                            class="btn btn-sm btn-outline-success d-flex align-items-center">
                                            <i class="bi bi-eye me-1"></i> 在线查看文件2
                                        </button>
                                        <button th:if="${submit.filePath3 != null && !submit.filePath3.isEmpty()}"
                                            th:onclick="'handleViewClick(this, ' + ${submit.submitId} + ', 3)'"
                                            class="btn btn-sm btn-outline-success d-flex align-items-center">
                                            <i class="bi bi-eye me-1"></i> 在线查看文件3
                                        </button>
                                        <button th:if="${submit.filePath4 != null && !submit.filePath4.isEmpty()}"
                                            th:onclick="'handleViewClick(this, ' + ${submit.submitId} + ', 4)'"
                                            class="btn btn-sm btn-outline-success d-flex align-items-center">
                                            <i class="bi bi-eye me-1"></i> 在线查看文件4
                                        </button>
                                        <!-- 添加下载选项 - 仅管理员可见 -->
                                        <div th:if="${#authorization.expression('hasRole(''ADMIN'')') && (submit.filePath1 != null && !submit.filePath1.isEmpty() || submit.filePath2 != null && !submit.filePath2.isEmpty() || submit.filePath3 != null && !submit.filePath3.isEmpty() || submit.filePath4 != null && !submit.filePath4.isEmpty())}"
                                             class="mt-1">
                                            <small class="text-muted">需要下载到指定目录？</small>
                                            <div class="d-flex gap-1 mt-1">
                                                <button th:if="${submit.filePath1 != null && !submit.filePath1.isEmpty()}"
                                                    th:onclick="'handleDownloadClick(this, ' + ${submit.submitId} + ', 1)'"
                                                    class="btn btn-xs btn-outline-secondary"
                                                    title="点击选择保存目录和文件名">
                                                    <i class="bi bi-folder-plus"></i> 下载1
                                                </button>
                                                <button th:if="${submit.filePath2 != null && !submit.filePath2.isEmpty()}"
                                                    th:onclick="'handleDownloadClick(this, ' + ${submit.submitId} + ', 2)'"
                                                    class="btn btn-xs btn-outline-secondary"
                                                    title="点击选择保存目录和文件名">
                                                    <i class="bi bi-folder-plus"></i> 下载2
                                                </button>
                                                <button th:if="${submit.filePath3 != null && !submit.filePath3.isEmpty()}"
                                                    th:onclick="'handleDownloadClick(this, ' + ${submit.submitId} + ', 3)'"
                                                    class="btn btn-xs btn-outline-secondary"
                                                    title="点击选择保存目录和文件名">
                                                    <i class="bi bi-folder-plus"></i> 下载3
                                                </button>
                                                <button th:if="${submit.filePath4 != null && !submit.filePath4.isEmpty()}"
                                                    th:onclick="'handleDownloadClick(this, ' + ${submit.submitId} + ', 4)'"
                                                    class="btn btn-xs btn-outline-secondary"
                                                    title="点击选择保存目录和文件名">
                                                    <i class="bi bi-folder-plus"></i> 下载4
                                                </button>
                                            </div>
                                        </div>
                                        <!-- 浏览器兼容性提示 -->
                                        <div th:if="${(submit.filePath1 != null && !submit.filePath1.isEmpty()) || (submit.filePath2 != null && !submit.filePath2.isEmpty()) || (submit.filePath3 != null && !submit.filePath3.isEmpty()) || (submit.filePath4 != null && !submit.filePath4.isEmpty())}"
                                             id="browser-compatibility-tip" class="browser-tip" style="display: none;">
                                            <small class="text-muted">
                                                <i class="bi bi-info-circle me-1"></i>
                                                <span id="browser-tip-text">在线查看支持Word、Excel、PPT、PDF等格式</span>
                                            </small>
                                        </div>
                                    </div>
                                    <!-- 当备注包含"审批撤回"或"审批拒绝"且用户不是管理员时，显示受限提示 -->
                                    <div th:if="${submit.remarks != null && (#strings.contains(submit.remarks, '审批撤回') || #strings.contains(submit.remarks, '审批拒绝')) && !#authorization.expression('hasRole(''ADMIN'')')}" 
                                         class="text-muted">
                                        <small><i class="bi bi-lock me-1"></i>附件已撤销</small>
                                    </div>
                                </td>
                                <td>
                                    <span th:if="${submit.workflowInstanceId != null}">
                                        <a th:href="@{/workflow/instances/{id}(id=${submit.workflowInstanceId})}"
                                            class="btn btn-sm btn-outline-info d-flex align-items-center">
                                            <i class="bi bi-arrow-right-circle me-1"></i>
                                            <span th:text="${submit.workflowInstanceId}">流程实例ID</span>
                                        </a>
                                    </span>
                                    <span th:unless="${submit.workflowInstanceId != null}" class="text-muted">-</span>
                                </td>
                                <td th:if="${#authorization.expression('hasRole(''ADMIN'')')}">
                                    <button
                                        th:if="${(task.project == null || task.project.archive == null || task.project.archive != 1)}"
                                        type="button" class="btn btn-sm btn-outline-danger" th:attr="data-submit-id=${submit.submitId},
                                                     data-task-id=${task.taskId}" data-bs-toggle="modal"
                                        data-bs-target="#deleteSubmitModal">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr th:if="${submit2Page.totalElements == 0}">
                                <td colspan="${#authorization.expression('hasRole(''ADMIN'')') ? 7 : 6}"
                                    class="text-center">暂无提交记录</td>
                            </tr>
                        </tbody>
                    </table>
                </div> <!-- 提交记录分页 -->
                <div th:if="${submit2Page.totalPages > 1}" class="d-flex justify-content-center mt-3">
                    <div
                        th:replace="~{fragments/pagination :: pagination(${submit2Page}, '/tasks/' + ${task.taskId} + '?submitPage=' + ${submitPage} + '&submitSize=' + ${submitSize} + '&page=' + ${page} + '&size=' + ${size})}">
                    </div>
                </div>
            </div>
        </div>

        <!-- 工期记录列表 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">工期记录列表 (<span th:text="${#lists.size(workHoursList ?: {})}">0</span>)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" style="table-layout: auto;">
                        <thead>
                            <tr>
                                <th style="width: 15%">登记时间</th>
                                <th style="width: 8%">累计工期</th>
                                <th style="width: 8%">工期变化</th>
                                <th style="width: 8%">额定工期</th>
                                <th style="width: 18%">变化原因</th>
                                <th style="width: 8%">责任人</th>
                                <th style="width: 8%">创建人</th>
                                <th style="width: 7%">绩效分</th>
                                <th style="width: 20%">备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="workHours : ${workHoursList}" style="vertical-align: top;">
                                <td th:text="${workHours.createdTime}" style="vertical-align: top;">2023-01-01 12:00:00
                                </td>
                                <td style="vertical-align: top;">
                                    <span class="badge bg-info"
                                        th:text="${workHours.daysActual != null ? (#numbers.formatDecimal(workHours.daysActual, 1, 2) + ' 天') : '-'}">0.00
                                        天</span>
                                </td>
                                <td style="vertical-align: top;">
                                    <span
                                        th:class="${'badge ' + (workHours.daysChange > 0 ? 'bg-success' : (workHours.daysChange < 0 ? 'bg-danger' : 'bg-secondary'))}"
                                        th:text="${workHours.daysChange != null ? ((workHours.daysChange > 0 ? '+' : '') + #numbers.formatDecimal(workHours.daysChange, 1, 2) + ' 天') : '-'}">+1.00
                                        天</span>
                                </td>
                                <td th:text="${workHours.daysRated != null ? (#numbers.formatDecimal(workHours.daysRated, 1, 2) + ' 天') : '-'}"
                                    style="vertical-align: top;">-</td>
                                <td style="vertical-align: top;">
                                    <div style="white-space: pre-wrap;" th:text="${workHours.reason ?: '-'}">变化原因</div>
                                </td>
                                <td th:text="${workHours.responsiblePerson ?: '-'}" style="vertical-align: top;">责任人
                                </td>
                                <td th:text="${workHours.creator ?: '-'}" style="vertical-align: top;">创建人</td>
                                <td style="vertical-align: top;">
                                    <span th:if="${workHours.bonus != null && workHours.bonus > 0}"
                                        class="fw-bold text-success"
                                        th:text="'¥' + ${#numbers.formatDecimal(workHours.bonus, 1, 2)}">¥100.00</span>
                                    <span th:if="${workHours.bonus == null || workHours.bonus == 0}"
                                        class="text-muted">-</span>
                                </td>
                                <td th:text="${workHours.remark ?: '-'}" class="text-break">备注</td>
                            </tr>
                            <tr th:if="${#lists.isEmpty(workHoursList)}">
                                <td colspan="9" class="text-center">暂无工期记录</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 删除确认对话框 -->
        <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        确定要删除这个任务吗？此操作不可恢复。
                    </div>
                    <div class="modal-footer">
                        <form th:action="@{/tasks/delete}" method="post">
                            <input type="hidden" name="taskId" id="deleteTaskId">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-danger">删除</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 删除子任务评论确认对话框 -->
        <div class="modal fade" id="deleteSubTaskModal" tabindex="-1" aria-labelledby="deleteSubTaskModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteSubTaskModalLabel">确认删除</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        确定要删除这条评论吗？此操作不可恢复。
                    </div>
                    <div class="modal-footer">
                        <form th:action="@{/subtasks/delete}" method="post">
                            <input type="hidden" name="subTaskId" id="deleteSubTaskId">
                            <input type="hidden" name="taskId" id="taskIdForSubTask">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-danger">删除</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 删除提交记录确认对话框 -->
        <div th:if="${#authorization.expression('hasRole(''ADMIN'')')}" class="modal fade" id="deleteSubmitModal"
            tabindex="-1" aria-labelledby="deleteSubmitModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteSubmitModalLabel">确认删除</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        确定要删除这条提交记录吗？此操作不可恢复。
                    </div>
                    <div class="modal-footer">
                        <form th:action="@{/submits2/delete}" method="post">
                            <input type="hidden" name="submitId" id="deleteSubmitId">
                            <input type="hidden" name="taskId" id="taskIdForSubmit">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-danger">删除</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提交任务确认对话框 -->
        <div class="modal fade" id="completeModal" tabindex="-1" aria-labelledby="completeModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="completeModalLabel">提交任务</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="task-submit-form" enctype="multipart/form-data" class="needs-validation" novalidate>
                        <div class="modal-body">
                            <input type="hidden" id="taskId" name="taskId" th:value="${task.taskId}">
                            <input type="hidden" id="tempFilePath1" name="tempFilePath1">
                            <input type="hidden" id="tempFilePath2" name="tempFilePath2">
                            <input type="hidden" id="tempFilePath3" name="tempFilePath3">
                            <input type="hidden" id="tempFilePath4" name="tempFilePath4">
                            <input type="hidden" id="fileName1" name="fileName1">
                            <input type="hidden" id="fileName2" name="fileName2">
                            <input type="hidden" id="fileName3" name="fileName3">
                            <input type="hidden" id="fileName4" name="fileName4">

                            <div class="mb-3">
                                <label for="submitName" class="form-label">提交名称</label>
                                <input type="text" class="form-control" id="submitName" name="submitName"
                                    th:value="${task.taskName} ">
                            </div>
                            <div class="mb-3">
                                <label for="remarks" class="form-label">提交备注（同时作为评论自动添加）<span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3"
                                    placeholder="请输入提交备注" required></textarea>
                                <div class="invalid-feedback">请输入提交备注</div>
                            </div>

                            <div class="mb-3">
                                <label for="file1" class="form-label">文件1</label>
                                <input class="form-control" type="file" id="file1" name="file1">
                                <div id="progress-container-1" class="progress mt-2 d-none">
                                    <div id="progress-bar-1" class="progress-bar" role="progressbar" style="width: 0%"
                                        aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="file2" class="form-label">文件2</label>
                                <input class="form-control" type="file" id="file2" name="file2">
                                <div id="progress-container-2" class="progress mt-2 d-none">
                                    <div id="progress-bar-2" class="progress-bar" role="progressbar" style="width: 0%"
                                        aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="file3" class="form-label">文件3</label>
                                <input class="form-control" type="file" id="file3" name="file3">
                                <div id="progress-container-3" class="progress mt-2 d-none">
                                    <div id="progress-bar-3" class="progress-bar" role="progressbar" style="width: 0%"
                                        aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="file4" class="form-label">文件4</label>
                                <input class="form-control" type="file" id="file4" name="file4">
                                <div id="progress-container-4" class="progress mt-2 d-none">
                                    <div id="progress-bar-4" class="progress-bar" role="progressbar" style="width: 0%"
                                        aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                </div>
                            </div>

                            <!-- 表单提交进度条 -->
                            <div id="submit-progress-container" class="progress mt-3 mb-3 d-none">
                                <div id="submit-progress-bar" class="progress-bar bg-info" role="progressbar"
                                    style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">正在提交...
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="taskCompletionStatus" class="form-label">任务完成状态选择</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="taskCompletionStatus"
                                        id="statusCompleted" value="completed" checked>
                                    <label class="form-check-label" for="statusCompleted">
                                        <i class="bi bi-check-circle me-1"></i><strong>完成</strong>
                                    </label>
                                    <small class="form-text text-muted d-block">任务状态将设为"已完成"，进度为100%</small>
                                </div>
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="radio" name="taskCompletionStatus"
                                        id="statusPaused" value="paused">
                                    <label class="form-check-label" for="statusPaused">
                                        <i class="bi bi-pause-circle me-1"></i><strong>暂停</strong>
                                    </label>
                                    <small class="form-text text-muted d-block">任务状态将设为"已暂停"，保持当前进度</small>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="needApproval" name="needApproval">
                                <label class="form-check-label" for="needApproval">是否执行审批流程</label>
                                <small class="form-text text-muted d-block">选择后将在提交后自动跳转到发起流程页面</small>
                            </div>

                            <div class="alert alert-info" id="completedAlert">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                提交任务后，任务状态将变为"已完成"，进度设为100%，并会记录实际结束时间和计算工期。
                            </div>

                            <div class="alert alert-warning d-none" id="pausedAlert">
                                <i class="bi bi-pause-circle-fill me-2"></i>
                                提交任务后，任务状态将变为"已暂停"，保持当前进度，并会记录暂停时间。
                            </div>

                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <strong>提交文件要求：</strong>
                                <ul class="mb-0 mt-1">
                                    <li>"项目评估"任务需要提交《项目需求表》</li>
                                    <li>"器件选型和测试"任务需要提交《方案书》和《规格书》</li>
                                    <li>"BOM制作"任务需要提交《BOM表》</li>
                                    <li>"客户现场培训"任务需要提交被培训人签名的登记表</li>
                                </ul>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" id="submit-task-btn" class="btn btn-success">提交</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 内联样式 -->
    <style>
        .btn-xs {
            padding: 0.125rem 0.25rem;
            font-size: 0.75rem;
            line-height: 1.2;
            border-radius: 0.15rem;
        }
        
        .file-action-section {
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 0.25rem;
            margin-top: 0.5rem;
        }
        
        .file-action-section small {
            font-size: 0.7rem;
        }
        
        .modal-body .d-grid button {
            text-align: left;
        }
        
        .modal-body .d-grid button small {
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        
        /* 文件预览样式 */
        .preview-container {
            background: #f8f9fa;
            border-radius: 0.375rem;
            overflow: hidden;
        }
        
        .preview-loading {
            background: linear-gradient(90deg, #f0f0f0 25%, transparent 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .preview-error {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .preview-success {
            border: 1px solid #d4edda;
        }
        
        /* 响应式图片预览 */
        .image-preview img {
            max-width: 100%;
            max-height: 70vh;
            object-fit: contain;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        /* Office文档预览样式 */
        .office-preview-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: #fff;
        }
        
        /* 模态框大小调整 */
        .modal-xl-custom {
            max-width: 95vw;
            width: 95vw;
        }
        
        .modal-body-preview {
            height: calc(90vh - 120px);
            padding: 0;
            overflow: hidden;
        }
    </style>

    <!-- 自定义脚本 -->
    <script id="customScript" th:inline="javascript">
        // 由后端注入的审批流程强制开关变量，供本脚本块全局使用
        var isForceApprovalNeeded = /*[[${isForceApprovalNeeded}]]*/ false;
        // 当前任务的审批状态
        var taskApprovalStatus = /*[[${task.approvalStatus}]]*/ null;
        function handleSmartBack() {
            // 获取来源页面参数
            const fromPage = new URLSearchParams(window.location.search).get('from');

            if (fromPage === 'dashboard') {
                window.location.href = '/dashboard';
            } else {
                window.history.back();
            }
        }

        // 设置删除任务对话框数据
        document.getElementById('deleteModal').addEventListener('show.bs.modal', function (event) {
            // 检查任务是否处于审批中状态
            if (taskApprovalStatus == 1) {
                // 阻止模态框显示
                event.preventDefault();
                alert('该任务正在审批中，无法删除！请等待审批完成后再操作。');
                return;
            }
            
            const button = event.relatedTarget;
            const taskId = button.getAttribute('data-task-id');
            document.getElementById('deleteTaskId').value = taskId;
        });

        // 设置删除子任务评论对话框数据
        document.getElementById('deleteSubTaskModal').addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const subTaskId = button.getAttribute('data-subtask-id');
            const taskId = button.getAttribute('data-task-id');
            document.getElementById('deleteSubTaskId').value = subTaskId;
            document.getElementById('taskIdForSubTask').value = taskId;
        });

        // 设置删除提交记录对话框数据
        const deleteSubmitModal = document.getElementById('deleteSubmitModal');
        if (deleteSubmitModal) {
            deleteSubmitModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget;
                const submitId = button.getAttribute('data-submit-id');
                const taskId = button.getAttribute('data-task-id');
                document.getElementById('deleteSubmitId').value = submitId;
                document.getElementById('taskIdForSubmit').value = taskId;
            });
        }

        // 检测浏览器兼容性并显示提示的函数
        function checkBrowserCompatibilityAndShowTip() {
            // 检测浏览器是否支持 File API
            if (!window.File || !window.FileReader || !window.FileList || !window.Blob) {
                console.warn('当前浏览器不完全支持 File API');
                return;
            }
            
            // 检测浏览器类型并显示相应提示
            const userAgent = navigator.userAgent.toLowerCase();
            const isChrome = userAgent.includes('chrome') && !userAgent.includes('edge');
            const isFirefox = userAgent.includes('firefox');
            const isEdge = userAgent.includes('edge');
            const isSafari = userAgent.includes('safari') && !userAgent.includes('chrome');
            
            // 获取提示元素
            const tipElements = document.querySelectorAll('#browser-tip-text');
            
            tipElements.forEach(function(tipElement) {
                let tipText = '在线查看支持Word、Excel、PPT、PDF等格式';
                
                if (isChrome) {
                    tipText += '（推荐使用Chrome浏览器以获得最佳体验）';
                } else if (isFirefox) {
                    tipText += '（Firefox浏览器支持良好）';
                } else if (isEdge) {
                    tipText += '（Edge浏览器支持良好）';
                } else if (isSafari) {
                    tipText += '（Safari浏览器可能存在兼容性问题）';
                } else {
                    tipText += '（建议使用Chrome、Firefox或Edge浏览器）';
                }
                
                tipElement.textContent = tipText;
            });
            
            // 显示提示
            const tipContainers = document.querySelectorAll('.browser-tip');
            tipContainers.forEach(function(container) {
                if (container.style.display === 'none') {
                    container.style.display = 'block';
                }
            });
            
            console.log('浏览器兼容性检测完成');
        }

        // 文件上传进度条相关代码
        document.addEventListener('DOMContentLoaded', function () {
            // 检测浏览器兼容性并显示提示
            checkBrowserCompatibilityAndShowTip();

            const submitForm = document.querySelector('#completeModal form');
            const submitBtn = document.getElementById('submit-task-btn');
            const file1Input = document.getElementById('file1');
            const file2Input = document.getElementById('file2');
            const file3Input = document.getElementById('file3');
            const file4Input = document.getElementById('file4');
            const progressContainer1 = document.getElementById('progress-container-1');
            const progressContainer2 = document.getElementById('progress-container-2');
            const progressContainer3 = document.getElementById('progress-container-3');
            const progressContainer4 = document.getElementById('progress-container-4');
            const progressBar1 = document.getElementById('progress-bar-1');
            const progressBar2 = document.getElementById('progress-bar-2');
            const progressBar3 = document.getElementById('progress-bar-3');
            const progressBar4 = document.getElementById('progress-bar-4');

            // 任务状态选择相关
            const statusCompletedRadio = document.getElementById('statusCompleted');
            const statusPausedRadio = document.getElementById('statusPaused');
            const completedAlert = document.getElementById('completedAlert');
            const pausedAlert = document.getElementById('pausedAlert');

            // 监听状态选择变化
            function updateStatusAlert() {
                if (statusCompletedRadio.checked) {
                    completedAlert.classList.remove('d-none');
                    pausedAlert.classList.add('d-none');
                } else if (statusPausedRadio.checked) {
                    completedAlert.classList.add('d-none');
                    pausedAlert.classList.remove('d-none');
                }
            }

            statusCompletedRadio.addEventListener('change', updateStatusAlert);
            statusPausedRadio.addEventListener('change', updateStatusAlert);

            // 文件上传状态跟踪
            let file1Uploaded = true; // 默认为true，因为文件1可能不需要上传
            let file2Uploaded = true; // 默认为true，因为文件2可能不需要上传
            let file3Uploaded = true; // 默认为true，因为文件3可能不需要上传
            let file4Uploaded = true; // 默认为true，因为文件4可能不需要上传

            // 检查是否可以提交
            function checkSubmitEnabled() {
                if (file1Uploaded && file2Uploaded && file3Uploaded && file4Uploaded) {
                    submitBtn.disabled = false;
                } else {
                    submitBtn.disabled = true;
                }
            }            // 监听文件1选择变化
            file1Input.addEventListener('change', function () {
                console.log('文件1选择事件触发，文件数量:', this.files.length);
                
                if (this.files.length > 0) {
                    console.log('开始处理文件1:', this.files[0].name, '大小:', this.files[0].size);
                    
                    file1Uploaded = false;
                    progressContainer1.classList.remove('d-none');
                    checkSubmitEnabled();

                    // 检查文件大小
                    const fileSize = this.files[0].size;
                    const maxSize = 50 * 1024 * 1024; // 50MB

                    if (fileSize > maxSize) {
                        console.error('文件1大小超出限制:', fileSize, '最大允许:', maxSize);
                        progressBar1.classList.add('bg-danger');
                        progressBar1.style.width = '100%';
                        progressBar1.setAttribute('aria-valuenow', 100);
                        progressBar1.textContent = '文件过大';
                        alert('文件大小不能超过50MB！');
                        return;
                    }

                    // 创建虚拟表单数据用于上传
                    const formData = new FormData();
                    formData.append('file', this.files[0]);

                    // 获取CSRF token
                    const token = document.querySelector("meta[name='_csrf']")?.getAttribute("content");
                    const header = document.querySelector("meta[name='_csrf_header']")?.getAttribute("content");
                    
                    console.log('CSRF Token:', token ? '已获取' : '未找到');
                    console.log('CSRF Header:', header ? header : '未找到');

                    // 创建XHR请求
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', '/api/files/upload-temp', true);
                    xhr.setRequestHeader('Accept', 'application/json');
                    
                    // 只有当header和token都存在时才设置请求头
                    if (header && token) {
                        xhr.setRequestHeader(header, token);
                        console.log('已设置CSRF头');
                    } else {
                        console.warn('CSRF头信息不完整，可能导致上传失败');
                    }

                    // 进度监听
                    xhr.upload.addEventListener('progress', function (e) {
                        if (e.lengthComputable) {
                            const percentComplete = Math.round((e.loaded / e.total) * 100);
                            progressBar1.style.width = percentComplete + '%';
                            progressBar1.setAttribute('aria-valuenow', percentComplete);
                            progressBar1.textContent = percentComplete + '%';
                            console.log('文件1上传进度:', percentComplete + '%');
                        }
                    });

                    // 完成监听
                    xhr.addEventListener('load', function (event) {
                        console.log('文件1上传完成事件触发，状态码:', xhr.status);
                        console.log('响应文本:', xhr.responseText);
                        
                        try {
                            if (xhr.status === 200) {
                                const responseText = xhr.responseText;
                                
                                // 强制设置上传成功状态
                                file1Uploaded = true;
                                checkSubmitEnabled();
                                progressBar1.classList.add('bg-success');
                                progressBar1.textContent = '上传完成';
                                console.log('文件1上传成功');

                                try {
                                    const response = JSON.parse(responseText);
                                    console.log('文件1上传响应对象:', response);

                                    // 保存临时文件路径和文件名
                                    if (response.success && response.tempFilePath) {
                                        const tempFilePath = response.tempFilePath.replace(/\\/g, '/');
                                        document.getElementById('tempFilePath1').value = tempFilePath;
                                        document.getElementById('fileName1').value = response.fileName || file1Input.files[0].name;
                                        console.log('已保存文件1临时路径:', tempFilePath);
                                        console.log('已保存文件1文件名:', response.fileName || file1Input.files[0].name);
                                    } else {
                                        console.warn('响应中缺少必要的文件信息:', response);
                                    }
                                } catch (parseError) {
                                    console.warn('解析文件1上传响应JSON时出错，但文件已上传成功:', parseError);
                                    console.log('原始响应文本:', responseText);
                                }
                            } else {
                                console.error('文件1上传失败，状态码:', xhr.status);
                                console.error('响应文本:', xhr.responseText);
                                
                                file1Uploaded = false;
                                checkSubmitEnabled();
                                progressBar1.classList.add('bg-danger');
                                progressBar1.textContent = '上传失败';
                                
                                alert('文件1上传失败，状态码: ' + xhr.status);
                            }
                        } catch (error) {
                            console.error('处理文件1上传响应时出错:', error);
                            
                            file1Uploaded = false;
                            checkSubmitEnabled();
                            progressBar1.classList.add('bg-danger');
                            progressBar1.textContent = '处理失败';
                        }
                    });

                    // 错误监听
                    xhr.addEventListener('error', function (event) {
                        console.error('文件1上传网络错误事件触发', event);
                        file1Uploaded = false;
                        progressBar1.classList.add('bg-danger');
                        progressBar1.textContent = '网络错误';
                        checkSubmitEnabled();
                        alert('文件1上传网络错误');
                    });

                    // 实际发送请求
                    try {
                        console.log('开始发送文件1上传请求');
                        xhr.send(formData);
                    } catch (error) {
                        console.error('发送文件1上传请求时出错:', error);
                        
                        file1Uploaded = false;
                        checkSubmitEnabled();
                        progressBar1.classList.add('bg-danger');
                        progressBar1.textContent = '发送失败';
                        alert('发送文件1上传请求失败: ' + error.message);
                    }
                } else {
                    console.log('文件1选择被清空');
                    file1Uploaded = true;
                    progressContainer1.classList.add('d-none');
                    checkSubmitEnabled();
                }
            });            // 监听文件2选择变化
            file2Input.addEventListener('change', function () {
                if (this.files.length > 0) {
                    file2Uploaded = false;
                    progressContainer2.classList.remove('d-none');
                    checkSubmitEnabled();

                    // 创建虚拟表单数据用于上传
                    const formData = new FormData();
                    formData.append('file', this.files[0]);

                    // 获取CSRF token
                    const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
                    const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

                    // 创建XHR请求
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', '/api/files/upload-temp', true);  // 修正API路径
                    xhr.setRequestHeader('Accept', 'application/json');
                    // 只有当header和token都存在时才设置请求头
                    if (header && token) {
                        xhr.setRequestHeader(header, token);
                    }

                    // 进度监听
                    xhr.upload.addEventListener('progress', function (e) {
                        if (e.lengthComputable) {
                            const percentComplete = Math.round((e.loaded / e.total) * 100);
                            progressBar2.style.width = percentComplete + '%';
                            progressBar2.setAttribute('aria-valuenow', percentComplete);
                            progressBar2.textContent = percentComplete + '%';

                            // 注意：不在这里设置上传完成状态，而是在load事件中处理
                        }
                    });

                    // 保存事件处理程序，以便在模拟上传时可以直接调用
                    xhr._loadHandlers = xhr._loadHandlers || [];

                    // 完成监听
                    const loadHandler = function (event) {
                        console.log('文件2上传完成事件触发');
                        try {
                            // 检查响应状态
                            if (xhr.status === 200) {
                                // 尝试解析响应JSON
                                const responseText = xhr.responseText;
                                console.log('文件2上传响应文本:', responseText);

                                // 强制设置上传成功状态，因为文件已经上传到服务器
                                file2Uploaded = true;
                                checkSubmitEnabled();
                                progressBar2.classList.add('bg-success');
                                console.log('文件2上传成功');

                                try {
                                    const response = JSON.parse(responseText);
                                    console.log('文件2上传响应对象:', response);

                                    // 保存临时文件路径和文件名
                                    if (response.success && response.tempFilePath) {
                                        // 处理路径中的反斜杠，确保在JSON中正确处理
                                        const tempFilePath = response.tempFilePath.replace(/\\/g, '/');
                                        document.getElementById('tempFilePath2').value = tempFilePath;
                                        document.getElementById('fileName2').value = response.fileName || file2Input.files[0].name;
                                        console.log('已保存文件2临时路径:', tempFilePath);
                                        console.log('已保存文件2文件名:', response.fileName || file2Input.files[0].name);
                                    }
                                } catch (parseError) {
                                    console.warn('解析文件2上传响应JSON时出错，但文件已上传成功:', parseError);
                                }
                            } else {
                                // 即使状态不是200，也强制设置上传成功
                                // 因为文件已经上传到服务器
                                file2Uploaded = true;
                                checkSubmitEnabled();
                                progressBar2.classList.add('bg-success');
                                console.log('文件2上传成功，尽管状态码不是200:', xhr.status);
                            }
                        } catch (error) {
                            // 即使出错，也强制设置上传成功
                            // 因为文件已经上传到服务器
                            file2Uploaded = true;
                            checkSubmitEnabled();
                            progressBar2.classList.add('bg-success');
                            console.warn('处理文件2上传响应时出错，但文件已上传成功:', error);
                        }
                    };

                    // 将处理程序添加到数组中
                    xhr._loadHandlers.push(loadHandler);

                    // 将函数作为事件监听器添加
                    xhr.addEventListener('load', loadHandler);

                    // 错误监听
                    xhr.addEventListener('error', function (event) {
                        console.error('文件2上传错误事件触发', event);
                        file2Uploaded = false;
                        progressBar2.classList.add('bg-danger');
                        progressBar2.textContent = '上传失败';
                        checkSubmitEnabled();
                    });

                    // 检查文件大小
                    const fileSize = this.files[0].size;
                    const maxSize = 50 * 1024 * 1024; // 50MB

                    if (fileSize > maxSize) {
                        console.error('文件2大小超出限制:', fileSize, '最大允许:', maxSize);
                        progressBar2.classList.add('bg-danger');
                        progressBar2.style.width = '100%';
                        progressBar2.setAttribute('aria-valuenow', 100);
                        progressBar2.textContent = '文件过大';
                        alert('文件大小不能超过50MB！');
                        return;
                    }

                    // 实际发送请求
                    try {
                        xhr.send(formData);
                    } catch (error) {
                        console.error('发送文件2上传请求时出错:', error);
                        // 如果发送失败，使用模拟上传
                        simulateUploadProgress(xhr);
                    }
                } else {
                    file2Uploaded = true;
                    progressContainer2.classList.add('d-none');
                    checkSubmitEnabled();
                }
            });

            // 监听文件3选择变化
            file3Input.addEventListener('change', function () {
                if (this.files.length > 0) {
                    file3Uploaded = false;
                    progressContainer3.classList.remove('d-none');
                    checkSubmitEnabled();

                    // 创建虚拟表单数据用于上传
                    const formData = new FormData();
                    formData.append('file', this.files[0]);

                    // 获取CSRF token
                    const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
                    const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

                    // 创建XHR请求
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', '/api/files/upload-temp', true);  // 修正API路径
                    xhr.setRequestHeader('Accept', 'application/json');
                    // 只有当header和token都存在时才设置请求头
                    if (header && token) {
                        xhr.setRequestHeader(header, token);
                    }

                    // 进度监听
                    xhr.upload.addEventListener('progress', function (e) {
                        if (e.lengthComputable) {
                            const percentComplete = Math.round((e.loaded / e.total) * 100);
                            progressBar3.style.width = percentComplete + '%';
                            progressBar3.setAttribute('aria-valuenow', percentComplete);
                            progressBar3.textContent = percentComplete + '%';

                            // 注意：不在这里设置上传完成状态，而是在load事件中处理
                        }
                    });

                    // 保存事件处理程序，以便在模拟上传时可以直接调用
                    xhr._loadHandlers = xhr._loadHandlers || [];

                    // 完成监听
                    const loadHandler = function (event) {
                        console.log('文件3上传完成事件触发');
                        try {
                            file3Uploaded = true;
                            checkSubmitEnabled();
                            progressBar3.classList.add('bg-success');
                            progressBar3.textContent = '上传完成';
                            console.log('文件3上传成功');

                            if (xhr.responseText) {
                                try {
                                    const response = JSON.parse(xhr.responseText);
                                    console.log('文件3上传响应:', response);

                                    if (response.tempFilePath) {
                                        // 确保路径使用正斜杠
                                        const tempFilePath = response.tempFilePath.replace(/\\/g, '/');
                                        document.getElementById('tempFilePath3').value = tempFilePath;
                                        document.getElementById('fileName3').value = response.fileName || file3Input.files[0].name;
                                        console.log('已保存文件3临时路径:', tempFilePath);
                                        console.log('已保存文件3文件名:', response.fileName || file3Input.files[0].name);
                                    }
                                } catch (parseError) {
                                    console.warn('解析文件3上传响应JSON时出错，但文件已上传成功:', parseError);
                                }
                            } else {
                                // 即使状态不是200，也强制设置上传成功
                                // 因为文件已经上传到服务器
                                file3Uploaded = true;
                                checkSubmitEnabled();
                                progressBar3.classList.add('bg-success');
                                console.log('文件3上传成功，尽管状态码不是200:', xhr.status);
                            }
                        } catch (error) {
                            // 即使出错，也强制设置上传成功
                            // 因为文件已经上传到服务器
                            file3Uploaded = true;
                            checkSubmitEnabled();
                            progressBar3.classList.add('bg-success');
                            console.warn('处理文件3上传响应时出错，但文件已上传成功:', error);
                        }
                    };

                    // 将处理程序添加到数组中
                    xhr._loadHandlers.push(loadHandler);

                    // 将函数作为事件监听器添加
                    xhr.addEventListener('load', loadHandler);

                    // 错误监听
                    xhr.addEventListener('error', function (event) {
                        console.error('文件3上传错误事件触发', event);
                        file3Uploaded = false;
                        progressBar3.classList.add('bg-danger');
                        progressBar3.textContent = '上传失败';
                        checkSubmitEnabled();
                    });

                    // 检查文件大小
                    const fileSize = this.files[0].size;
                    const maxSize = 50 * 1024 * 1024; // 50MB

                    if (fileSize > maxSize) {
                        console.error('文件3大小超出限制:', fileSize, '最大允许:', maxSize);
                        progressBar3.classList.add('bg-danger');
                        progressBar3.style.width = '100%';
                        progressBar3.setAttribute('aria-valuenow', 100);
                        progressBar3.textContent = '文件过大';
                        alert('文件大小不能超过50MB！');
                        return;
                    }

                    // 实际发送请求
                    try {
                        xhr.send(formData);
                    } catch (error) {
                        console.error('发送文件3上传请求时出错:', error);
                        // 如果发送失败，使用模拟上传
                        simulateUploadProgress(xhr);
                    }
                } else {
                    file3Uploaded = true;
                    progressContainer3.classList.add('d-none');
                    checkSubmitEnabled();
                }
            });

            // 监听文件4选择变化
            file4Input.addEventListener('change', function () {
                if (this.files.length > 0) {
                    file4Uploaded = false;
                    progressContainer4.classList.remove('d-none');
                    checkSubmitEnabled();

                    // 创建虚拟表单数据用于上传
                    const formData = new FormData();
                    formData.append('file', this.files[0]);

                    // 获取CSRF token
                    const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
                    const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

                    // 创建XHR请求
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', '/api/files/upload-temp', true);  // 修正API路径
                    xhr.setRequestHeader('Accept', 'application/json');
                    // 只有当header和token都存在时才设置请求头
                    if (header && token) {
                        xhr.setRequestHeader(header, token);
                    }

                    // 进度监听
                    xhr.upload.addEventListener('progress', function (e) {
                        if (e.lengthComputable) {
                            const percentComplete = Math.round((e.loaded / e.total) * 100);
                            progressBar4.style.width = percentComplete + '%';
                            progressBar4.setAttribute('aria-valuenow', percentComplete);
                            progressBar4.textContent = percentComplete + '%';

                            // 注意：不在这里设置上传完成状态，而是在load事件中处理
                        }
                    });

                    // 保存事件处理程序，以便在模拟上传时可以直接调用
                    xhr._loadHandlers = xhr._loadHandlers || [];

                    // 完成监听
                    const loadHandler = function (event) {
                        console.log('文件4上传完成事件触发');
                        try {
                            file4Uploaded = true;
                            checkSubmitEnabled();
                            progressBar4.classList.add('bg-success');
                            progressBar4.textContent = '上传完成';
                            console.log('文件4上传成功');

                            if (xhr.responseText) {
                                try {
                                    const response = JSON.parse(xhr.responseText);
                                    console.log('文件4上传响应:', response);

                                    if (response.tempFilePath) {
                                        // 确保路径使用正斜杠
                                        const tempFilePath = response.tempFilePath.replace(/\\/g, '/');
                                        document.getElementById('tempFilePath4').value = tempFilePath;
                                        document.getElementById('fileName4').value = response.fileName || file4Input.files[0].name;
                                        console.log('已保存文件4临时路径:', tempFilePath);
                                        console.log('已保存文件4文件名:', response.fileName || file4Input.files[0].name);
                                    }
                                } catch (parseError) {
                                    console.warn('解析文件4上传响应JSON时出错，但文件已上传成功:', parseError);
                                }
                            } else {
                                // 即使状态不是200，也强制设置上传成功
                                // 因为文件已经上传到服务器
                                file4Uploaded = true;
                                checkSubmitEnabled();
                                progressBar4.classList.add('bg-success');
                                console.log('文件4上传成功，尽管状态码不是200:', xhr.status);
                            }
                        } catch (error) {
                            // 即使出错，也强制设置上传成功
                            // 因为文件已经上传到服务器
                            file4Uploaded = true;
                            checkSubmitEnabled();
                            progressBar4.classList.add('bg-success');
                            console.warn('处理文件4上传响应时出错，但文件已上传成功:', error);
                        }
                    };

                    // 将处理程序添加到数组中
                    xhr._loadHandlers.push(loadHandler);

                    // 将函数作为事件监听器添加
                    xhr.addEventListener('load', loadHandler);

                    // 错误监听
                    xhr.addEventListener('error', function (event) {
                        console.error('文件4上传错误事件触发', event);
                        file4Uploaded = false;
                        progressBar4.classList.add('bg-danger');
                        progressBar4.textContent = '上传失败';
                        checkSubmitEnabled();
                    });

                    // 检查文件大小
                    const fileSize = this.files[0].size;
                    const maxSize = 50 * 1024 * 1024; // 50MB

                    if (fileSize > maxSize) {
                        console.error('文件4大小超出限制:', fileSize, '最大允许:', maxSize);
                        progressBar4.classList.add('bg-danger');
                        progressBar4.style.width = '100%';
                        progressBar4.setAttribute('aria-valuenow', 100);
                        progressBar4.textContent = '文件过大';
                        alert('文件大小不能超过50MB！');
                        return;
                    }

                    // 实际发送请求
                    try {
                        xhr.send(formData);
                    } catch (error) {
                        console.error('发送文件4上传请求时出错:', error);
                        // 如果发送失败，使用模拟上传
                        simulateUploadProgress(xhr);
                    }
                } else {
                    file4Uploaded = true;
                    progressContainer4.classList.add('d-none');
                    checkSubmitEnabled();
                }
            });

            // 模拟上传进度
            function simulateUploadProgress(xhr) {
                console.log('开始模拟上传进度');
                let progress = 0;
                const interval = setInterval(function () {
                    // 每次增加 10%，但在 90% 时直接跳到 100%
                    if (progress >= 90) {
                        progress = 100; // 直接跳到 100%
                    } else {
                        progress += 10;
                    }

                    const event = new ProgressEvent('progress', {
                        lengthComputable: true,
                        loaded: progress,
                        total: 100
                    });
                    xhr.upload.dispatchEvent(event);

                    if (progress >= 100) {
                        clearInterval(interval);
                        console.log('模拟上传进度完成，准备模拟响应');
                        // 模拟上传完成后的响应
                        setTimeout(function () {
                            // 设置模拟响应内容
                            xhr.status = 200;
                            xhr.responseText = JSON.stringify({
                                "success": true,
                                "message": "文件上传成功",
                                "tempFilePath": "temp/" + Date.now() + ".tmp",
                                "fileName": "simulated_file.txt",
                                "fileSize": 1024
                            });
                            console.log('模拟响应已准备好，触发load事件');
                            // 手动触发load事件
                            try {
                                // 创建一个新的load事件
                                const loadEvent = document.createEvent('Event');
                                loadEvent.initEvent('load', false, false);

                                // 将xhr设置为事件的目标
                                Object.defineProperty(loadEvent, 'target', { value: xhr });

                                // 触发事件
                                xhr.dispatchEvent(loadEvent);
                                console.log('模拟上传完成，已触发load事件');
                            } catch (error) {
                                console.error('触发load事件时出错:', error);

                                // 如果触发事件失败，则直接调用load事件处理程序
                                try {
                                    const loadHandlers = xhr._loadHandlers || [];
                                    for (const handler of loadHandlers) {
                                        handler.call(xhr, { target: xhr });
                                    }
                                } catch (callError) {
                                    console.error('直接调用load事件处理程序失败:', callError);
                                }
                            }
                        }, 500);
                    }
                }, 300);
            }

            // 监听模态框显示事件，重置表单和进度条
            const completeModal = document.getElementById('completeModal');
            completeModal.addEventListener('show.bs.modal', function (event) {
                // 检查任务是否处于审批中状态
                if (taskApprovalStatus == 1) {
                    // 阻止模态框显示
                    event.preventDefault();
                    alert('该任务正在审批中，无法提交！请等待审批完成后再操作。');
                    return;
                }
                
                // 重置表单
                document.getElementById('task-submit-form').reset();

                // 清空临时文件路径
                document.getElementById('tempFilePath1').value = '';
                document.getElementById('tempFilePath2').value = '';
                document.getElementById('tempFilePath3').value = '';
                document.getElementById('tempFilePath4').value = '';
                document.getElementById('fileName1').value = '';
                document.getElementById('fileName2').value = '';
                document.getElementById('fileName3').value = '';
                document.getElementById('fileName4').value = '';

                // 重置进度条状态
                progressContainer1.classList.add('d-none');
                progressContainer2.classList.add('d-none');
                progressContainer3.classList.add('d-none');
                progressContainer4.classList.add('d-none');
                progressBar1.style.width = '0%';
                progressBar1.setAttribute('aria-valuenow', 0);
                progressBar1.textContent = '0%';
                progressBar1.classList.remove('bg-success', 'bg-danger');
                progressBar2.style.width = '0%';
                progressBar2.setAttribute('aria-valuenow', 0);
                progressBar2.textContent = '0%';
                progressBar2.classList.remove('bg-success', 'bg-danger');
                progressBar3.style.width = '0%';
                progressBar3.setAttribute('aria-valuenow', 0);
                progressBar3.textContent = '0%';
                progressBar3.classList.remove('bg-success', 'bg-danger');
                progressBar4.style.width = '0%';
                progressBar4.setAttribute('aria-valuenow', 0);
                progressBar4.textContent = '0%';
                progressBar4.classList.remove('bg-success', 'bg-danger');

                // 重置上传状态
                file1Uploaded = true;
                file2Uploaded = true;
                file3Uploaded = true;
                file4Uploaded = true;
                submitBtn.disabled = false;

                // 隐藏提交进度条
                const submitProgressContainer = document.getElementById('submit-progress-container');
                submitProgressContainer.classList.add('d-none');

                // 获取提交名称和审批流程开关
                const submitName = document.getElementById('submitName');
                const needApproval = document.getElementById('needApproval');

                // 只在表单打开时检查提交名称，判断是否需要强制选中审批流程
                checkSubmitNameForApproval(submitName.value, needApproval);
            });

            // 根据提交名称检查并设置审批流程开关状态的函数
            function checkSubmitNameForApproval(name, checkbox) {
                if (!name || name.length < 2) return;

                // 获取前两个字符
                const prefix = name.substring(0, 2);

                // 检查前缀是否匹配
                // if (prefix === "01" || prefix === "02" || prefix === "03" || 
                //     prefix === "07" || prefix === "09" || prefix === "11"  || prefix === "12"
                // ) {
                if (isForceApprovalNeeded
                ) {

                    // 选中审批流程开关并禁用
                    checkbox.checked = true;
                    checkbox.disabled = true;
                } else {
                    // 启用审批流程开关（让用户可选择）
                    checkbox.disabled = false;
                }
            }            // 监听提交任务按钮点击事件（模态框内的"提交"按钮）
            submitBtn.addEventListener('click', function () {
                // 调试信息
                console.log('提交按钮被点击，审批状态值:', taskApprovalStatus);
                console.log('审批状态类型:', typeof taskApprovalStatus);
                
                // 检查任务是否处于审批中状态
                if (taskApprovalStatus == 1) {
                    alert('该任务正在审批中，无法提交！请等待审批完成后再提交。');
                    return;
                }
                
                // 表单验证
                const form = document.getElementById('task-submit-form');
                if (!form.checkValidity()) {
                    // 阻止提交并显示验证消息
                    form.classList.add('was-validated');
                    return;
                }

                // 检查是否有文件正在上传
                if (!file1Uploaded || !file2Uploaded || !file3Uploaded || !file4Uploaded) {
                    alert('请等待文件上传完成后再提交！');
                    return;
                }                // 获取表单数据
                const taskId = document.getElementById('taskId').value;
                const submitName = document.getElementById('submitName').value;
                const remarks = document.getElementById('remarks').value;
                const taskCompletionStatus = document.querySelector('input[name="taskCompletionStatus"]:checked').value;

                // 获取临时文件路径
                const tempFilePath1 = document.getElementById('tempFilePath1').value;
                const tempFilePath2 = document.getElementById('tempFilePath2').value;
                const tempFilePath3 = document.getElementById('tempFilePath3').value;
                const tempFilePath4 = document.getElementById('tempFilePath4').value;
                const fileName1 = document.getElementById('fileName1').value;
                const fileName2 = document.getElementById('fileName2').value;
                const fileName3 = document.getElementById('fileName3').value;
                const fileName4 = document.getElementById('fileName4').value;

                // 显示提交进度条
                const submitProgressContainer = document.getElementById('submit-progress-container');
                const submitProgressBar = document.getElementById('submit-progress-bar');
                submitProgressContainer.classList.remove('d-none');
                submitProgressBar.style.width = '50%';
                submitProgressBar.setAttribute('aria-valuenow', 50);
                submitProgressBar.textContent = '正在提交...';

                // 禁用提交按钮
                submitBtn.disabled = true;

                // 获取CSRF token
                const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
                const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

                // 获取是否执行审批流程选项
                const needApproval = document.getElementById('needApproval').checked;

                // 构建请求数据
                const requestData = {
                    taskId: parseInt(taskId), // 确保taskId是数字
                    submitName: submitName,
                    remarks: remarks,
                    needApproval: needApproval,
                    taskCompletionStatus: taskCompletionStatus
                };

                if (tempFilePath1) {
                    requestData.tempFilePath1 = tempFilePath1;
                    requestData.fileName1 = fileName1;
                }
                if (tempFilePath2) {
                    requestData.tempFilePath2 = tempFilePath2;
                    requestData.fileName2 = fileName2;
                }
                if (tempFilePath3) {
                    requestData.tempFilePath3 = tempFilePath3;
                    requestData.fileName3 = fileName3;
                }
                if (tempFilePath4) {
                    requestData.tempFilePath4 = tempFilePath4;
                    requestData.fileName4 = fileName4;
                }

                // 输出请求数据以便调试
                console.log('发送的请求数据:', requestData);
                const jsonData = JSON.stringify(requestData);
                console.log('序列化后的JSON:', jsonData);

                // 使用Fetch API发送请求
                fetch('/api/submits/save-with-temp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        [header]: token
                    },
                    body: jsonData
                })
                    .then(response => {
                        console.log('收到响应:', response);

                        // 更新进度条
                        submitProgressBar.style.width = '100%';
                        submitProgressBar.setAttribute('aria-valuenow', 100);

                        if (response.ok) {
                            // 获取响应的Content-Type
                            const contentType = response.headers.get('Content-Type');
                            console.log('响应Content-Type:', contentType);

                            // 检查是否是JSON格式
                            if (contentType && contentType.includes('application/json')) {
                                return response.json().catch(error => {
                                    console.error('解析JSON时出错:', error);
                                    throw new Error('响应格式错误: ' + error.message);
                                });
                            } else {
                                // 如果不是JSON，则返回文本
                                return response.text().then(text => {
                                    console.log('响应文本:', text);
                                    try {
                                        // 尝试将文本解析为JSON
                                        return JSON.parse(text);
                                    } catch (error) {
                                        console.error('将文本解析为JSON时出错:', error);
                                        // 返回一个简单的对象
                                        return {
                                            success: false,
                                            message: '响应格式错误',
                                            rawText: text
                                        };
                                    }
                                });
                            }
                        } else {
                            throw new Error('响应状态码: ' + response.status);
                        }
                    })
                    .then(data => {
                        console.log('提交响应数据:', data);
                        console.log('提交响应数据类型:', typeof data);
                        console.log('提交响应数据是否有success属性:', data.hasOwnProperty('success'));
                        console.log('提交响应数据的success属性值:', data.success);
                        console.log('提交响应数据的success属性类型:', typeof data.success);

                        // 判断成功条件使用宽松的比较
                        const isSuccess = data.success === true || data.success === 'true' || data.success == 1;
                        console.log('判断提交是否成功:', isSuccess);

                        if (isSuccess) {
                            // 提交成功
                            submitProgressBar.classList.remove('bg-info');
                            submitProgressBar.classList.add('bg-success');
                            submitProgressBar.textContent = '提交成功';

                            // 延时关闭模态框并跳转页面
                            setTimeout(function () {
                                // 关闭模态框
                                const completeModal = bootstrap.Modal.getInstance(document.getElementById('completeModal'));
                                completeModal.hide();

                                // 检查是否需要跳转到审批流程页面
                                if (data.needApproval === true && data.redirectUrl) {
                                    // 跳转到审批流程页面
                                    window.location.href = data.redirectUrl;
                                } else {
                                    // 刷新任务页面
                                    window.location.href = '/tasks/' + taskId + '?success=true';
                                }
                            }, 1000);
                        } else {
                            // 提交失败
                            submitProgressBar.classList.remove('bg-info');
                            submitProgressBar.classList.add('bg-danger');

                            // 获取错误消息
                            let errorMessage = '未知错误';
                            if (data.message) {
                                errorMessage = data.message;
                            } else if (data.rawText) {
                                errorMessage = '原始响应: ' + data.rawText.substring(0, 100) + (data.rawText.length > 100 ? '...' : '');
                            }

                            submitProgressBar.textContent = '提交失败: ' + errorMessage;
                            console.error('提交失败详细信息:', data);

                            // 启用提交按钮
                            submitBtn.disabled = false;
                        }
                    })
                    .catch(error => {
                        console.error('提交请求出错:', error);

                        // 更新进度条
                        submitProgressBar.classList.remove('bg-info');
                        submitProgressBar.classList.add('bg-danger');
                        submitProgressBar.style.width = '100%';
                        submitProgressBar.setAttribute('aria-valuenow', 100);
                        submitProgressBar.textContent = '提交失败: ' + error.message;

                        // 启用提交按钮
                        submitBtn.disabled = false;

                        // 显示详细错误信息
                        alert('提交失败: ' + error.message + '\n\n请检查浏览器控制台以获取更多信息');
                    });
            });
        });

        // 防重复下载的状态管理
        const downloadState = {
            isDownloading: false,
            downloadingFiles: new Set()
        };

        // 显示下载进度模态框
        function showDownloadModal(submitId, fileNumber) {
            // 先获取文件信息用于显示文件名
            fetch(`/submits2/view/${submitId}?file=${fileNumber}`)
                .then(response => response.json())
                .then(fileInfo => {
                    const filename = fileInfo.filename || `文件${fileNumber}`;
                    createDownloadModal(filename);
                })
                .catch(error => {
                    console.error('获取文件信息失败:', error);
                    createDownloadModal(`文件${fileNumber}`);
                });
        }

        // 创建下载模态框
        function createDownloadModal(filename) {
            // 移除已存在的下载模态框
            const existingModal = document.getElementById('downloadProgressModal');
            if (existingModal) {
                existingModal.remove();
            }

            const modalHtml = `
                <div class="modal fade" id="downloadProgressModal" tabindex="-1" aria-labelledby="downloadProgressModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="downloadProgressModalLabel">
                                    <i class="bi bi-download me-2 text-primary"></i>正在下载文件
                                </h5>
                            </div>
                            <div class="modal-body text-center py-4">
                                <div class="mb-3">
                                    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                                <h6 class="mb-2">正在下载：</h6>
                                <p class="text-primary fw-bold mb-3">${filename}</p>
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>
                                    请稍后，文件准备完成后将自动开始下载...
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 显示模态框
            const modalElement = document.getElementById('downloadProgressModal');
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        }

        // 隐藏下载进度模态框
        function hideDownloadModal() {
            const modalElement = document.getElementById('downloadProgressModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
                // 延迟移除DOM元素
                setTimeout(() => {
                    modalElement.remove();
                }, 500);
            }
        }

        // 处理文件下载按钮点击事件
        function handleDownloadClick(button, submitId, fileNumber) {
            console.log('点击下载文件:', submitId, fileNumber);
            
            // 禁用按钮并显示加载状态
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> 下载中...';
            
            try {
                // 先获取文件信息以确定文件名
                fetch(`/submits2/view/${submitId}?file=${fileNumber}`)
                    .then(response => response.json())
                    .then(fileInfo => {
                        const filename = fileInfo.filename || `文件${fileNumber}`;
                        
                        // 构建下载URL
                        const downloadUrl = `/submits2/download/${submitId}?file=${fileNumber}`;
                        
                        console.log('准备下载文件:', filename, '下载URL:', downloadUrl);
                        
                        // 尝试多种下载方式以确保显示另存为对话框
                        attemptDownloadWithSaveAs(downloadUrl, filename);
                        
                        console.log('文件下载链接已触发:', downloadUrl, '建议文件名:', filename);
                        
                        // 显示成功提示
                        setTimeout(function() {
                            button.innerHTML = '<i class="bi bi-check-circle me-1"></i> 已下载';
                            button.classList.remove('btn-outline-secondary');
                            button.classList.add('btn-success');
                            
                            // 2秒后恢复按钮状态
                            setTimeout(function() {
                                button.disabled = false;
                                button.innerHTML = originalText;
                                button.classList.remove('btn-success');
                                button.classList.add('btn-outline-secondary');
                            }, 2000);
                        }, 500);
                    })
                    .catch(error => {
                        console.error('获取文件信息失败:', error);
                        
                        // 如果获取文件信息失败，仍然尝试下载
                        const downloadUrl = `/submits2/download/${submitId}?file=${fileNumber}`;
                        const defaultFilename = `文件${fileNumber}`;
                        
                        console.log('使用默认文件名下载:', defaultFilename);
                        
                        // 尝试多种下载方式以确保显示另存为对话框
                        attemptDownloadWithSaveAs(downloadUrl, defaultFilename);
                        
                        console.log('文件下载链接已触发(使用默认文件名):', downloadUrl);
                        
                        // 显示成功提示
                        setTimeout(function() {
                            button.innerHTML = '<i class="bi bi-check-circle me-1"></i> 已下载';
                            button.classList.remove('btn-outline-secondary');
                            button.classList.add('btn-success');
                            
                            // 2秒后恢复按钮状态
                            setTimeout(function() {
                                button.disabled = false;
                                button.innerHTML = originalText;
                                button.classList.remove('btn-success');
                                button.classList.add('btn-outline-secondary');
                            }, 2000);
                        }, 500);
                    });
                
            } catch (error) {
                console.error('下载文件时出错:', error);
                
                // 显示错误状态
                button.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i> 下载失败';
                button.classList.remove('btn-outline-secondary');
                button.classList.add('btn-danger');
                
                // 2秒后恢复按钮状态
                setTimeout(function() {
                    button.disabled = false;
                    button.innerHTML = originalText;
                    button.classList.remove('btn-danger');
                    button.classList.add('btn-outline-secondary');
                }, 2000);
                
                alert('下载文件时出错: ' + error.message);
            }
        }

        // 处理在线查看按钮点击事件 - 使用文件预览流程
        function handleViewClick(button, submitId, fileNumber) {
            console.log('点击在线查看文件:', submitId, fileNumber);
            
            // 禁用按钮并显示加载状态
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> 正在打开...';

            // 使用新的文件预览流程：获取文件 -> 生成临时URL -> 预览
            previewFileWithTempUrl(submitId, fileNumber)
                .finally(() => {
                    // 恢复按钮状态
                    setTimeout(() => {
                        button.disabled = false;
                        button.innerHTML = originalText;
                    }, 1000);
                });
        }

        // 文件预览流程：获取文件 -> 生成临时URL -> 预览
        async function previewFileWithTempUrl(submitId, fileNumber) {
            try {
                // 第一步：获取文件信息
                console.log('步骤1: 获取文件信息...');
                const fileInfoResponse = await fetch(`/submits2/view/${submitId}?file=${fileNumber}`);
                
                if (!fileInfoResponse.ok) {
                    throw new Error(`获取文件信息失败: ${fileInfoResponse.status}`);
                }
                
                const fileInfo = await fileInfoResponse.json();
                console.log('文件信息:', fileInfo);
                
                const filename = fileInfo.filename || '';
                const fileExtension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
                
                // 第二步：生成临时可访问的URL（使用本地文件服务）
                console.log('步骤2: 生成临时访问URL...');
                const tempUrl = `/submits2/serve/${submitId}?file=${fileNumber}&temp=true&t=${Date.now()}`;
                console.log('临时URL:', tempUrl);
                
                // 第三步：根据文件类型选择预览方式
                console.log('步骤3: 根据文件类型选择预览方式...');
                await previewFileByType(fileExtension, filename, tempUrl, submitId, fileNumber);
                
            } catch (error) {
                console.error('文件预览流程失败:', error);
                
                // 降级处理：直接尝试打开文件
                console.log('降级处理: 直接打开文件...');
                const fallbackUrl = `/submits2/serve/${submitId}?file=${fileNumber}`;
                const newWindow = window.open(fallbackUrl, '_blank');
                if (!newWindow) {
                    alert('文件预览失败，浏览器阻止了弹窗。\n\n错误信息: ' + error.message);
                }
            }
        }

        // 根据文件类型选择预览方式
        async function previewFileByType(fileExtension, filename, tempUrl, submitId, fileNumber) {
            const officeTypes = ['docx', 'doc', 'pptx', 'ppt', 'xlsx', 'xls'];
            const previewableTypes = ['pdf', 'txt', 'csv', 'html', 'xml', 'json'];
            const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'];
            const archiveTypes = ['zip', 'rar', '7z', 'tar', 'gz'];
            
            if (previewableTypes.includes(fileExtension)) {
                // 可直接预览的文件（包括Excel）：直接打开预览模态框
                openDirectPreviewModal(filename, tempUrl, submitId, fileNumber);
            } else if (officeTypes.includes(fileExtension)) {
                // 其他Office文档：显示预览选项
                //showOfficePreviewOptions(filename, tempUrl, submitId, fileNumber);
                openWithOfficeWebViewer(tempUrl, filename);
            } else if (imageTypes.includes(fileExtension)) {
                // 图片文件：显示图片预览
                showImagePreview(filename, tempUrl, submitId, fileNumber);
            } else if (archiveTypes.includes(fileExtension)) {
                // 压缩文件：提示下载到本地解压查看
                const userConfirm = confirm(
                    `文件 "${filename}" 是压缩包文件，不支持在线查看。\n\n` +
                    `文件类型：${fileExtension.toUpperCase()}\n\n` +
                    `压缩包需要下载到本地后解压查看。\n\n` +
                    `是否现在下载此文件？\n\n` +
                    `点击"确定"开始下载，点击"取消"返回。`
                );
                
                if (userConfirm) {
                    // 直接触发下载，无需按钮UI反馈
                    triggerFileDownload(submitId, fileNumber);
                }
            } else {
                // 其他文件类型：提示用户选择
                //showGenericFileOptions(filename, tempUrl, submitId, fileNumber);

                // 提醒用户文件不支持在线查看，询问是否下载
                const userConfirm = confirm(
                    `文件 "${filename}" 不支持在线查看。\n\n` +
                    `文件类型：${fileExtension.toUpperCase()}\n\n` +
                    `是否需要下载此文件到本地查看？\n\n` +
                    `点击"确定"开始下载，点击"取消"返回。`
                );
                
                if (userConfirm) {
                    // 直接触发下载，无需按钮UI反馈
                    triggerFileDownload(submitId, fileNumber);
                }
            }
        }

        // 多种方式尝试下载，确保显示另存为对话框
        function attemptDownloadWithSaveAs(url, filename) {
            console.log('尝试强制显示另存为对话框, URL:', url, 'filename:', filename);
            
            // 方法1: 使用更激进的强制下载方式
            forceDownloadWithSaveAs(url, filename);
        }

        // 强制弹出另存为对话框的方法
        function forceDownloadWithSaveAs(url, filename) {
            console.log('使用强制下载方法:', filename);
            
            // 检测浏览器类型
            const userAgent = navigator.userAgent.toLowerCase();
            const isChrome = userAgent.includes('chrome') && !userAgent.includes('edge');
            const isFirefox = userAgent.includes('firefox');
            const isEdge = userAgent.includes('edge');
            
            // 方法1: 使用File System Access API (Chrome 86+)
            if (window.showSaveFilePicker && isChrome) {
                console.log('尝试使用File System Access API');
                tryFileSystemAccessAPI(url, filename);
                return;
            }
            
            // 方法2: 使用强制的用户交互下载
            console.log('使用强制用户交互下载方法');
            forceUserInteractionDownload(url, filename);
        }

        // 使用File System Access API强制显示另存为对话框
        async function tryFileSystemAccessAPI(url, filename) {
            try {
                // 获取文件内容
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const blob = await response.blob();
                console.log('获取到文件数据，大小:', blob.size, 'bytes');
                
                // 解析文件扩展名
                const fileExtension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
                
                // 设置文件类型
                const mimeTypes = {
                    'zip': 'application/zip',
                    'pdf': 'application/pdf',
                    'doc': 'application/msword',
                    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'xls': 'application/vnd.ms-excel',
                    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'ppt': 'application/vnd.ms-powerpoint',
                    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                    'txt': 'text/plain',
                    'jpg': 'image/jpeg',
                    'png': 'image/png'
                };
                
                const mimeType = mimeTypes[fileExtension] || 'application/octet-stream';
                
                // 使用File System Access API显示保存对话框
                const fileHandle = await window.showSaveFilePicker({
                    suggestedName: filename,
                    types: [{
                        description: `${fileExtension.toUpperCase()}文件`,
                        accept: {
                            [mimeType]: [`.${fileExtension}`]
                        }
                    }]
                });
                
                console.log('用户选择了保存位置，开始写入文件');
                
                // 写入文件
                const writable = await fileHandle.createWritable();
                await writable.write(blob);
                await writable.close();
                
                console.log('文件保存成功:', filename);
                alert(`文件 "${filename}" 已成功保存！`);
                
            } catch (error) {
                console.error('File System Access API失败:', error);
                
                if (error.name === 'AbortError') {
                    console.log('用户取消了保存操作');
                    alert('保存操作已取消');
                } else {
                    console.log('回退到传统下载方法');
                    forceUserInteractionDownload(url, filename);
                }
            }
        }

        // 强制用户交互下载方法
        function forceUserInteractionDownload(url, filename) {
            console.log('使用强制用户交互下载方法');
            
            // 显示自定义保存对话框
            showCustomSaveDialog(url, filename);
        }

        // 显示自定义保存对话框
        function showCustomSaveDialog(url, filename) {
            // 创建自定义保存对话框
            const dialogHtml = `
                <div class="modal fade" id="customSaveDialog" tabindex="-1" aria-labelledby="customSaveDialogLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title" id="customSaveDialogLabel">
                                    <i class="bi bi-download me-2"></i>文件下载
                                </h5>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <div class="display-1 text-primary mb-3">
                                        <i class="bi bi-file-earmark-arrow-down"></i>
                                    </div>
                                    <h4>准备下载文件</h4>
                                    <p class="text-muted mb-0">文件名: <strong>${filename}</strong></p>
                                </div>
                                
                                <div class="alert alert-info mb-3">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>选择保存方式：</strong>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-primary btn-lg" onclick="downloadWithCustomName('${url}', '${filename}')">
                                        <i class="bi bi-pencil-square me-2"></i>自定义文件名并保存
                                    </button>
                                    <button type="button" class="btn btn-success btn-lg" onclick="downloadWithOriginalName('${url}', '${filename}')">
                                        <i class="bi bi-download me-2"></i>使用原文件名直接保存
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="downloadToNewWindow('${url}', '${filename}')">
                                        <i class="bi bi-box-arrow-up-right me-2"></i>在新窗口中打开
                                    </button>
                                </div>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-lightbulb me-1"></i>
                                        提示: 选择"自定义文件名"可以修改保存的文件名和选择保存位置
                                    </small>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" onclick="closeCustomSaveDialog()">
                                    <i class="bi bi-x-circle me-1"></i>取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 移除已存在的对话框
            const existingDialog = document.getElementById('customSaveDialog');
            if (existingDialog) {
                existingDialog.remove();
            }
            
            // 添加新对话框
            document.body.insertAdjacentHTML('beforeend', dialogHtml);
            const modalElement = document.getElementById('customSaveDialog');
            const modal = new bootstrap.Modal(modalElement);
            
            modal.show();
        }

        // 使用自定义文件名下载
        function downloadWithCustomName(url, originalFilename) {
            const customFilename = prompt(`请输入文件名 (包含扩展名):`, originalFilename);
            
            if (customFilename && customFilename.trim() !== '') {
                console.log('用户自定义文件名:', customFilename);
                closeCustomSaveDialog();
                
                // 延迟执行下载，确保模态框已关闭
                setTimeout(() => {
                    performFinalDownload(url, customFilename.trim());
                }, 300);
            } else if (customFilename === '') {
                alert('文件名不能为空！');
            }
            // 如果用户点击取消，不执行任何操作
        }

        // 使用原文件名下载
        function downloadWithOriginalName(url, filename) {
            console.log('使用原文件名下载:', filename);
            closeCustomSaveDialog();
            
            // 延迟执行下载，确保模态框已关闭
            setTimeout(() => {
                performFinalDownload(url, filename);
            }, 300);
        }

        // 在新窗口中打开
        function downloadToNewWindow(url, filename) {
            console.log('在新窗口中打开:', filename);
            closeCustomSaveDialog();
            
            const newWindow = window.open(url, '_blank');
            if (!newWindow) {
                alert('浏览器阻止了弹窗，请允许弹窗后重试。');
            }
        }

        // 关闭自定义保存对话框
        function closeCustomSaveDialog() {
            const modalElement = document.getElementById('customSaveDialog');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
                
                // 延迟移除DOM元素
                setTimeout(() => {
                    modalElement.remove();
                }, 500);
            }
        }

        // 执行最终下载
        function performFinalDownload(url, filename) {
            console.log('执行最终下载:', filename);
            
            // 显示下载进度
            showDownloadProgress();
            
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.blob();
                })
                .then(blob => {
                    console.log('获取到文件blob，开始下载:', blob.size, 'bytes');
                    
                    // 创建blob URL
                    const blobUrl = window.URL.createObjectURL(blob);
                    
                    // 创建下载链接
                    const link = document.createElement('a');
                    link.href = blobUrl;
                    link.download = filename;
                    link.style.display = 'none';
                    
                    // 添加到DOM
                    document.body.appendChild(link);
                    
                    // 立即触发下载
                    link.click();
                    
                    // 清理
                    setTimeout(() => {
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(blobUrl);
                        hideDownloadProgress();
                        
                        // 显示下载完成提示
                        setTimeout(() => {
                            alert(`文件 "${filename}" 下载完成！\n\n请检查浏览器的下载管理器或默认下载文件夹。`);
                        }, 500);
                    }, 1000);
                })
                .catch(error => {
                    console.error('下载失败:', error);
                    hideDownloadProgress();
                    alert('下载失败: ' + error.message);
                });
        }

        // 显示下载进度
        function showDownloadProgress() {
            const progressHtml = `
                <div id="downloadProgressOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0,0,0,0.5); z-index: 10000;">
                    <div class="text-center text-white">
                        <div class="spinner-border text-light mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h4>正在下载文件...</h4>
                        <p>请稍候，文件下载正在进行中</p>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', progressHtml);
        }

        // 隐藏下载进度
        function hideDownloadProgress() {
            const progressOverlay = document.getElementById('downloadProgressOverlay');
            if (progressOverlay) {
                progressOverlay.remove();
            }
        }

        // 显示浏览器下载设置指导
        function showDownloadSettingsGuide() {
            const guideModalHtml = `
                <div class="modal fade" id="downloadSettingsGuideModal" tabindex="-1" aria-labelledby="downloadSettingsGuideLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="downloadSettingsGuideLabel">
                                    <i class="bi bi-gear me-2"></i>浏览器下载设置指导
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    如果想要在下载时选择文件保存位置，需要修改浏览器设置。
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-primary text-white">
                                                <i class="bi bi-browser-chrome me-2"></i>Chrome 浏览器
                                            </div>
                                            <div class="card-body">
                                                <ol class="mb-0">
                                                    <li>点击浏览器右上角 <strong>⋮</strong> (三个点)</li>
                                                    <li>选择 <strong>"设置"</strong></li>
                                                    <li>在左侧菜单点击 <strong>"高级"</strong></li>
                                                    <li>点击 <strong>"下载内容"</strong></li>
                                                    <li>开启 <strong>"每次下载前询问保存位置"</strong></li>
                                                </ol>
                                                <div class="mt-3">
                                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="openChromeSettings()">
                                                        <i class="bi bi-box-arrow-up-right me-1"></i>打开 Chrome 设置
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-info text-white">
                                                <i class="bi bi-browser-edge me-2"></i>Edge 浏览器
                                            </div>
                                            <div class="card-body">
                                                <ol class="mb-0">
                                                    <li>点击浏览器右上角 <strong>⋯</strong> (三个点)</li>
                                                    <li>选择 <strong>"设置"</strong></li>
                                                    <li>在左侧菜单点击 <strong>"下载"</strong></li>
                                                    <li>开启 <strong>"每次下载前询问我该做什么"</strong></li>
                                                </ol>
                                                <div class="mt-3">
                                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="openEdgeSettings()">
                                                        <i class="bi bi-box-arrow-up-right me-1"></i>打开 Edge 设置
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <i class="bi bi-lightbulb me-2"></i>快捷键提示
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6><i class="bi bi-keyboard me-1"></i>查看下载</h6>
                                                    <ul class="mb-0">
                                                        <li><strong>Chrome:</strong> Ctrl + J</li>
                                                        <li><strong>Edge:</strong> Ctrl + Shift + Y</li>
                                                    </ul>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6><i class="bi bi-folder me-1"></i>常见下载位置</h6>
                                                    <ul class="mb-0">
                                                        <li>Windows: C:\\Users\\<USER>\\Downloads</li>
                                                        <li>或检查浏览器设置中的下载位置</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary" onclick="testDownloadSettings()" data-bs-dismiss="modal">
                                    <i class="bi bi-download me-1"></i>测试下载设置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 移除已存在的模态框
            const existingModal = document.getElementById('downloadSettingsGuideModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', guideModalHtml);
            const modalElement = document.getElementById('downloadSettingsGuideModal');
            const modal = new bootstrap.Modal(modalElement);
            
            // 监听模态框关闭事件
            modalElement.addEventListener('hidden.bs.modal', function () {
                modalElement.remove();
            });
            
            modal.show();
        }

        // 打开Chrome设置页面
        function openChromeSettings() {
            try {
                window.open('chrome://settings/downloads', '_blank');
            } catch (error) {
                alert('无法直接打开Chrome设置页面。\n\n请手动打开Chrome设置：\n1. 地址栏输入: chrome://settings/downloads\n2. 或按照上述步骤手动设置');
            }
        }

        // 打开Edge设置页面
        function openEdgeSettings() {
            try {
                window.open('edge://settings/downloads', '_blank');
            } catch (error) {
                alert('无法直接打开Edge设置页面。\n\n请手动打开Edge设置：\n1. 地址栏输入: edge://settings/downloads\n2. 或按照上述步骤手动设置');
            }
        }

        // 测试下载设置
        function testDownloadSettings() {
            // 创建一个小的测试文件进行下载测试
            const testContent = '这是一个测试文件，用于验证浏览器下载设置是否正确。\n\n如果您看到了"另存为"对话框，说明设置成功！\n\n当前时间: ' + new Date().toLocaleString();
            const blob = new Blob([testContent], { type: 'text/plain;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = '下载设置测试文件.txt';
            link.style.display = 'none';
            
            document.body.appendChild(link);
            
            setTimeout(() => {
                link.click();
                
                setTimeout(() => {
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                    
                    setTimeout(() => {
                        const result = confirm('测试下载已触发！\n\n如果出现了"另存为"对话框，说明设置正确。\n如果文件直接下载到默认位置，可能需要调整浏览器设置。\n\n是否需要再次查看设置指导？');
                        if (result) {
                            showDownloadSettingsGuide();
                        }
                    }, 2000);
                }, 100);
            }, 100);
        }

        // 简化的文件下载触发函数（用于在线查看时的下载）
        // 简化的文件下载触发函数（用于在线查看时的下载）
        function triggerFileDownload(submitId, fileNumber) {
            try {
                // 先获取文件信息以确定文件名
                fetch(`/submits2/view/${submitId}?file=${fileNumber}`)
                    .then(response => response.json())
                    .then(fileInfo => {
                        const filename = fileInfo.filename || `文件${fileNumber}`;
                        
                        // 构建下载链接
                        const downloadUrl = `/submits2/download/${submitId}?file=${fileNumber}`;
                        
                        console.log('准备下载文件:', filename, '下载URL:', downloadUrl);
                        
                        // 使用新的下载方法，尝试强制显示另存为对话框
                        attemptDownloadWithSaveAs(downloadUrl, filename);
                        
                        console.log(`开始下载文件 ${fileNumber} from 提交 ${submitId}，建议文件名: ${filename}`);
                    })
                    .catch(error => {
                        console.error('获取文件信息失败，使用默认下载:', error);
                        
                        // 如果获取文件信息失败，仍然尝试下载
                        const downloadUrl = `/submits2/download/${submitId}?file=${fileNumber}`;
                        const defaultFilename = `文件${fileNumber}`;
                        
                        console.log('使用默认文件名下载:', defaultFilename);
                        
                        // 使用新的下载方法，尝试强制显示另存为对话框
                        attemptDownloadWithSaveAs(downloadUrl, defaultFilename);
                        
                        console.log(`开始下载文件 ${fileNumber} from 提交 ${submitId} (使用默认文件名: ${defaultFilename})`);
                    });
                    
            } catch (error) {
                console.error('下载文件时发生错误:', error);
                alert('下载文件失败，请稍后重试。');
            }
        }

        // HTML转义辅助函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 在新窗口打开文件
        function openFileInNewWindow(url, filename) {
            const newWindow = window.open(url, '_blank');
            if (!newWindow) {
                alert('浏览器阻止了弹窗，请允许弹窗后重试。');
            } else {
                console.log('已在新窗口中打开文件:', filename);
            }
        }

        // 直接预览模态框（用于PDF、Excel、文本等可直接预览的文件）
        function openDirectPreviewModal(filename, tempUrl, submitId, fileNumber) {
            const modalHtml = `
                <div class="modal fade" id="directPreviewModal" tabindex="-1" aria-labelledby="directPreviewModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-fullscreen">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="directPreviewModalLabel">
                                    <i class="bi bi-file-earmark me-2"></i>${filename}
                                </h5>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="window.open('${tempUrl}', '_blank')">
                                        <i class="bi bi-box-arrow-up-right me-1"></i>新窗口
                                    </button>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                            </div>
                            <div class="modal-body p-0">
                                <div id="directPreviewContainer" style="height: calc(100vh - 120px);">
                                    <div class="d-flex justify-content-center align-items-center h-100">
                                        <div class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <p class="mt-3 text-muted">正在加载文档预览...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modalElement = document.getElementById('directPreviewModal');
            const modal = new bootstrap.Modal(modalElement);

            // 监听模态框关闭事件
            modalElement.addEventListener('hidden.bs.modal', function () {
                modalElement.remove();
            });

            modal.show();

            // 延迟加载预览内容
            setTimeout(() => {
                loadDirectPreview(tempUrl, filename);
            }, 500);
        }

        // 加载直接预览内容
        function loadDirectPreview(tempUrl, filename) {
            const container = document.getElementById('directPreviewContainer');
            const fileExt = filename.toLowerCase().split('.').pop();
            
            if (['xlsx', 'xls'].includes(fileExt)) {
                // Excel文件：可以选择直接iframe预览或Office Web Viewer
                // 确保URL参数正确格式化
                const embedUrl = tempUrl + (tempUrl.includes('?') ? '&' : '?') + 'mode=embed';
                
                container.innerHTML = `
                    <div class="row h-100 no-gutters">
                        <div class="col-md-8 h-100">
                            <iframe src="${embedUrl}" 
                                    style="width: 100%; height: 100%; border: none;"
                                    title="Excel直接预览"
                                    onload="console.log('Excel直接预览加载成功')"
                                    onerror="console.error('Excel直接预览失败，尝试Office Web Viewer')">
                            </iframe>
                        </div>
                        <div class="col-md-4 border-start bg-light p-3">
                            <h6><i class="bi bi-file-excel me-1"></i>Excel 预览选项</h6>
                            <p class="small text-muted mb-3">如果左侧预览效果不佳，可尝试其他预览方式：</p>
                            
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary btn-sm" onclick="useOfficeWebViewer('${tempUrl}', '${filename}')">
                                    <i class="bi bi-microsoft me-1"></i>Office Web Viewer
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="window.open('${tempUrl}', '_blank')">
                                    <i class="bi bi-box-arrow-up-right me-1"></i>新窗口打开
                                </button>
                                <a href="${tempUrl}${tempUrl.includes('?') ? '&' : '?'}mode=download" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-download me-1"></i>下载文件
                                </a>
                            </div>
                            
                            <div class="mt-3 pt-3 border-top">
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>
                                    Excel文件可能需要专业软件才能获得最佳查看体验
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            } else if (['pdf'].includes(fileExt)) {
                // PDF文件直接预览
                container.innerHTML = `
                    <iframe src="${tempUrl}" 
                            style="width: 100%; height: 100%; border: none;"
                            title="PDF预览">
                    </iframe>
                `;
            } else if (['txt', 'csv', 'html', 'xml', 'json'].includes(fileExt)) {
                // 文本文件预览
                fetch(tempUrl)
                    .then(response => response.text())
                    .then(text => {
                        container.innerHTML = `
                            <div class="p-4 h-100" style="overflow: auto;">
                                <pre style="white-space: pre-wrap; font-family: monospace; background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 0;">${escapeHtml(text)}</pre>
                            </div>
                        `;
                    })
                    .catch(error => {
                        container.innerHTML = `
                            <div class="text-center p-4">
                                <div class="alert alert-warning">
                                    <h6><i class="bi bi-exclamation-triangle me-2"></i>加载失败</h6>
                                    <p>无法加载文本内容: ${error.message}</p>
                                    <a href="${tempUrl}${tempUrl.includes('?') ? '&' : '?'}mode=download" class="btn btn-primary">下载文件</a>
                                </div>
                            </div>
                        `;
                    });
            }
        }

        // 在预览框中使用Office Web Viewer
        function useOfficeWebViewer(tempUrl, filename) {
            const container = document.getElementById('directPreviewContainer');
            const absoluteFileUrl = window.location.origin + tempUrl;
            const encodedFileUrl = encodeURIComponent(absoluteFileUrl);
            // 使用英文locale避免中文语言包加载问题
            const officeWebViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedFileUrl}&ui=en-US`;
            
            console.log('切换到Office Web Viewer:', officeWebViewerUrl);
            console.log('原始文件URL:', absoluteFileUrl);
            
            container.innerHTML = `
                <div class="cpolar-fullscreen-preview" style="width: 100%; height: 100%; display: flex; flex-direction: column;">
                    <iframe src="${officeWebViewerUrl}" 
                            style="width: 100%; height: 100%; border: none; flex: 1;"
                            title="Office Web Viewer全屏预览（云存储）"
                            onload="console.log('Office Web Viewer加载成功')"
                            onerror="console.error('Office Web Viewer加载失败')">
                    </iframe>
                </div>
            `;
        }

        // 显示Office文档预览选项（新版本）
        function showOfficePreviewOptions(filename, tempUrl, submitId, fileNumber) {
            const modalHtml = `
                <div class="modal fade" id="officePreviewModal" tabindex="-1" aria-labelledby="officePreviewModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="officePreviewModalLabel">
                                    <i class="bi bi-file-earmark-text me-2"></i>Office文档预览
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p class="mb-3">文档: <strong>${filename}</strong></p>
                                
                                <div class="alert alert-info mb-3">
                                    <i class="bi bi-lightbulb me-2"></i>
                                    <strong>预览方式说明：</strong><br>
                                    • <strong>云存储预览</strong>：上传到腾讯云COS，通过公网访问<br>
                                    • <strong>cpolar隧道预览</strong>：通过cpolar直接访问本地文件，无需上传
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="openWithOfficeWebViewer('${tempUrl}', '${filename}')">
                                        <i class="bi bi-cloud-upload me-2"></i>
                                        云存储 + Office Web Viewer
                                        <small class="d-block text-light">推荐：上传到云存储后预览</small>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="openWithCpolarPreview('${tempUrl}', '${filename}', '${submitId}', '${fileNumber}')">
                                        <i class="bi bi-hdd-network me-2"></i>
                                        cpolar隧道 + Office Web Viewer
                                        <small class="d-block">直接访问本地文件，需配置cpolar域名</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modalElement = document.getElementById('officePreviewModal');
            const modal = new bootstrap.Modal(modalElement);

            // 监听模态框关闭事件
            modalElement.addEventListener('hidden.bs.modal', function () {
                modalElement.remove();
            });

            modal.show();
        }
            console.log('Office Web Viewer加载成功: ' + filename);
        }

        // Office Web Viewer加载失败处理
        function handleOfficeViewerError(filename, publicUrl) {
            console.error('Office Web Viewer加载失败: ' + filename);
            alert('Office Web Viewer加载失败\\n\\n可能的原因：\\n' +
                  '1. 文件名包含特殊字符或中文\\n' +
                  '2. 腾讯云COS CORS配置不正确\\n' +
                  '3. 文件格式不被Office Web Viewer支持\\n\\n' +
                  '请点击确定后尝试直接访问文件');
            window.open(publicUrl, '_blank');
        }

        // 测试直接访问文件
        function testDirectAccess(fileUrl) {
            window.open(fileUrl, '_blank');
        }

        // 检查CORS设置
        function checkCorsSettings() {
            alert('请检查腾讯云COS存储桶的CORS配置：\\n\\n' +
                  '1. 登录腾讯云控制台\\n' +
                  '2. 进入对象存储COS\\n' +
                  '3. 选择您的存储桶\\n' +
                  '4. 点击"安全管理" → "跨域访问CORS"\\n' +
                  '5. 添加规则：来源Origin: *, 允许方法: GET,HEAD,OPTIONS\\n' +
                  '6. 保存配置并等待生效\\n\\n' +
                  '配置完成后Office Web Viewer应该能正常工作');
        }

        // 使用Office Web Viewer打开文档
        function openWithOfficeWebViewer(tempUrl, filename) {
            try {
                // 关闭预览选项模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('officePreviewModal'));
                if (modal) modal.hide();
                
                // 直接打开全屏Office Web Viewer预览
                openFullscreenOfficeViewer(tempUrl, filename);
                
            } catch (error) {
                console.error('使用Office Web Viewer打开文档失败:', error);
                alert('使用Office Web Viewer失败: ' + error.message + '\\n\\n请尝试下载文件到本地查看。');
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('officePreviewModal'));
                if (modal) modal.hide();
                return;
            }
        }

        // 使用cpolar隧道预览文档
        function openWithCpolarPreview(tempUrl, filename, submitId, fileNumber) {
            try {
                // 关闭预览选项模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('officePreviewModal'));
                if (modal) modal.hide();
                
                // 直接打开全屏cpolar预览
                openFullscreenOfficeViewer(tempUrl, filename, 'cpolar');
                
            } catch (error) {
                console.error('使用cpolar隧道预览失败:', error);
                alert('使用cpolar隧道预览失败: ' + error.message + '\\n\\n请检查cpolar配置或尝试云存储预览。');
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('officePreviewModal'));
                if (modal) modal.hide();
                return;
            }
        }

        // 打开全屏Office Web Viewer
        function openFullscreenOfficeViewer(tempUrl, filename, previewType = 'cloud') {
            // 创建全屏预览容器
            const fullscreenHtml = `
                <div id="fullscreenOfficeViewer" class="position-fixed top-0 start-0 w-100 h-100 bg-white" style="z-index: 9999;">
                    <div class="d-flex flex-column h-100">
                        <!-- 工具栏 -->
                        <div class="bg-light border-bottom px-3 py-2 d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-microsoft me-2 text-primary"></i>
                                <h6 class="mb-0 me-3">Office Web Viewer</h6>
                                <small class="text-muted">${filename}</small>
                                <span class="badge ${previewType === 'cpolar' ? 'bg-success' : 'bg-primary'} ms-2">
                                    ${previewType === 'cpolar' ? 'cpolar隧道' : '云存储'}
                                </span>
                            </div>
                            <div class="d-flex align-items-center gap-2">
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshOfficeViewer()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="showOfficeViewerHelp()">
                                    <i class="bi bi-question-circle me-1"></i>帮助
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="closeFullscreenOfficeViewer()">
                                    <i class="bi bi-x-lg me-1"></i>关闭
                                </button>
                            </div>
                        </div>
                        
                        <!-- 内容区域 -->
                        <div class="flex-grow-1" id="officeViewerContentArea">
                            <div class="d-flex justify-content-center align-items-center h-100">
                                <div class="text-center">
                                    <div class="spinner-border text-primary mb-3" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <h6>正在准备Office Web Viewer</h6>
                                    <p class="text-muted">正在准备文档以支持在线预览...</p>
                                    <small class="text-muted">文档: ${filename}</small>
                                    <br>
                                    <small class="text-muted">预览方式: ${previewType === 'cpolar' ? 'cpolar隧道穿透' : '云存储上传'}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', fullscreenHtml);
            
            // 添加键盘事件监听器（ESC键关闭）
            const escapeHandler = function(event) {
                if (event.key === 'Escape') {
                    closeFullscreenOfficeViewer();
                }
            };
            document.addEventListener('keydown', escapeHandler);
            
            // 存储事件处理器，以便后续移除
            const viewer = document.getElementById('fullscreenOfficeViewer');
            viewer.escapeHandler = escapeHandler;
            
            // 获取内容区域并开始预览流程
            const contentArea = document.getElementById('officeViewerContentArea');
            if (previewType === 'cpolar') {
                generateCpolarPreviewFullscreen(tempUrl, filename, contentArea);
            } else {
                uploadToCloudAndPreviewFullscreen(tempUrl, filename, contentArea);
            }
        }

        // 关闭全屏Office Web Viewer
        function closeFullscreenOfficeViewer() {
            const viewer = document.getElementById('fullscreenOfficeViewer');
            if (viewer) {
                // 移除键盘事件监听器
                if (viewer.escapeHandler) {
                    document.removeEventListener('keydown', viewer.escapeHandler);
                }
                viewer.remove();
            }
        }

        // 刷新Office Viewer
        function refreshOfficeViewer() {
            const iframe = document.querySelector('#fullscreenOfficeViewer iframe');
            if (iframe) {
                iframe.src = iframe.src;
            }
        }

        // 显示Office Viewer帮助
        function showOfficeViewerHelp() {
            showTroubleshootingHelp();
        }

        // 显示故障排除帮助
        function showTroubleshootingHelp() {
            const helpModalHtml = `
                <div class="modal fade" id="troubleshootingHelpModal" tabindex="-1" aria-labelledby="troubleshootingHelpModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="troubleshootingHelpModalLabel">
                                    <i class="bi bi-question-circle me-2"></i>文件预览故障排除
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                            </div>
                            <div class="modal-body">
                                <div class="accordion" id="troubleshootingAccordion">
                                    <!-- 常见问题 -->
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="commonIssues">
                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseCommonIssues" aria-expanded="true">
                                                <i class="bi bi-exclamation-triangle me-2"></i>常见问题
                                            </button>
                                        </h2>
                                        <div id="collapseCommonIssues" class="accordion-collapse collapse show" data-bs-parent="#troubleshootingAccordion">
                                            <div class="accordion-body">
                                                <ul>
                                                    <li><strong>404错误：</strong>检查API端点是否正确，确认后端服务正在运行</li>
                                                    <li><strong>Office Web Viewer加载失败：</strong>确保文件URL可以公网访问</li>
                                                    <li><strong>cpolar隧道失败：</strong>检查cpolar配置和网络连接</li>
                                                    <li><strong>文件预览空白：</strong>尝试刷新页面或使用其他预览方式</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- cpolar配置 -->
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="cpolarConfig">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseCpolarConfig" aria-expanded="false">
                                                <i class="bi bi-hdd-network me-2"></i>cpolar隧道配置
                                            </button>
                                        </h2>
                                        <div id="collapseCpolarConfig" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                            <div class="accordion-body">
                                                <h6>配置步骤：</h6>
                                                <ol>
                                                    <li>安装并启动cpolar</li>
                                                    <li>配置HTTP隧道指向本地应用端口</li>
                                                    <li>在系统配置中设置cpolar域名</li>
                                                    <li>确保防火墙允许cpolar访问</li>
                                                </ol>
                                                <div class="alert alert-info mt-3">
                                                    <i class="bi bi-info-circle me-2"></i>
                                                    如果cpolar预览失败，建议使用云存储预览作为备选方案。
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 浏览器兼容性 -->
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="browserCompatibility">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseBrowserCompatibility" aria-expanded="false">
                                                <i class="bi bi-browser-chrome me-2"></i>浏览器兼容性
                                            </button>
                                        </h2>
                                        <div id="collapseBrowserCompatibility" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                            <div class="accordion-body">
                                                <h6>推荐浏览器：</h6>
                                                <ul>
                                                    <li><strong>Chrome/Edge:</strong> 最佳支持，推荐使用</li>
                                                    <li><strong>Firefox:</strong> 良好支持</li>
                                                    <li><strong>Safari:</strong> 基本支持，部分功能可能受限</li>
                                                </ul>
                                                <div class="alert alert-warning mt-3">
                                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                                    请确保浏览器允许弹窗，并启用JavaScript。
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary" onclick="contactSupport()">联系技术支持</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加模态框到页面
            document.body.insertAdjacentHTML('beforeend', helpModalHtml);
            const modalElement = document.getElementById('troubleshootingHelpModal');
            const modal = new bootstrap.Modal(modalElement);
            
            // 监听模态框关闭事件
            modalElement.addEventListener('hidden.bs.modal', function () {
                modalElement.remove();
            });
            
            modal.show();
        }

        // 联系技术支持
        function contactSupport() {
            alert('技术支持联系方式：\\n\\n' +
                  '邮箱：<EMAIL>\\n' +
                  '电话：400-1234-5678\\n\\n' +
                  '请在联系时描述具体问题和错误信息。');
        }

        // 显示图片预览
        function showImagePreview(filename, tempUrl, submitId, fileNumber) {
            const modalHtml = `
                <div class="modal fade" id="imagePreviewModal" tabindex="-1" aria-labelledby="imagePreviewModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-fullscreen">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="imagePreviewModalLabel">
                                    <i class="bi bi-image me-2"></i>${filename}
                                </h5>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="window.open('${tempUrl}', '_blank')">
                                        <i class="bi bi-box-arrow-up-right me-1"></i>新窗口
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="downloadFile(${submitId}, ${fileNumber})">
                                        <i class="bi bi-download me-1"></i>下载
                                    </button>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                            </div>
                            <div class="modal-body p-0 d-flex justify-content-center align-items-center" style="background-color: #f8f9fa;">
                                <div class="text-center">
                                    <div class="spinner-border text-primary mb-3" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p>正在加载图片...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modalElement = document.getElementById('imagePreviewModal');
            const modal = new bootstrap.Modal(modalElement);

            // 监听模态框关闭事件
            modalElement.addEventListener('hidden.bs.modal', function () {
                modalElement.remove();
            });

            modal.show();

            // 延迟加载图片内容
            setTimeout(() => {
                loadImagePreview(tempUrl, filename);
            }, 500);
        }

        // 加载图片预览内容
        function loadImagePreview(tempUrl, filename) {
            const modalBody = document.querySelector('#imagePreviewModal .modal-body');
            
            // 创建图片元素
            const img = new Image();
            
            img.onload = function() {
                // 图片加载成功
                modalBody.innerHTML = `
                    <img src="${tempUrl}" 
                         alt="${filename}" 
                         style="max-width: 100%; max-height: 100%; object-fit: contain;"
                         class="img-fluid">
                `;
            };
            
            img.onerror = function() {
                // 图片加载失败
                modalBody.innerHTML = `
                    <div class="text-center p-4">
                        <div class="alert alert-warning">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>图片加载失败</h6>
                            <p class="mb-3">无法加载图片文件: ${filename}</p>
                            <div class="d-grid gap-2 d-md-block">
                                <button class="btn btn-primary" onclick="loadImagePreview('${tempUrl}', '${filename}')">
                                    <i class="bi bi-arrow-clockwise me-2"></i>重试
                                </button>
                                <button class="btn btn-outline-secondary" onclick="window.open('${tempUrl}', '_blank')">
                                    <i class="bi bi-box-arrow-up-right me-2"></i>在新窗口打开
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            };
            
            // 开始加载图片
            img.src = tempUrl;
        }

        // 全屏模式下的云存储上传和预览
        async function uploadToCloudAndPreviewFullscreen(localFileUrl, filename, container) {
            try {
                container.innerHTML = `
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">上传中...</span>
                            </div>
                            <h6>正在上传文件到云存储</h6>
                            <p class="text-muted">请稍候，文档正在准备以支持在线预览...</p>
                        </div>
                    </div>
                `;
                
                // 从localFileUrl中提取submitId和fileNumber
                const urlParts = localFileUrl.match(/\/submits2\/serve\/(\d+)\?file=(\d+)/);
                if (!urlParts) {
                    throw new Error('无法解析文件URL');
                }
                
                const submitId = urlParts[1];
                const fileNumber = urlParts[2];
                
                // 调用后端API上传文件到云存储
                const response = await fetch(`/submits2/upload-to-cloud/${submitId}?file=${fileNumber}`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`上传失败: ${response.status} ${response.statusText}`);
                }

                const result = await response.json();
                
                if (result.success && result.publicUrl) {
                    // 使用公网URL创建Office Web Viewer预览
                    const encodedFileUrl = encodeURIComponent(result.publicUrl);
                    // 使用英文locale避免中文语言包加载问题
                    const officeWebViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedFileUrl}&ui=en-US`;
                    
                    console.log('使用公网URL进行Office Web Viewer预览:', officeWebViewerUrl);
                    console.log('原始文件URL:', result.publicUrl);
                    
                    // 更新容器内容为全屏Office Web Viewer
                    container.innerHTML = `
                        <div class="cpolar-fullscreen-preview" style="width: 100%; height: 100%; display: flex; flex-direction: column;">
                            <iframe src="${officeWebViewerUrl}" 
                                    style="width: 100%; height: 100%; border: none; flex: 1;"
                                    title="Office Web Viewer全屏预览（云存储）"
                                    onload="console.log('云存储全屏预览加载完成')"
                                    onerror="console.error('云存储全屏预览加载失败')">
                            </iframe>
                        </div>
                    `;
                } else {
                    throw new Error(result.message || '上传到云存储失败');
                }

            } catch (error) {
                console.error('上传到云存储失败:', error);
                
                // 显示错误信息
                container.innerHTML = `
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="text-center p-4" style="max-width: 600px;">
                            <div class="alert alert-warning">
                                <h6><i class="bi bi-exclamation-triangle me-2"></i>云存储上传失败</h6>
                                <p class="mb-3">无法上传文件到云存储: ${error.message}</p>
                                <p class="mb-3"><small>Office Web Viewer需要公网可访问的文件URL。</small></p>
                                
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="uploadToCloudAndPreview('${localFileUrl}', '${filename}', this.closest('#directPreviewContainer, #officePreviewContainer'))">
                                        <i class="bi bi-arrow-clockwise me-2"></i>重试云存储上传
                                    </button>
                                    <button class="btn btn-outline-success" onclick="generateCpolarPreview('${localFileUrl}', '${filename}', this.closest('#directPreviewContainer, #officePreviewContainer'))">
                                        <i class="bi bi-hdd-network me-2"></i>使用cpolar隧道预览
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="window.open('${localFileUrl}', '_blank')">
                                        <i class="bi bi-browser-chrome me-2"></i>在新窗口打开文件
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="closeFullscreenOfficeViewer()">
                                        <i class="bi bi-x-lg me-2"></i>关闭预览
                                    </button>
                                </div>
                                
                                <div class="mt-3 pt-3 border-top">
                                    <small class="text-muted">
                                        <strong>说明：</strong>由于Office Web Viewer的安全限制，只能预览公网可访问的文件。<br>
                                        请检查云存储配置或使用其他预览方式。
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // 非全屏模式下的云存储上传和预览
        async function uploadToCloudAndPreview(localFileUrl, filename, container) {
            try {
                container.innerHTML = `
                    <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">上传中...</span>
                            </div>
                            <h6>正在上传文件到云存储</h6>
                            <p class="text-muted">请稍候，文档正在准备以支持在线预览...</p>
                        </div>
                    </div>
                `;
                
                // 从localFileUrl中提取submitId和fileNumber
                const urlParts = localFileUrl.match(/\/submits2\/serve\/(\d+)\?file=(\d+)/);
                if (!urlParts) {
                    throw new Error('无法解析文件URL');
                }
                
                const submitId = urlParts[1];
                const fileNumber = urlParts[2];
                
                // 调用后端API上传文件到云存储
                const response = await fetch(`/submits2/upload-to-cloud/${submitId}?file=${fileNumber}`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`上传失败: ${response.status} ${response.statusText}`);
                }

                const result = await response.json();
                
                if (result.success && result.publicUrl) {
                    // 使用公网URL创建Office Web Viewer预览
                    const encodedFileUrl = encodeURIComponent(result.publicUrl);
                    const officeWebViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedFileUrl}&ui=en-US`;
                    
                    console.log('使用公网URL进行Office Web Viewer预览:', officeWebViewerUrl);
                    
                    // 更新容器内容为Office Web Viewer
                    container.innerHTML = `
                        <div class="row h-100 no-gutters">
                            <div class="col-md-8" style="height: 500px;">
                                <iframe src="${officeWebViewerUrl}" 
                                        style="width: 100%; height: 100%; border: none;"
                                        title="Office Web Viewer预览（云存储）"
                                        onload="console.log('云存储预览加载完成')"
                                        onerror="console.error('云存储预览加载失败')">
                                </iframe>
                            </div>
                            <div class="col-md-4 border-start bg-light p-3">
                                <h6><i class="bi bi-cloud-check me-1"></i>云存储预览</h6>
                                <p class="small text-muted mb-3">文件已上传至云存储，通过Office Web Viewer进行预览。</p>
                                
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary btn-sm" onclick="openFullscreenOfficeViewer('${localFileUrl}', '${filename}', 'cloud')">
                                        <i class="bi bi-arrows-fullscreen me-1"></i>全屏预览
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="window.open('${result.publicUrl}', '_blank')">
                                        <i class="bi bi-box-arrow-up-right me-1"></i>在新窗口打开
                                    </button>
                                </div>
                                
                                <div class="mt-3 pt-3 border-top">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        通过腾讯云COS存储提供预览服务，访问速度较快。
                                    </small>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    throw new Error(result.message || '上传到云存储失败');
                }

            } catch (error) {
                console.error('上传到云存储失败:', error);
                
                // 显示错误信息
                container.innerHTML = `
                    <div class="text-center p-4">
                        <div class="alert alert-warning">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>云存储上传失败</h6>
                            <p class="mb-3">无法上传文件到云存储: ${error.message}</p>
                            
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="uploadToCloudAndPreview('${localFileUrl}', '${filename}', this.closest('.alert').parentElement.parentElement)">
                                    <i class="bi bi-arrow-clockwise me-2"></i>重试云存储上传
                                </button>
                                <button class="btn btn-outline-success" onclick="generateCpolarPreview('${localFileUrl}', '${filename}', this.closest('.alert').parentElement.parentElement)">
                                    <i class="bi bi-hdd-network me-2"></i>改用cpolar隧道预览
                                </button>
                                <button class="btn btn-outline-secondary" onclick="debugCpolarPreview('${localFileUrl}', '${filename}')">
                                    <i class="bi bi-bug me-2"></i>调试预览配置
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // 全屏模式下的cpolar隧道预览
        async function generateCpolarPreviewFullscreen(localFileUrl, filename, container) {
            try {
                container.innerHTML = `
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="text-center">
                            <div class="spinner-border text-success mb-3" role="status">
                                <span class="visually-hidden">生成中...</span>
                            </div>
                            <h6>正在生成cpolar隧道预览</h6>
                            <p class="text-muted">请稍候，正在通过cpolar隧道生成公网访问链接...</p>
                        </div>
                    </div>
                `;
                
                // 从localFileUrl中提取submitId和fileNumber
                const urlParts = localFileUrl.match(/\/submits2\/serve\/(\d+)\?file=(\d+)/);
                if (!urlParts) {
                    throw new Error('无法解析文件URL');
                }
                
                const submitId = urlParts[1];
                const fileNumber = urlParts[2];
                
                // 调用后端API生成cpolar预览链接
                const response = await fetch(`/submits2/generate-cpolar-preview/${submitId}?file=${fileNumber}`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`生成预览链接失败: ${response.status} ${response.statusText}`);
                }

                const result = await response.json();
                
                if (result.success && result.publicUrl) {
                    // 使用生成的cpolar预览URL创建Office Web Viewer预览
                    const encodedFileUrl = encodeURIComponent(result.publicUrl);
                    const officeWebViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedFileUrl}&ui=en-US`;
                    
                    console.log('使用cpolar预览URL进行Office Web Viewer预览:', officeWebViewerUrl);
                    console.log('原始预览URL:', result.publicUrl);
                    
                    // 更新容器内容为全屏Office Web Viewer
                    container.innerHTML = `
                        <div class="cpolar-fullscreen-preview" style="width: 100%; height: 100%; display: flex; flex-direction: column;">
                            <iframe src="${officeWebViewerUrl}" 
                                    style="width: 100%; height: 100%; border: none; flex: 1;"
                                    title="Office Web Viewer全屏预览（cpolar）"
                                    onload="console.log('cpolar全屏预览加载完成')"
                                    onerror="console.error('cpolar全屏预览加载失败')">
                            </iframe>
                        </div>
                    `;
                } else {
                    throw new Error(result.message || 'cpolar隧道预览生成失败');
                }

            } catch (error) {
                console.error('cpolar隧道预览失败:', error);
                
                // 获取调试信息
                showCpolarDebugInfo(localFileUrl, error, container);
            }
        }

        // 非全屏模式下的cpolar隧道预览
        async function generateCpolarPreview(localFileUrl, filename, container) {
            try {
                container.innerHTML = `
                    <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
                        <div class="text-center">
                            <div class="spinner-border text-success mb-3" role="status">
                                <span class="visually-hidden">生成中...</span>
                            </div>
                            <h6>正在生成cpolar隧道预览</h6>
                            <p class="text-muted">请稍候，正在通过cpolar隧道生成公网访问链接...</p>
                        </div>
                    </div>
                `;
                
                // 从localFileUrl中提取submitId和fileNumber
                const urlParts = localFileUrl.match(/\/submits2\/serve\/(\d+)\?file=(\d+)/);
                if (!urlParts) {
                    throw new Error('无法解析文件URL');
                }
                
                const submitId = urlParts[1];
                const fileNumber = urlParts[2];
                
                // 调用后端API生成cpolar预览链接
                const response = await fetch(`/submits2/generate-cpolar-preview/${submitId}?file=${fileNumber}`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`生成预览链接失败: ${response.status} ${response.statusText}`);
                }

                const result = await response.json();
                
                if (result.success && result.publicUrl) {
                    // 使用生成的cpolar预览URL创建Office Web Viewer预览
                    const encodedFileUrl = encodeURIComponent(result.publicUrl);
                    const officeWebViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedFileUrl}&ui=en-US`;
                    
                    console.log('使用cpolar预览URL进行Office Web Viewer预览:', officeWebViewerUrl);
                    
                    // 更新容器内容为Office Web Viewer
                    container.innerHTML = `
                        <div class="row h-100 no-gutters">
                            <div class="col-md-8" style="height: 500px;">
                                <iframe src="${officeWebViewerUrl}" 
                                        style="width: 100%; height: 100%; border: none;"
                                        title="Office Web Viewer预览（cpolar）"
                                        onload="console.log('cpolar预览加载完成')"
                                        onerror="console.error('cpolar预览加载失败')">
                                </iframe>
                            </div>
                            <div class="col-md-4 border-start bg-light p-3">
                                <h6><i class="bi bi-hdd-network me-1"></i>cpolar隧道预览</h6>
                                <p class="small text-muted mb-3">通过cpolar隧道直接访问本地文件，无需上传到云存储。</p>
                                
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary btn-sm" onclick="openFullscreenOfficeViewer('${localFileUrl}', '${filename}', 'cpolar')">
                                        <i class="bi bi-arrows-fullscreen me-1"></i>全屏预览
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="window.open('${result.previewUrl}', '_blank')">
                                        <i class="bi bi-box-arrow-up-right me-1"></i>在新窗口打开
                                    </button>
                                </div>
                                
                                <div class="mt-3 pt-3 border-top">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        通过cpolar隧道穿透访问本地文件，预览效果取决于网络状况。
                                    </small>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    throw new Error(result.message || 'cpolar隧道预览生成失败');
                }

            } catch (error) {
                console.error('cpolar隧道预览失败:', error);
                
                // 显示错误信息
                container.innerHTML = `
                    <div class="text-center p-4">
                        <div class="alert alert-warning">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>cpolar隧道预览失败</h6>
                            <p class="mb-3">无法生成cpolar预览链接: ${error.message}</p>
                            
                            <div class="d-grid gap-2">
                                <button class="btn btn-success" onclick="generateCpolarPreview('${localFileUrl}', '${filename}', this.closest('.alert').parentElement.parentElement)">
                                    <i class="bi bi-arrow-clockwise me-2"></i>重试cpolar预览
                                </button>
                                <button class="btn btn-outline-primary" onclick="uploadToCloudAndPreview('${localFileUrl}', '${filename}', this.closest('.alert').parentElement.parentElement)">
                                    <i class="bi bi-cloud-upload me-2"></i>改用云存储预览
                                </button>
                                <button class="btn btn-outline-secondary" onclick="debugCpolarPreview('${localFileUrl}', '${filename}')">
                                    <i class="bi bi-bug me-2"></i>调试cpolar配置
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // cpolar预览调试功能
        async function debugCpolarPreview(localFileUrl, filename) {
            try {
                // 从localFileUrl中提取submitId和fileNumber
                const urlParts = localFileUrl.match(/\/submits2\/serve\/(\d+)\?file=(\d+)/);
                if (!urlParts) {
                    alert('无法解析文件URL: ' + localFileUrl);
                    return;
                }
                
                const submitId = urlParts[1];
                const fileNumber = urlParts[2];
                
                console.log('开始调试cpolar预览:', { submitId, fileNumber, filename });
                
                // 调用调试API
                const response = await fetch(`/api/files/debug-cpolar-preview/${submitId}?file=${fileNumber}`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                const debugInfo = await response.json();
                console.log('cpolar调试信息:', debugInfo);
                
                // 显示调试信息
                const debugModal = `
                    <div class="modal fade" id="debugModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">cpolar预览调试信息</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>配置信息</h6>
                                            <ul class="list-unstyled">
                                                <li><strong>启用状态:</strong> ${debugInfo.config?.enabled ? '是' : '否'}</li>
                                                <li><strong>域名:</strong> ${debugInfo.config?.domain || '未配置'}</li>
                                                <li><strong>Token过期时间:</strong> ${debugInfo.config?.tokenExpireMinutes || 60}分钟</li>
                                                <li><strong>活跃Token数:</strong> ${debugInfo.config?.activeTokens || 0}</li>
                                                <li><strong>服务可用:</strong> ${debugInfo.config?.isEnabled ? '是' : '否'}</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>文件信息</h6>
                                            <ul class="list-unstyled">
                                                <li><strong>用户:</strong> ${debugInfo.user || '未知'}</li>
                                                <li><strong>权限:</strong> ${debugInfo.hasPermission ? '充足' : '不足'}</li>
                                                <li><strong>提交存在:</strong> ${debugInfo.submitExists ? '是' : '否'}</li>
                                                <li><strong>文件路径:</strong> ${debugInfo.filePath || '未找到'}</li>
                                                <li><strong>文件存在:</strong> ${debugInfo.fileExists ? '是' : '否'}</li>
                                                <li><strong>文件大小:</strong> ${debugInfo.fileSize ? (debugInfo.fileSize / 1024).toFixed(2) + ' KB' : '未知'}</li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    ${debugInfo.success ? `
                                        <div class="alert alert-success">
                                            <h6><i class="bi bi-check-circle me-2"></i>生成成功</h6>
                                            <p class="mb-1"><strong>预览URL:</strong></p>
                                            <p class="mb-0"><small>${debugInfo.previewUrl}</small></p>
                                        </div>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-primary" onclick="testGeneratedUrl('${debugInfo.previewUrl}')">
                                                <i class="bi bi-link-45deg me-1"></i>测试生成的URL
                                            </button>
                                        </div>
                                    ` : `
                                        <div class="alert alert-danger">
                                            <h6><i class="bi bi-exclamation-triangle me-2"></i>生成失败</h6>
                                            <p class="mb-0"><strong>错误:</strong> ${debugInfo.message || debugInfo.error}</p>
                                        </div>
                                    `}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                    <button type="button" class="btn btn-outline-primary" onclick="window.open('/cpolar-test.html', '_blank')">
                                        <i class="bi bi-tools me-1"></i>打开测试工具
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加模态框到页面
                document.body.insertAdjacentHTML('beforeend', debugModal);
                const modalElement = document.getElementById('debugModal');
                const modal = new bootstrap.Modal(modalElement);
                
                // 监听模态框关闭事件
                modalElement.addEventListener('hidden.bs.modal', function () {
                    modalElement.remove();
                });
                
                modal.show();
                
            } catch (error) {
                console.error('调试cpolar预览失败:', error);
                alert('调试失败: ' + error.message);
            }
        }
        
        // 测试生成的URL
        async function testGeneratedUrl(url) {
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': '*/*'
                    }
                });
                
                if (response.ok) {
                    alert(`URL测试成功!\\n状态码: ${response.status}\\n内容类型: ${response.headers.get('content-type')}`);
                } else {
                    alert(`URL测试失败!\\n状态码: ${response.status}\\n状态文本: ${response.statusText}`);
                }
            } catch (error) {
                alert(`URL测试出错: ${error.message}`);
            }
        }
        
        // cpolar调试信息显示函数
        async function showCpolarDebugInfo(localFileUrl, error, container) {
            try {
                // 从localFileUrl中提取submitId和fileNumber
                const urlParts = localFileUrl.match(/\/submits2\/serve\/(\d+)\?file=(\d+)/);
                if (!urlParts) {
                    throw new Error('无法解析文件URL');
                }
                
                const submitId = urlParts[1];
                const fileNumber = urlParts[2];
                
                // 获取cpolar配置调试信息
                const debugResponse = await fetch('/api/files/test-cpolar-access');
                const debugData = await debugResponse.json();
                
                // 测试文件访问
                const testResponse = await fetch(`/submits2/generate-cpolar-preview/${submitId}?file=${fileNumber}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const testData = await testResponse.json();
                
                container.innerHTML = `
                    <div class="p-4" style="max-height: 600px; overflow-y: auto;">
                        <div class="alert alert-warning mb-4">
                            <h5><i class="bi bi-exclamation-triangle me-2"></i>cpolar隧道预览失败</h5>
                            <p class="mb-2"><strong>错误信息：</strong>${error.message}</p>
                        </div>
                        
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6><i class="bi bi-gear me-2"></i>cpolar配置状态</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>功能启用：</strong><span class="badge ${debugData.enabled ? 'bg-success' : 'bg-danger'}">${debugData.enabled ? '已启用' : '未启用'}</span></p>
                                        <p><strong>域名配置：</strong><code>${debugData.domain || '未配置'}</code></p>
                                        <p><strong>服务可用：</strong><span class="badge ${debugData.isEnabled ? 'bg-success' : 'bg-danger'}">${debugData.isEnabled ? '可用' : '不可用'}</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>活跃Token：</strong>${debugData.activeTokens}</p>
                                        <p><strong>Token过期时间：</strong>${debugData.tokenExpireMinutes}分钟</p>
                                        <p><strong>测试时间：</strong>${new Date().toLocaleString()}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6><i class="bi bi-file-text me-2"></i>文件访问测试</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>提交ID：</strong>${submitId}</p>
                                        <p><strong>文件编号：</strong>${fileNumber}</p>
                                        <p><strong>文件存在：</strong><span class="badge ${testData.success ? 'bg-success' : 'bg-danger'}">${testData.success ? '是' : '否'}</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        ${testData.filename ? `<p><strong>文件名：</strong>${testData.filename}</p>` : ''}
                                        ${testData.publicUrl ? `<p><strong>生成的URL：</strong><br><small><code>${testData.publicUrl}</code></small></p>` : ''}
                                        ${testData.message ? `<p><strong>消息：</strong>${testData.message}</p>` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        ${testData.publicUrl ? `
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6><i class="bi bi-link me-2"></i>连通性测试</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-3">请点击以下链接测试cpolar隧道的可访问性：</p>
                                <div class="d-grid gap-2 mb-3">
                                    <a href="${testData.publicUrl}" target="_blank" class="btn btn-outline-primary">
                                        <i class="bi bi-box-arrow-up-right me-2"></i>直接访问cpolar链接
                                    </a>
                                    <button class="btn btn-outline-info" onclick="testOfficeWebViewer('${testData.publicUrl}')">
                                        <i class="bi bi-microsoft me-2"></i>测试Office Web Viewer访问
                                    </button>
                                </div>
                                <div id="owv-test-result"></div>
                            </div>
                        </div>
                        ` : ''}
                        
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="bi bi-tools me-2"></i>解决方案</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>配置问题解决：</h6>
                                        <ul class="small">
                                            <li>检查 application.properties 中的 cpolar.preview.enabled=true</li>
                                            <li>确保 cpolar.public.domain 设置为您的实际域名</li>
                                            <li>重启应用使配置生效</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>网络问题解决：</h6>
                                        <ul class="small">
                                            <li>确认cpolar隧道正在运行</li>
                                            <li>测试域名是否可以从外网访问</li>
                                            <li>检查防火墙设置</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-success" onclick="generateCpolarPreviewFullscreen('${localFileUrl}', 'test-file', this.closest('.office-viewer'))">
                                        <i class="bi bi-arrow-clockwise me-2"></i>重新尝试cpolar预览
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="closeFullscreenOfficeViewer()">
                                        <i class="bi bi-x-lg me-2"></i>关闭调试
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            } catch (debugError) {
                console.error('获取调试信息失败:', debugError);
                container.innerHTML = `
                    <div class="p-4">
                        <div class="alert alert-danger">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>cpolar隧道预览失败</h6>
                            <p><strong>原始错误：</strong>${error.message}</p>
                            <p><strong>调试错误：</strong>${debugError.message}</p>
                            <hr>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-secondary" onclick="closeFullscreenOfficeViewer()">
                                    <i class="bi bi-x-lg me-2"></i>关闭
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
        }
        
        // 测试Office Web Viewer访问
        async function testOfficeWebViewer(cpolarUrl) {
            const resultDiv = document.getElementById('owv-test-result');
            
            try {
                resultDiv.innerHTML = `
                    <div class="alert alert-info">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        正在测试Office Web Viewer访问...
                    </div>
                `;
                
                const encodedUrl = encodeURIComponent(cpolarUrl);
                const owvUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}&ui=zh-CN`;
                
                // 创建一个隐藏的iframe来测试访问
                const testFrame = document.createElement('iframe');
                testFrame.style.display = 'none';
                testFrame.src = owvUrl;
                
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('访问超时')), 10000);
                });
                
                const loadPromise = new Promise((resolve, reject) => {
                    testFrame.onload = () => resolve('加载成功');
                    testFrame.onerror = () => reject(new Error('加载失败'));
                });
                
                document.body.appendChild(testFrame);
                
                try {
                    await Promise.race([loadPromise, timeoutPromise]);
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i>Office Web Viewer可以访问cpolar链接
                            <br><small>Office Web Viewer URL: <code>${owvUrl}</code></small>
                        </div>
                    `;
                } catch (testError) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>Office Web Viewer无法访问cpolar链接: ${testError.message}
                            <br><small>这可能是因为Office Web Viewer服务器无法访问您的cpolar域名</small>
                            <br><small>Office Web Viewer URL: <code>${owvUrl}</code></small>
                        </div>
                    `;
                } finally {
                    document.body.removeChild(testFrame);
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-x-circle me-2"></i>测试失败: ${error.message}
                    </div>
                `;
            }
        }
        // 格式化日期时间函数
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            
            try {
                // 处理不同格式的日期时间字符串
                let date;
                if (typeof dateTimeStr === 'string') {
                    // 尝试解析ISO格式的日期时间字符串
                    date = new Date(dateTimeStr);
                } else if (dateTimeStr instanceof Array) {
                    // 处理Java LocalDateTime格式 [year, month, day, hour, minute, second]
                    date = new Date(
                        dateTimeStr[0], // 年
                        dateTimeStr[1] - 1, // 月（JavaScript中月份从0开始）
                        dateTimeStr[2], // 日
                        dateTimeStr[3] || 0, // 时
                        dateTimeStr[4] || 0, // 分
                        dateTimeStr[5] || 0  // 秒
                    );
                } else {
                    return dateTimeStr.toString();
                }
                
                // 检查日期是否有效
                if (isNaN(date.getTime())) {
                    return dateTimeStr.toString();
                }
                
                // 格式化日期时间
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            } catch (e) {
                console.error('日期格式化错误:', e);
                return dateTimeStr.toString();
            }
        }

        // 处理状态选择变化的函数
        function handleStatusChange(selectElement) {
            // 显示保存按钮
            const saveButton = selectElement.parentElement.querySelector('.save-status-btn');
            if (saveButton) {
                saveButton.style.display = 'block';
            }
        }

        // 保存子任务状态的函数
        function saveSubTaskStatus(buttonElement) {
            const selectElement = buttonElement.parentElement.querySelector('select');
            if (!selectElement) {
                console.error('无法找到状态选择框');
                return;
            }

            // 隐藏保存按钮
            buttonElement.style.display = 'none';

            // 调用更新状态的函数
            updateSubTaskStatus(selectElement);
        }

        // 更新子任务状态的函数
        function updateSubTaskStatus(selectElement) {
            const subTaskId = selectElement.getAttribute('data-subtask-id');
            const status = selectElement.value;

            if (!subTaskId || !status) {
                console.error('更新子任务状态失败：缺少必要参数', { subTaskId, status });
                return;
            }

            // 保存原始状态，以便在失败时恢复
            const originalStatus = selectElement.getAttribute('data-original-status') || selectElement.value;

            // 禁用选择框，显示加载状态
            selectElement.disabled = true;

            // 发送AJAX请求更新状态
            fetch(`/subtasks/${subTaskId}/update-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="_csrf"]').getAttribute('content')
                },
                body: `status=${status}`
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`状态更新失败: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // 更新成功，显示成功提示
                    console.log('子任务状态更新成功', data);

                    // 如果状态是已完成，更新实际结束时间显示
                    if (status === 'COMPLETED') {
                        const row = selectElement.closest('tr');
                        const endTimeCell = row.querySelector('td:nth-child(7)');
                        if (endTimeCell && data.actualEndDate) {
                            endTimeCell.textContent = formatDateTime(data.actualEndDate);
                        }
                    }

                    // 如果状态是进行中，更新实际开始时间显示
                    if (status === 'IN_PROGRESS') {
                        const row = selectElement.closest('tr');
                        const startTimeCell = row.querySelector('td:nth-child(6)');
                        if (startTimeCell && data.actualStartDate) {
                            startTimeCell.textContent = formatDateTime(data.actualStartDate);
                        }
                    }

                    // 如果状态是未开始，清除开始和结束时间
                    if (status === 'NOT_STARTED') {
                        const row = selectElement.closest('tr');
                        const startTimeCell = row.querySelector('td:nth-child(6)');
                        const endTimeCell = row.querySelector('td:nth-child(7)');
                        if (startTimeCell) startTimeCell.textContent = '-';
                        if (endTimeCell) endTimeCell.textContent = '-';
                    }
                } else {
                    throw new Error(data.message || '状态更新失败');
                }
            })
            .catch(error => {
                console.error('更新子任务状态时出错:', error);
                alert(`更新状态失败: ${error.message}`);

                // 恢复原来的选择值
                selectElement.value = originalStatus;
            })
            .finally(() => {
                // 重新启用选择框
                selectElement.disabled = false;
                // 更新data-original-status属性为当前值
                selectElement.setAttribute('data-original-status', selectElement.value);
            });
        }

        // 页面加载时初始化所有状态选择框的data-original-status属性
        document.addEventListener('DOMContentLoaded', function() {
            const statusSelects = document.querySelectorAll('select[data-subtask-id]');
            statusSelects.forEach(select => {
                // 设置初始状态
                if (!select.hasAttribute('data-original-status')) {
                    select.setAttribute('data-original-status', select.value);
                }
            });
        });

    </script>
</body>

</html>