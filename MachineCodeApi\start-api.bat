@echo off
echo 正在启动机器码Web API服务...
echo.

cd /d "c:\mylog-web\MachineCodeApi"

echo 检查项目文件...
if not exist "MachineCodeApi.csproj" (
    echo 错误：找不到项目文件 MachineCodeApi.csproj
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo 检查ClassPs.dll文件...
if not exist "ClassPs.dll" (
    echo 警告：找不到 ClassPs.dll 文件
    echo 正在从上级目录复制...
    copy "..\ClassPs.dll" "ClassPs.dll"
)

echo 恢复NuGet包...
dotnet restore

echo 编译项目...
dotnet build

if %ERRORLEVEL% NEQ 0 (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo ========================================
echo 机器码Web API服务正在启动...
echo 服务地址: http://localhost:5000
echo Swagger文档: http://localhost:5000/swagger
echo 健康检查: http://localhost:5000/api/machinecode/health
echo.
echo 按 Ctrl+C 停止服务
echo ========================================
echo.

dotnet run --urls "http://localhost:5000"
