﻿var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 閰嶇疆CORS浠ュ厑璁窲ava搴旂敤璁块棶
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowJavaApp", policy =>
    {
        policy.WithOrigins("http://localhost:8080")
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("AllowJavaApp");
app.UseAuthorization();
app.MapControllers();

app.Run();
