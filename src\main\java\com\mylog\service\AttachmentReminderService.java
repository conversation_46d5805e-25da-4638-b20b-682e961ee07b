package com.mylog.service;

import com.mylog.model.ProjectTask;
import java.util.List;

/**
 * 附件提醒服务接口
 * 用于检查发布项目中最近7天有新附件提交的任务
 */
public interface AttachmentReminderService {
    
    /**
     * 获取需要提醒的任务列表
     * 条件：
     * 1. 任务所属项目名称中包含"发布"
     * 2. 任务最近7天有新附件提交
     * 
     * @return 需要提醒的任务列表
     */
    List<ProjectTask> getTasksWithRecentAttachments();
    
    /**
     * 检查指定任务是否需要提醒
     * 
     * @param task 任务对象
     * @return 是否需要提醒
     */
    boolean shouldRemindTask(ProjectTask task);
}