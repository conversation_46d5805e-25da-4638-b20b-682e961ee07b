﻿<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head('项目管理')}">
    <meta charset="UTF-8">
    <title>项目管理</title>
    <style>
        /* 调整表格单元格的内边距 */
        .table td,
        .table th {
            padding: 0.5rem 0.5rem;
            /* 上下0.5rem，左右0.5rem（原始是0.75rem） */
            white-space: normal;
            /* 允许所有单元格内容换行 */
            word-wrap: break-word;
            /* 长单词也能断行 */
            word-break: break-word;
            /* 更好的断行方式 */
        }

        /* 使所有单元格的内容都能换行显示 */
        .table td {
            max-width: none;
            /* 取消最大宽度限制 */
            overflow: visible;
            /* 显示超出部分 */
        }

        /* 响应式调整：在小屏幕上进一步优化表格显示 */
        @media (max-width: 992px) {
            .table-responsive {
                overflow-x: auto;
                /* 允许水平滚动 */
            }

            .table td,
            .table th {
                min-width: 100px;
                /* 设置最小宽度，避免内容过度挤压 */
            }

            /* 项目名称列给予更多空间 */
            .table td:first-child {
                min-width: 150px;
            }
        }
    </style>
    <script th:inline="javascript">
        // 将人员列表保存为全局变量
        const personnelList = /*[[${personnel}]]*/[];
        console.log('人员列表:', personnelList); // 添加调试日志        // 将视觉类型列表保存为全局变量
        const visionTypeList = /*[[${visionTypes}]]*/[];
        console.log('视觉类型列表:', visionTypeList); // 添加调试日志
        console.log('视觉类型列表长度:', visionTypeList.length); // 检查列表长度
        console.log('视觉类型列表内容详细:', JSON.stringify(visionTypeList)); // 详细检查列表内容

        // 将项目类型列表保存为全局变量
        const projectTypeList = /*[[${projectTypes}]]*/[];
        console.log('项目类型列表:', projectTypeList); // 添加调试日志
    </script>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">项目管理 <small class="fs-6">（<span class="badge bg-primary rounded-pill"
                        th:text="'进行中 ' + ${inProgressProjectCount ?: 0}">进行中 23</span>/<span
                        th:text="${projectPage != null ? projectPage.totalElements : 0}">52</span>）</small></h1>            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="confirmExportToExcel(event)"
                        sec:authorize="hasRole('ADMIN') or #authentication.name == '邓利鹏'">
                        <i class="bi bi-file-earmark-excel"></i> 导出Excel
                    </button>

                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="fixAllTotalCosts()"
                        sec:authorize="hasRole('ADMIN')" id="fixTotalCostsBtn">
                        <i class="bi bi-tools"></i> 修复总成本1和总成本2
                    </button>

                    <a th:href="@{/projects}"
                        th:class="'btn btn-sm ' + (${isArchived} ? 'btn-outline-secondary' : 'btn-secondary')">
                        <i class="bi bi-list-task"></i> 未归档项目
                    </a>
                    <a th:href="@{/projects(archived=true)}"
                        th:class="'btn btn-sm ' + (${isArchived} ? 'btn-secondary' : 'btn-outline-secondary')">
                        <i class="bi bi-archive"></i> 已归档项目
                    </a>
                    <a th:if="${#authorization.expression('hasRole(''ADMIN'')') || #authorization.expression('hasRole(''MANAGER'')')}"
                        th:href="@{/projects/new}" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-plus"></i> 新建项目
                    </a>
                </div>
            </div>
        </div> <!-- 总成本1总额卡片 -->
        <div class="row mb-3" th:if="${projectPage != null && !projectPage.empty}">
            <div class="col-md-4">
                <div class="card bg-warning">
                    <div class="card-body p-3">
                        <h5 class="card-title text-primary mb-2">当前页总成本1总额</h5>
                        <p class="card-text h3"
                            th:text="${totalCost1 != null ? #numbers.formatDecimal(totalCost1, 1, 'COMMA', 2, 'POINT') + ' 元' : '0.00 元'}">
                            0.00 元</p>
                        <p class="card-text" th:if="${costPercentage != null}"
                            th:text="|占总成本 ${#numbers.formatDecimal(costPercentage, 1, 'COMMA', 2, 'POINT')}%|">占总成本
                            0.00%</p>
                    </div>
                </div>
            </div> <!-- 添加所有匹配项目的总成本1总额卡片（仅在高级搜索时显示） -->
            <div class="col-md-4" th:if="${isAdvancedSearch != null && isAdvancedSearch && totalSearchCost1 != null}">
                <div class="card bg-info text-white">
                    <div class="card-body p-3">
                        <h5 class="card-title mb-2">所有匹配项目总成本1总额</h5>
                        <p class="card-text h3"
                            th:text="${totalSearchCost1 != null ? #numbers.formatDecimal(totalSearchCost1, 1, 'COMMA', 2, 'POINT') + ' 元' : '0.00 元'}">
                            0.00 元</p>
                        <p class="card-text" th:if="${searchCostPercentage != null}"
                            th:text="|占总成本 ${#numbers.formatDecimal(searchCostPercentage, 1, 'COMMA', 2, 'POINT')}%|">
                            占总成本 0.00%</p>
                    </div>
                </div>
            </div> <!-- 添加全局项目总成本1总额卡片 - 仅管理员可见 -->
            <div class="col-md-4" sec:authorize="hasRole('ADMIN')">
                <div class="card bg-success text-white">
                    <div class="card-body p-3">
                        <h5 class="card-title mb-2">所有项目总成本1总额</h5>
                        <p class="card-text h3"
                            th:text="${globalTotalCost1 != null ? #numbers.formatDecimal(globalTotalCost1, 1, 'COMMA', 2, 'POINT') + ' 元' : '0.00 元'}">
                            0.00 元</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索栏 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">项目搜索</h5>
                        <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse"
                            data-bs-target="#searchCollapse" aria-expanded="true" aria-controls="searchCollapse">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                    <div class="collapse show" id="searchCollapse">
                        <div class="card-body">
                            <!-- 高级搜索 -->
                            <form th:action="@{/projects/advanced-search}" method="get" class="row g-3"
                                id="advancedSearchForm">
                                <!-- 动态搜索条件 -->
                                <div id="searchConditions">
                                    <div class="search-condition row mb-3">
                                        <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                                            <select class="form-select search-field" onchange="updateValueField(this)">
                                                <option value="">选择字段</option>
                                                <option value="projectName">项目名称</option>
                                                <option value="projectCode">项目编号</option>
                                                <option value="customerName">客户名称</option>
                                                <option value="projectType">项目类型</option>
                                                <option value="visionType">视觉类型</option>
                                                <option value="responsible">负责人</option>
                                                <option value="supervisor1">监理人1</option>
                                                <option value="supervisor2">监理人2</option>
                                                <option value="status">状态</option>
                                                <option value="hasComputerInfo">编码</option>
                                                <option value="inProgressTaskName">进行中任务</option>
                                                <option value="pausedTaskName">暂停中任务</option>
                                                <option value="risk">风险等级</option>
                                                <option value="totalCost1">总成本1</option>
                                                <option value="totalCost2">总成本2</option>
                                                <option value="ratedDurationDays">额定工期</option>
                                                <option value="difficultyCoefficient">难度系数</option>
                                                <option value="cameraQuantity">相机数量</option>
                                                <option value="quantity">设备数量</option>
                                                <option value="salesOrderNumber">销售订单号</option>
                                                <option value="productPartNumber">料号</option>
                                            </select>
                                        </div>
                                        <div class="col-10 col-sm-5 col-md-7 value-container">
                                            <!-- 值输入框将根据选择的字段动态生成 -->
                                            <input type="text" class="form-control search-value" disabled
                                                placeholder="请先选择字段">
                                        </div>
                                        <div class="col-2 col-sm-1 col-md-2">
                                            <button type="button" class="btn btn-outline-danger"
                                                onclick="removeCondition(this)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 按钮组 - 更新为完全响应式布局 -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="d-flex flex-wrap gap-2">
                                            <button type="button" class="btn btn-outline-primary"
                                                onclick="addSearchCondition()">
                                                <i class="bi bi-plus"></i> 添加条件
                                            </button> <button type="button" class="btn btn-outline-primary"
                                                onclick="addTimeCondition()">
                                                <i class="bi bi-calendar"></i> 添加创建时间条件
                                            </button>
                                            <button type="button" class="btn btn-outline-primary"
                                                onclick="addActualEndTimeCondition()">
                                                <i class="bi bi-calendar-check"></i> 添加结束时间条件
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-search"></i> 应用筛选
                                            </button>
                                            <a th:href="@{/projects}" class="btn btn-outline-secondary">
                                                <i class="bi bi-arrow-counterclockwise"></i> 重置
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">项目列表</h5>
                <div class="btn-group">
                    <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse"
                        data-bs-target="#projectListCollapse" aria-expanded="true" aria-controls="projectListCollapse">
                        <i class="bi bi-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="collapse show" id="projectListCollapse">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead>
                                <tr>
                                    <th style="width: 9%">项目名称</th>
                                    <th style="width: 5%">客户名称</th>
                                    <th style="width: 4%">负责人</th>
                                    <th style="width: 4%">监理1</th>
                                    <th style="width: 4%">监理2</th>
                                    <th style="width: 5%">视觉类型</th>
                                    <th style="width: 4%">状态</th>
                                    <th style="width: 4%">编码</th>
                                    <th style="width: 5%">总成本1</th>
                                    <th style="width: 6%">项目额定</th>
                                    <th style="width: 6%">任务额定</th>
                                    <th style="width: 14%">进行中任务</th>
                                    <th style="width: 14%">暂停中任务</th>
                                    <th style="width: 6%">结束时间</th>
                                    <th style="width: 6%">创建日期</th>

                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="project : ${projectPage.content}">
                                    <td>
                                        <a th:href="@{/projects/{id}(id=${project.projectId})}"
                                            th:text="${project.projectName}">示例项目</a>
                                    </td>
                                    <td th:text="${project.customerName}">示例客户</td>
                                    <td th:text="${project.responsible}">张三</td>
                                    <td th:text="${project.supervisor1 ?: '-'}">监理1</td>
                                    <td th:text="${project.supervisor2 ?: '-'}">监理2</td>
                                    <td
                                        th:text="${project.visionType != null ? project.visionType.replace(',', '、') : '-'}">
                                        标准视觉</td>
                                    <td>
                                        <div th:switch="${project.status}">
                                            <span th:case="'进行中'" class="badge bg-primary"
                                                th:text="${project.status}">进行中</span>
                                            <span th:case="'已完成'" class="badge bg-success"
                                                th:text="${project.status}">已完成</span>
                                            <span th:case="'已暂停'" class="badge bg-warning"
                                                th:text="${project.status}">已暂停</span>
                                            <span th:case="'已取消'" class="badge bg-dark"
                                                th:text="${project.status}">已取消</span>
                                            <span th:case="*" class="badge bg-info"
                                                th:text="${project.status}">其他状态</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span th:if="${project.hasComputerInfo == '有'}" class="badge bg-success">有</span>
                                        <span th:if="${project.hasComputerInfo == '无'}" class="badge bg-secondary">无</span>
                                        <span th:if="${project.hasComputerInfo == null or project.hasComputerInfo == ''}" class="badge bg-secondary">无</span>
                                    </td>
                                    <td
                                        th:text="${project.totalCost1 != null ? #numbers.formatDecimal(project.totalCost1, 1, 'COMMA', 2, 'POINT') + ' 元' : '-'}">
                                        -</td>

                                    <td
                                        th:text="${project.ratedDurationDays != null ? #numbers.formatDecimal(project.ratedDurationDays, 1, 2) + ' 天' : '-'}">
                                        -</td>
                                    <td th:style="${project.taskRatedDurationTotal == null || project.ratedDurationDays == null || 
                                        #numbers.formatDecimal(project.taskRatedDurationTotal, 1, 2) != #numbers.formatDecimal(project.ratedDurationDays, 1, 2) ? 
                                        'color: red; font-weight: bold;' : 'color: black;'}"
                                        th:text="${project.taskRatedDurationTotal != null ? #numbers.formatDecimal(project.taskRatedDurationTotal, 1, 2) + ' 天' : '-'}">
                                        -</td>
                                    <td th:text="${project.note ?: '-'}">-</td>
                                    <td th:text="${project.pausedTaskNames ?: '-'}">-</td>
                                    <td
                                        th:text="${project.actualEndDate != null and #strings.length(project.actualEndDate.toString()) >= 10 ? #strings.substring(project.actualEndDate.toString(), 0, 10) : '-'}">
                                        2025-05-23</td>
                                    <td
                                        th:text="${project.createdDate != null and #strings.length(project.createdDate.toString()) >= 10 ? #strings.substring(project.createdDate.toString(), 0, 10) : '-'}">
                                        2025-05-23</td>
                                </tr>
                                <tr th:if="${projectPage.empty}">
                                    <td colspan="13" class="text-center">暂无项目</td>
                                </tr><!-- 添加总成本1总额行 -->
                                <tr th:if="${!projectPage.empty}" class="table-info">
                                    <td colspan="6" class="text-end fw-bold">当前页总成本1总额：</td>
                                    <td class="fw-bold"
                                        th:text="${totalCost1 != null ? #numbers.formatDecimal(totalCost1, 1, 'COMMA', 2, 'POINT') + ' 元 (' + #numbers.formatDecimal(costPercentage, 1, 'COMMA', 2, 'POINT') + '%)' : '0.00 元 (0.00%)'}">
                                        0.00 元 (0.00%)</td>
                                    <td colspan="5"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div> <!-- 分页控件 -->
                <div class="card-footer" th:if="${projectPage != null && projectPage.totalPages > 0}">
                    <div th:replace="~{fragments/pagination :: pagination(${projectPage}, @{/projects})}"></div>
                </div>
            </div>
        </div>
    </div>    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script th:src="@{/js/history-tracker.js}"></script>
    <script>
        // 导出Excel确认对话框函数
function confirmExportToExcel(event) {
    if (confirm('确定要导出匹配的项目数据到Excel文件吗？\n\n导出将包含当前筛选条件下的所有项目数据。')) {
        let btn = event && event.target ? event.target : null;
        let originalText = btn ? btn.innerHTML : '';
        if (btn) {
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 导出中...';
            btn.disabled = true;
        }
        // 并行导出两个Excel，全部完成后再恢复按钮
        Promise.all([
            new Promise(resolve => exportProjectsToExcel({target: btn, resolve}, '/projects/export/projects?')),
            new Promise(resolve => exportProjectsToExcel({target: btn, resolve}, '/projects/export/tasks?'))
        ]).finally(() => {
            if (btn) {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        });
    }
}

        // 确保项目链接可以正常工作
        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('.project-link').forEach(function (link) {
                link.addEventListener('click', function (e) {
                    console.log('Project link clicked');
                    // 不阻止默认行为，让链接正常跳转
                });
            });
        });

        // 格式化日期为标准格式 yyyy-MM-dd HH:mm:ss
        function formatDateToStandard(dateInput) {
            if (dateInput.value) {
                // 获取日期值
                const selectedDate = new Date(dateInput.value);

                // 格式化为 yyyy-MM-dd HH:mm:ss
                const year = selectedDate.getFullYear();
                const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
                const day = String(selectedDate.getDate()).padStart(2, '0');

                // 如果是开始日期，设置为当天开始时间 00:00:00
                // 如果是结束日期，设置为当天结束时间 23:59:59
                let hours = '00', minutes = '00', seconds = '00';
                if (dateInput.name === 'field_createdDate_end') {
                    hours = '23';
                    minutes = '59';
                    seconds = '59';
                }

                // 创建标准格式的日期字符串
                const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

                // 创建隐藏字段存储格式化后的日期
                let hiddenInput = document.querySelector(`input[type="hidden"][name="${dateInput.name}_formatted"]`);
                if (!hiddenInput) {
                    hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = `${dateInput.name}_formatted`;
                    dateInput.parentNode.appendChild(hiddenInput);
                }
                hiddenInput.value = formattedDate;

                console.log(`格式化日期: ${dateInput.name} = ${formattedDate}`);
            }
        }

        // 通用函数：设置模态框数据
        function setupModal(modal, projectId, projectName, actionUrl) {
            var $modal = $(modal);
            $modal.find('.project-id').val(projectId);
            $modal.find('.project-name').text(projectName);
            $modal.find('form').attr('action', actionUrl);
        }

        // 删除项目、归档项目和取消归档项目的相关JavaScript代码移除

        // 高级搜索相关函数
        function updateValueField(selectField) {
            const valueContainer = selectField.closest('.search-condition').querySelector('.value-container');
            const selectedField = selectField.value;

            if (!selectedField) {
                valueContainer.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';
                return;
            }

            // 获取当前值（如果存在）
            const currentValue = valueContainer.querySelector('input, select')?.value || '';

            let inputHtml = '';

            // 根据选择的字段类型生成不同的输入控件
            switch (selectedField) {
                case 'responsible':
                case 'supervisor1':
                case 'supervisor2':
                    // 使用HTML模板字符串创建下拉列表，与其他字段保持一致的方式
                    let fieldLabel = selectedField === 'responsible' ? '负责人' : 
                                   (selectedField === 'supervisor1' ? '监理人1' : '监理人2');
                    inputHtml = `
                        <select name="field_${selectedField}" class="form-select search-value">
                            <option value="">请选择${fieldLabel}</option>
                    `;

                    // 添加人员选项
                    console.log('添加人员选项:', personnelList);
                    personnelList.forEach(person => {
                        inputHtml += `<option value="${person}">${person}</option>`;
                    });

                    // 关闭select标签
                    inputHtml += `</select>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break;
                case 'status':
                    inputHtml = `
                        <select name="field_${selectedField}" class="form-select search-value">
                            <option value="">请选择状态</option>
                            <option value="未开始">未开始</option>
                            <option value="已完成">已完成</option>
                            <option value="进行中">进行中</option>
                            <option value="已暂停">已暂停</option>
                            <option value="已取消">已取消</option>
                        </select>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break;
                case 'risk':
                    inputHtml = `
                        <select name="field_${selectedField}" class="form-select search-value">
                            <option value="">请选择风险等级</option>
                            <option value="正常">正常</option>
                            <option value="低">低</option>
                            <option value="中">中</option>
                            <option value="高">高</option>
                        </select>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break;
                case 'hasComputerInfo':
                    inputHtml = `
                        <select name="field_${selectedField}" class="form-select search-value">
                            <option value="">请选择编码</option>
                            <option value="有">有</option>
                            <option value="无">无</option>
                        </select>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break;
                case 'visionType':
                    // 视觉类型下拉列表
                    inputHtml = `
                        <select name="field_${selectedField}" class="form-select search-value">
                            <option value="">请选择视觉类型</option>
                    `;

                    console.log('更新视觉类型字段，视觉类型列表:', visionTypeList);
                    console.log('当前值:', currentValue);

                    // 使用全局变量 visionTypeList
                    if (visionTypeList && visionTypeList.length > 0) {
                        visionTypeList.forEach(type => {
                            inputHtml += `<option value="${type}">${type}</option>`;
                        });
                    } else {
                        console.warn('视觉类型列表为空或不存在');
                        // 使用备选值
                        ['2D', '3D', 'OCR', '其他'].forEach(type => {
                            inputHtml += `<option value="${type}">${type}</option>`;
                        });
                    }

                    // 关闭select标签
                    inputHtml += `</select>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break; case 'totalCost1':
                    // 总成本1区间搜索
                    inputHtml = `
                        <div class="row">
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最小</span>
                                    <input type="number" step="0.01" class="form-control" name="field_totalCost1_min" placeholder="最小值">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最大</span>
                                    <input type="number" step="0.01" class="form-control" name="field_totalCost1_max" placeholder="最大值">
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break; case 'totalCost2':
                    // 总成本2区间搜索
                    inputHtml = `
                        <div class="row">
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最小</span>
                                    <input type="number" step="0.01" class="form-control" name="field_totalCost2_min" placeholder="最小值">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最大</span>
                                    <input type="number" step="0.01" class="form-control" name="field_totalCost2_max" placeholder="最大值">
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break; case 'ratedDurationDays':
                    // 额定工期区间搜索
                    inputHtml = `
                        <div class="row">
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最小</span>
                                    <input type="number" step="0.01" class="form-control" name="field_${selectedField}_min" placeholder="最小值">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最大</span>
                                    <input type="number" step="0.01" class="form-control" name="field_${selectedField}_max" placeholder="最大值">
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break; case 'difficultyCoefficient':
                    // 难度系数区间搜索
                    inputHtml = `
                        <div class="row">
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最小</span>
                                    <input type="number" step="0.01" class="form-control" name="field_${selectedField}_min" placeholder="最小值">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最大</span>
                                    <input type="number" step="0.01" class="form-control" name="field_${selectedField}_max" placeholder="最大值">
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break; case 'cameraQuantity':
                    // 相机数量区间搜索
                    inputHtml = `
                        <div class="row">
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最小</span>
                                    <input type="number" step="1" class="form-control" name="field_${selectedField}_min" placeholder="最小值" min="0">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最大</span>
                                    <input type="number" step="1" class="form-control" name="field_${selectedField}_max" placeholder="最大值" min="0">
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break; case 'quantity':
                    // 设备数量区间搜索
                    inputHtml = `
                        <div class="row">
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最小</span>
                                    <input type="number" step="1" class="form-control" name="field_${selectedField}_min" placeholder="最小值" min="0">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最大</span>
                                    <input type="number" step="1" class="form-control" name="field_${selectedField}_max" placeholder="最大值" min="0">
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break; case 'inProgressTaskName':
                    // 进行中任务名称搜索
                    inputHtml = `
                        <input type="text" name="field_${selectedField}" class="form-control search-value" placeholder="请输入任务名称关键词">
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break; case 'pausedTaskName':
                    // 暂停中任务名称搜索
                    inputHtml = `
                        <input type="text" name="field_${selectedField}" class="form-control search-value" placeholder="请输入任务名称关键词">
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break;
                     
                default:
                    inputHtml = `
                        <input type="text" name="field_${selectedField}" class="form-control search-value" placeholder="请输入${selectField.options[selectField.selectedIndex].text}">
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
            }valueContainer.innerHTML = inputHtml;
        } function addSearchCondition() {
            const searchConditions = document.getElementById('searchConditions');
            const newCondition = document.createElement('div');
            newCondition.className = 'search-condition row mb-3';
            newCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select search-field" onchange="updateValueField(this)">
                        <option value="">选择字段</option>
                        <option value="projectName">项目名称</option>
                        <option value="projectCode">项目编号</option>
                        <option value="customerName">客户名称</option>
                        <option value="projectType">项目类型</option>
                        <option value="visionType">视觉类型</option>
                        <option value="responsible">负责人</option>
                        <option value="supervisor1">监理人1</option>
                        <option value="supervisor2">监理人2</option>
                        <option value="status">状态</option>
                        <option value="hasComputerInfo">编码</option>
                        <option value="inProgressTaskName">进行中任务</option>                        <option value="pausedTaskName">暂停中任务</option>                        <option value="risk">风险等级</option><option value="totalCost1">总成本1</option>
                        <option value="totalCost2">总成本2</option>
                        <option value="ratedDurationDays">额定工期</option><option value="difficultyCoefficient">难度系数</option>
                        <option value="cameraQuantity">相机数量</option>
                        <option value="quantity">设备数量</option>
                        <option value="salesOrderNumber">销售订单号</option>
                        <option value="productPartNumber">料号</option>
                    </select>
                </div>
                <div class="col-10 col-sm-5 col-md-7 value-container">
                    <input type="text" class="form-control search-value" disabled placeholder="请先选择字段">
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            searchConditions.appendChild(newCondition);
        }        // 添加带值的搜索条件函数
        function addSearchConditionWithValues(fieldName, fieldValue) {
            console.log(`添加带值的项目搜索条件: 字段=${fieldName}, 值=${fieldValue}`);
            const conditions = document.getElementById('searchConditions');
            const condition = document.createElement('div');
            condition.className = 'search-condition row mb-3';

            // 创建字段选择列
            const fieldCol = document.createElement('div');
            fieldCol.className = 'col-12 col-sm-6 col-md-3 mb-2 mb-sm-0';

            // 创建字段选择下拉框
            const select = document.createElement('select');
            select.className = 'form-select search-field';
            // 暂时不设置onchange，避免在设置值时触发重复添加选项
            // select.setAttribute('onchange', 'updateValueField(this)');
            // 注意：这里不要设置 name="fieldNames"，避免重复            // 注意：这里不要设置 name="fieldNames"，避免重复
            select.innerHTML = `
                <option value="">选择字段</option>                
                <option value="projectName">项目名称</option>
                <option value="projectCode">项目编号</option>
                <option value="customerName">客户名称</option>
                <option value="projectType">项目类型</option>
                <option value="visionType">视觉类型</option>                
                <option value="responsible">负责人</option>
                <option value="supervisor1">监理人1</option>
                <option value="supervisor2">监理人2</option>
                <option value="status">状态</option>
                <option value="hasComputerInfo">编码</option>                
                <option value="inProgressTaskName">进行中任务</option>
                <option value="pausedTaskName">暂停中任务</option>                <option value="risk">风险等级</option>                <option value="totalCost1">总成本1</option>
                <option value="totalCost2">总成本2</option>
                <option value="ratedDurationDays">额定工期</option>                <option value="difficultyCoefficient">难度系数</option>
                <option value="cameraQuantity">相机数量</option>
                <option value="quantity">设备数量</option>
                <option value="salesOrderNumber">销售订单号</option>
                <option value="productPartNumber">料号</option>
            `;

            // 设置选中的字段
            select.value = fieldName;
            fieldCol.appendChild(select);

            // 创建值输入列
            const valueCol = document.createElement('div');
            valueCol.className = 'col-10 col-sm-5 col-md-7 value-container';

            // 根据字段类型创建对应的输入控件
            let inputField;
            switch (fieldName) {
                case 'responsible':
                case 'supervisor1':
                case 'supervisor2':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select search-value';
                    inputField.name = 'field_' + fieldName;
                    
                    let fieldLabel = fieldName === 'responsible' ? '负责人' : 
                                   (fieldName === 'supervisor1' ? '监理人1' : '监理人2');
                    inputField.innerHTML = `<option value="">请选择${fieldLabel}</option>`;

                    // 使用全局变量 personnelList 添加选项
                    if (personnelList && personnelList.length > 0) {
                        personnelList.forEach(person => {
                            const option = document.createElement('option');
                            option.value = person;
                            option.textContent = person;
                            inputField.appendChild(option);
                        });
                    }
                    break;
                case 'status':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select search-value';
                    inputField.name = 'field_' + fieldName;
                    inputField.innerHTML = `
                        <option value="">请选择状态</option>
                        <option value="未开始">未开始</option>
                        <option value="进行中">进行中</option>
                        <option value="已完成">已完成</option>
                        <option value="已暂停">已暂停</option>
                        <option value="已取消">已取消</option>
                    `;
                    break;
                case 'visionType':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select search-value';
                    inputField.name = 'field_' + fieldName;

                    // 创建默认选项
                    inputField.innerHTML = '<option value="">请选择视觉类型</option>';

                    console.log('添加搜索条件：更新视觉类型字段，视觉类型列表:', visionTypeList);

                    // 使用全局变量 visionTypeList 动态生成选项
                    if (visionTypeList && visionTypeList.length > 0) {
                        visionTypeList.forEach(type => {
                            const option = document.createElement('option');
                            option.value = type;
                            option.textContent = type;
                            inputField.appendChild(option);
                        });
                    }
                    break;
                case 'projectType':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select search-value';
                    inputField.name = 'field_' + fieldName;

                    // 创建默认选项
                    inputField.innerHTML = '<option value="">请选择项目类型</option>';

                    console.log('添加搜索条件：更新项目类型字段，项目类型列表:', projectTypeList);

                    // 使用全局变量 projectTypeList 动态生成选项
                    if (projectTypeList && projectTypeList.length > 0) {
                        projectTypeList.forEach(type => {
                            const option = document.createElement('option');
                            option.value = type;
                            option.textContent = type;
                            inputField.appendChild(option);
                        });
                    }
                    break;
                case 'risk':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select search-value';
                    inputField.name = 'field_' + fieldName;
                    inputField.innerHTML = `
                        <option value="">请选择风险等级</option>
                        <option value="低">低</option>
                        <option value="中">中</option>
                        <option value="高">高</option>
                    `;
                    break;
                default:
                    inputField = document.createElement('input');
                    inputField.type = 'text';
                    inputField.className = 'form-control search-value';
                    inputField.name = 'field_' + fieldName;
                    inputField.placeholder = '请输入';
            }            // 设置字段值
            // 对于进行中任务名称字段，即使空值也要设置（空值表示搜索没有进行中任务的项目）
            if (fieldName === 'inProgressTaskName') {
                if (fieldValue !== null && fieldValue !== undefined) {
                    inputField.value = fieldValue;
                }
            } else if (fieldName === 'pausedTaskName') {
                // 对于暂停中任务名称字段，即使空值也要设置（空值表示搜索没有暂停中任务的项目）
                if (fieldValue !== null && fieldValue !== undefined) {
                    inputField.value = fieldValue;
                }
            } else {
                // 其他字段的正常处理逻辑
                if (fieldValue) {
                    inputField.value = fieldValue;
                    // 对于select元素，确保选项被正确选中
                    if (inputField.tagName === 'SELECT') {
                        Array.from(inputField.options).forEach(option => {
                            if (option.value === fieldValue) {
                                option.selected = true;
                            }
                        });
                    }
                }
            }

            // 添加隐藏的字段名
            const hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.name = 'fieldNames';
            hiddenField.value = fieldName;

            valueCol.appendChild(inputField);
            valueCol.appendChild(hiddenField);

            // 创建删除按钮列
            const deleteCol = document.createElement('div');
            deleteCol.className = 'col-2 col-sm-1 col-md-2';

            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-outline-danger';
            deleteBtn.setAttribute('onclick', 'removeCondition(this)');
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
            deleteCol.appendChild(deleteBtn);

            // 组装条件行
            condition.appendChild(fieldCol);
            condition.appendChild(valueCol);
            condition.appendChild(deleteCol);

            conditions.appendChild(condition);
            
            // 在完成所有选项添加后再设置onchange事件，避免触发重复添加
            select.setAttribute('onchange', 'updateValueField(this)');
            
            console.log(`已添加项目搜索条件: 字段=${fieldName}, 值=${fieldValue}`);
        }

        function removeCondition(button) {
            const condition = button.closest('.search-condition');
            if (document.querySelectorAll('.search-condition').length > 1) {
                condition.remove();
            } else {
                // 如果只有一个条件，则清空而不是删除
                const select = condition.querySelector('.search-field');
                select.value = '';
                updateValueField(select);
            }
        }        // 添加时间条件函数
        function addTimeCondition() {
            const searchConditions = document.getElementById('searchConditions');
            const newCondition = document.createElement('div');
            newCondition.className = 'search-condition row mb-3';
            newCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select" disabled>
                        <option>创建时间</option>
                    </select>
                    <input type="hidden" name="fieldNames" value="createdDate">
                </div>
                <div class="col-6 col-sm-3 col-md-3 mb-2 mb-sm-0">
                    <div class="input-group">
                        <span class="input-group-text">从</span>
                        <input type="date" class="form-control" name="field_createdDate_start"
                               onchange="formatDateToStandard(this)">
                    </div>
                </div>
                <div class="col-6 col-sm-3 col-md-4 mb-2 mb-sm-0">
                    <div class="input-group">
                        <span class="input-group-text">到</span>
                        <input type="date" class="form-control" name="field_createdDate_end"
                               onchange="formatDateToStandard(this)">
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-2 text-end text-md-start mt-2 mt-md-0">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            searchConditions.appendChild(newCondition);
        }

        // 添加结束时间条件函数
        function addActualEndTimeCondition() {
            console.log('添加结束时间条件');

            // 检查是否已经存在结束时间条件
            const existingEndTimeCondition = document.querySelector('input[name="fieldNames"][value="actualEndDate"]');
            if (existingEndTimeCondition) {
                console.log('已存在结束时间条件，不重复添加');
                alert('已存在结束时间条件，不能重复添加');
                return;
            }

            const searchConditions = document.getElementById('searchConditions');
            const newCondition = document.createElement('div');
            newCondition.className = 'search-condition row mb-3';
            newCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select" disabled>
                        <option>结束时间</option>
                    </select>
                    <input type="hidden" name="fieldNames" value="actualEndDate">
                </div>
                <div class="col-6 col-sm-3 col-md-3 mb-2 mb-sm-0">
                    <div class="input-group">
                        <span class="input-group-text">从</span>
                        <input type="date" class="form-control" name="field_actualEndDate_start"
                               onchange="formatDateToStandard(this)">
                    </div>
                </div>
                <div class="col-6 col-sm-3 col-md-4 mb-2 mb-sm-0">
                    <div class="input-group">
                        <span class="input-group-text">到</span>
                        <input type="date" class="form-control" name="field_actualEndDate_end"
                               onchange="formatDateToStandard(this)">
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-2 text-end text-md-start mt-2 mt-md-0">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            searchConditions.appendChild(newCondition);
            console.log('已添加结束时间条件');
        }        // 表单提交前处理
        document.getElementById('advancedSearchForm').addEventListener('submit', function (event) {
            // 检查是否有有效的搜索条件
            const hasValidCondition = Array.from(document.querySelectorAll('.search-field'))
                .some(select => select.value !== '');

            // 检查是否有时间条件（创建时间或结束时间）
            const hasTimeCondition = Array.from(this.querySelectorAll('input[name="fieldNames"]'))
                .some(input => input.value === 'createdDate' || input.value === 'actualEndDate');

            if (!hasValidCondition && !hasTimeCondition) {
                event.preventDefault();
                alert('请至少选择一个搜索字段');
                return;
            }

            // 添加必需的字段名到 fieldNames 数组
            const fieldsToAdd = [];

            // 检查各种条件并添加对应的字段名
            if (this.querySelector('input[name="field_totalCost1_min"]') || this.querySelector('input[name="field_totalCost1_max"]')) {
                fieldsToAdd.push('totalCost1');
            }
            if (this.querySelector('input[name="field_totalCost2_min"]') || this.querySelector('input[name="field_totalCost2_max"]')) {
                fieldsToAdd.push('totalCost2');
            }
            if (this.querySelector('input[name="field_visionCost_min"]') || this.querySelector('input[name="field_visionCost_max"]')) {
                fieldsToAdd.push('visionCost');
            }
            if (this.querySelector('input[name="field_visionCost2_min"]') || this.querySelector('input[name="field_visionCost2_max"]')) {
                fieldsToAdd.push('visionCost2');
            }
            if (this.querySelector('input[name="field_ratedDurationDays_min"]') || this.querySelector('input[name="field_ratedDurationDays_max"]')) {
                fieldsToAdd.push('ratedDurationDays');
            }
            if (this.querySelector('input[name="field_difficultyCoefficient_min"]') || this.querySelector('input[name="field_difficultyCoefficient_max"]')) {
                fieldsToAdd.push('difficultyCoefficient');
            }
            if (this.querySelector('input[name="field_cameraQuantity_min"]') || this.querySelector('input[name="field_cameraQuantity_max"]')) {
                fieldsToAdd.push('cameraQuantity');
            }
            if (this.querySelector('input[name="field_quantity_min"]') || this.querySelector('input[name="field_quantity_max"]')) {
                fieldsToAdd.push('quantity');
            }

            // 获取现有的字段名
            const existingFieldNames = Array.from(this.querySelectorAll('input[name="fieldNames"]')).map(input => input.value);

            // 添加缺失的字段名
            fieldsToAdd.forEach(fieldName => {
                if (!existingFieldNames.includes(fieldName)) {
                    const hiddenField = document.createElement('input');
                    hiddenField.type = 'hidden';
                    hiddenField.name = 'fieldNames';
                    hiddenField.value = fieldName;
                    this.appendChild(hiddenField);
                    console.log(`添加字段名: ${fieldName}`);
                }
            });

            // 清理重复的字段名
            const fieldNameInputs = Array.from(this.querySelectorAll('input[name="fieldNames"]'));
            const fieldNameValues = {};

            console.log('表单提交前处理，当前字段名数量:', fieldNameInputs.length);

            // 统计各个字段名的出现次数
            fieldNameInputs.forEach(input => {
                const value = input.value;
                if (value) {
                    fieldNameValues[value] = (fieldNameValues[value] || 0) + 1;
                }
            });

            console.log('字段名统计:', fieldNameValues);

            // 移除重复的字段名，保留第一个
            for (const [fieldName, count] of Object.entries(fieldNameValues)) {
                if (count > 1) {
                    console.log(`发现重复字段名: ${fieldName}, 次数: ${count}`);

                    let found = false;
                    fieldNameInputs.forEach(input => {
                        if (input.value === fieldName) {
                            if (found) {
                                // 移除重复的字段名
                                input.remove();
                                console.log(`移除重复的字段名: ${fieldName}`);
                            } else {
                                found = true;
                                console.log(`保留字段名: ${fieldName}`);
                            }
                        }
                    });
                }
            }
        });

        // 分页链接处理
        document.addEventListener('DOMContentLoaded', function () {
            // 处理分页链接点击事件
            document.querySelectorAll('.pagination-link').forEach(link => {
                link.addEventListener('click', function (e) {
                    if (e) e.preventDefault();
                    // 获取目标页码
                    const page = this && this.getAttribute ? this.getAttribute('data-page') : null;
                    if (!page) {
                        // 兜底跳转
                        if (link && link.href) window.location.href = link.href;
                        return;
                    }
                    // 获取当前URL和参数
                    let url;
                    try {
                        url = new URL(window.location.href);
                    } catch (err) {
                        if (link && link.href) window.location.href = link.href;
                        return;
                    }
                    const params = url.searchParams;
                    // 更新页码参数
                    params.set('page', page);
                    // 检查是否在高级搜索模式
                    const isSearchMode = Array.from(params.keys()).some(key => key.startsWith('field_') || key === 'fieldNames');

                    // 如果在高级搜索模式，确保URL路径正确
                    if (isSearchMode && url.pathname !== '/projects/advanced-search') {
                        url.pathname = '/projects/advanced-search';
                    }

                    // 构建新的URL
                    url.search = params.toString();

                    // 跳转到新URL
                    window.location.href = url.toString();
                });
            });            // 初始化：如果有URL参数，还原搜索条件
            const urlParams = new URLSearchParams(window.location.search);
            const fieldNames = urlParams.getAll('fieldNames');
            const currentPath = window.location.pathname;

            if (fieldNames.length > 0) {
                // 检查是否需要还原搜索条件
                // 只有当当前页面是从其他页面跳转过来或者表单是空的时候才还原条件
                const hasExistingConditions = document.querySelectorAll('.search-condition .search-field').length > 0
                    && Array.from(document.querySelectorAll('.search-condition .search-field')).some(select => select.value !== '');

                console.log('当前路径:', currentPath);
                console.log('是否已有搜索条件:', hasExistingConditions);
                console.log('URL中的字段数量:', fieldNames.length);

                // 如果当前是高级搜索页面且已经有条件，说明是表单提交后的结果页面，不需要重复添加条件
                if (currentPath === '/projects/advanced-search' && hasExistingConditions) {
                    console.log('高级搜索页面已有条件，跳过条件恢复避免重复');
                    return;
                }

                // 清空默认的搜索条件（只有在需要恢复时才清空）
                if (!hasExistingConditions) {
                    document.getElementById('searchConditions').innerHTML = '';
                }                // 为每个字段创建一个搜索条件
                fieldNames.forEach((fieldName, index) => {
                    if (fieldName === 'createdDate') {
                        // 创建时间条件
                        addTimeCondition();

                        // 设置时间范围值
                        const startDate = urlParams.get('field_createdDate_start');
                        const endDate = urlParams.get('field_createdDate_end');

                        if (startDate) {
                            document.querySelector('input[name="field_createdDate_start"]').value = startDate;
                        }

                        if (endDate) {
                            document.querySelector('input[name="field_createdDate_end"]').value = endDate;
                        }
                    } else if (fieldName === 'actualEndDate') {
                        // 结束时间条件
                        addActualEndTimeCondition();

                        // 设置时间范围值
                        const startDate = urlParams.get('field_actualEndDate_start');
                        const endDate = urlParams.get('field_actualEndDate_end');

                        if (startDate) {
                            document.querySelector('input[name="field_actualEndDate_start"]').value = startDate;
                        } if (endDate) {
                            document.querySelector('input[name="field_actualEndDate_end"]').value = endDate;
                        }
                    } else if (fieldName === 'totalCost1') {
                        // 总成本1条件
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = 'totalCost1';
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_totalCost1_min');
                        const maxValue = urlParams.get('field_totalCost1_max');

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_totalCost1_min"]').value = minValue;
                        }

                        if (maxValue) {
                            lastCondition.querySelector('input[name="field_totalCost1_max"]').value = maxValue;
                        }
                    } else if (fieldName === 'visionCost') {
                        // 总成本1条件（兼容性保留）
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_visionCost_min');
                        const maxValue = urlParams.get('field_visionCost_max');

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_visionCost_min"]').value = minValue;
                        } if (maxValue) {
                            lastCondition.querySelector('input[name="field_visionCost_max"]').value = maxValue;
                        }
                    } else if (fieldName === 'cameraQuantity') {
                        // 相机数量条件
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_cameraQuantity_min');
                        const maxValue = urlParams.get('field_cameraQuantity_max');

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_cameraQuantity_min"]').value = minValue;
                        }

                        if (maxValue) {
                            lastCondition.querySelector('input[name="field_cameraQuantity_max"]').value = maxValue;
                        }
                    } else if (fieldName === 'ratedDurationDays') {
                        // 额定工期条件
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_ratedDurationDays_min');
                        const maxValue = urlParams.get('field_ratedDurationDays_max');

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_ratedDurationDays_min"]').value = minValue;
                        }

                        if (maxValue) {
                            lastCondition.querySelector('input[name="field_ratedDurationDays_max"]').value = maxValue;
                        }
                    } else if (fieldName === 'difficultyCoefficient') {
                        // 难度系数条件
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_difficultyCoefficient_min');
                        const maxValue = urlParams.get('field_difficultyCoefficient_max');

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_difficultyCoefficient_min"]').value = minValue;
                        } if (maxValue) {
                            lastCondition.querySelector('input[name="field_difficultyCoefficient_max"]').value = maxValue;
                        }
                    } else if (fieldName === 'totalCost2') {
                        // 总成本2条件
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = 'totalCost2';
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_totalCost2_min');
                        const maxValue = urlParams.get('field_totalCost2_max');

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_totalCost2_min"]').value = minValue;
                        }

                        if (maxValue) {
                            lastCondition.querySelector('input[name="field_totalCost2_max"]').value = maxValue;
                        }
                    } else if (fieldName === 'visionCost2') {
                        // 总成本2条件（兼容性保留）
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值 - 兼容旧的字段名
                        const minValue = urlParams.get('field_visionCost2_min') || urlParams.get('field_totalCost2_min');
                        const maxValue = urlParams.get('field_visionCost2_max') || urlParams.get('field_totalCost2_max');

                        if (minValue) {
                            const minInput = lastCondition.querySelector('input[name="field_totalCost2_min"]');
                            if (minInput) {
                                minInput.value = minValue;
                            }
                        } if (maxValue) {
                            const maxInput = lastCondition.querySelector('input[name="field_totalCost2_max"]');
                            if (maxInput) {
                                maxInput.value = maxValue;
                            }
                        }
                    } else if (fieldName === 'quantity') {
                        // 设备数量条件
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_quantity_min');
                        const maxValue = urlParams.get('field_quantity_max');

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_quantity_min"]').value = minValue;
                        }

                        if (maxValue) {
                            lastCondition.querySelector('input[name="field_quantity_max"]').value = maxValue;
                        }
                    } else {
                        // 普通条件，使用新的addSearchConditionWithValues函数
                        const fieldValue = urlParams.get('field_' + fieldName);
                        // 对于进行中任务名称字段，即使值为空也要恢复（空值表示搜索没有进行中任务的项目）
                        if (fieldName === 'inProgressTaskName') {
                            if (fieldValue !== null) { // 只要参数存在就恢复，即使是空字符串
                                console.log(`恢复进行中任务名称搜索条件: 字段=${fieldName}, 值='${fieldValue}'`);
                                addSearchConditionWithValues(fieldName, fieldValue);
                            }
                        } else if (fieldName === 'pausedTaskName') {
                            // 对于暂停中任务名称字段，即使值为空也要恢复（空值表示搜索没有暂停中任务的项目）
                            if (fieldValue !== null) { // 只要参数存在就恢复，即使是空字符串
                                console.log(`恢复暂停中任务名称搜索条件: 字段=${fieldName}, 值='${fieldValue}'`);
                                addSearchConditionWithValues(fieldName, fieldValue);
                            }
                        } else {
                            // 其他字段的正常处理逻辑
                            if (fieldValue) {
                                console.log(`恢复项目搜索条件: 字段=${fieldName}, 值=${fieldValue}`);
                                addSearchConditionWithValues(fieldName, fieldValue);
                            }
                        }
                    }
                });
            }

            // 添加自动提交功能，检查是否有搜索条件并自动提交
            if (fieldNames.length > 0) {
                // 检查当前URL路径，只有在管理页面才自动提交
                // 如果是 /projects/advanced-search 页面，说明搜索已经在后端执行，不需要再次提交
                console.log('当前页面路径:', currentPath);

                if (currentPath === '/projects/list' || currentPath === '/projects') {
                    console.log('检测到项目管理页面的搜索条件，准备自动执行搜索');
                    // 使用setTimeout确保DOM完全更新后再提交
                    setTimeout(() => {
                        const form = document.getElementById('advancedSearchForm');
                        if (form) {
                            console.log('自动提交项目搜索表单');
                            form.submit();
                        }
                    }, 100);
                } else if (currentPath === '/projects/advanced-search') {
                    console.log('当前在高级搜索页面，搜索已在后端执行，无需自动提交');
                } else {
                    console.log('当前在其他页面，不需要自动提交表单');
                }
            }
        });

        // 导出项目Excel函数
function exportProjectsToExcel(event, exportType) {
    // 按钮加载逻辑已由confirmExportToExcel统一处理
 
    // 获取当前页面的搜索参数
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = exportType + urlParams.toString();

    fetch(exportUrl, {
        method: 'GET'
    })
    .then(response => {
        if (response.ok) {
            // 获取文件名
            let filename = 'tasks.xlsx';
            const disposition = response.headers.get('Content-Disposition');
            if (disposition && disposition.indexOf('filename=') !== -1) {
                filename = disposition.split('filename=')[1].replace(/"/g, '').trim();
            }
            return response.blob().then(blob => ({ blob, filename }));
        } else {
            return response.text().then(text => { throw new Error(text); });
        }
    })
    .then(({ blob, filename }) => {
        // 下载 Excel 文件
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
        alert('项目数据导出成功！');
    })
    .catch(error => {
        let msg = error.message;
        try {
            const errJson = JSON.parse(msg);
            msg = errJson.message || msg;
        } catch (e) {}
        alert('导出失败：' + msg);
    })
    .finally(() => {
        if (event && event.resolve) {
            event.resolve();
        }
    });
}

        // 修复所有项目总成本函数
        function fixAllTotalCosts() {
            // 确认对话框
            if (!confirm('确定要修复所有项目的总成本1和总成本2吗？\n\n这将重新计算所有项目的总成本：\n• 总成本1 = 单机成本1 × 设备数量\n• 总成本2 = 单机成本2 × 设备数量\n\n操作不可撤销，请确认！')) {
                return;
            }

            // 显示加载提示
            const btn = document.getElementById('fixTotalCostsBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 修复中...';
            btn.disabled = true;

            // 发起修复请求
            fetch('/projects/fix-total-costs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('总成本修复完成！\n\n' + data.message + '\n\n页面将自动刷新以显示最新数据。');
                        // 刷新页面以显示更新后的数据
                        window.location.reload();
                    } else {
                        alert('修复失败：' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('修复总成本错误:', error);
                    alert('修复失败，请稍后重试或联系管理员');
                })
                .finally(() => {
                    // 恢复按钮状态
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }
    </script>
</body>

</html>