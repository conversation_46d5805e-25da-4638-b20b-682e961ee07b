package com.mylog.model;

import lombok.Data;

import jakarta.persistence.*;

@Entity
@Table(name = "computer_info")
@Data
public class ComputerInfo {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "project_id")
    private Integer projectId;
    
    @Column(name = "machine_code")
    private String machineCode;
    
    @Column(name = "license_code")
    private String licenseCode;
    
    @Column(name = "main_version")
    private String mainVersion;
    
    @Column(name = "manager_version")
    private String managerVersion;
    
    @Column(name = "tool_version")
    private String toolVersion;
    
    @Column(name = "created_by")
    private String createdBy;
    
    @Column(name = "created_time")
    private String createdTime;
    
    @Column(name = "computer_model")
    private String computerModel;
    
    @Column(name = "operating_system")
    private String operatingSystem;
    
    @Column(name = "remark")
    private String remark;
    
    @Column(name = "workstation_info")
    private String workstationInfo;
}
