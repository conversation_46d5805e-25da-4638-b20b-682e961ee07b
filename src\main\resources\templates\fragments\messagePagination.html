<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<body>
    <!-- 消息页面分页片段 -->
    <div th:fragment="messagePagination(page)">
        <nav aria-label="Page navigation" class="d-flex justify-content-center flex-column">
            <!-- 分页导航 -->
            <ul class="pagination mb-2 justify-content-center">
                <!-- 首页按钮 «««  -->
                <li class="page-item" th:classappend="${page.first ? 'disabled' : ''}">
                    <a class="page-link" th:href="${page.first ? '#' : '/messages?page=0&size=' + page.size}" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;&laquo;</span>
                    </a>
                </li>
                
                <!-- 页码按钮，实现智能分页显示 -->
                <th:block th:with="totalPages=${page.totalPages}, currentPage=${page.number}">
                    <!-- 当总页数不超过5页时，显示所有页码 -->
                    <th:block th:if="${totalPages <= 5}">
                        <li class="page-item" th:each="i : ${#numbers.sequence(0, totalPages - 1)}"
                            th:classappend="${i == currentPage ? 'active' : ''}">
                            <a class="page-link" th:href="@{/messages(page=${i}, size=${page.size})}" th:text="${i + 1}">1</a>
                        </li>
                    </th:block>
                    
                    <!-- 当总页数超过5页时，使用智能分页显示 -->
                    <th:block th:if="${totalPages > 5}">
                        <!-- 当前页在前3页时 -->
                        <th:block th:if="${currentPage <= 2}">
                            <li class="page-item" th:each="i : ${#numbers.sequence(0, 4)}"
                                th:classappend="${i == currentPage ? 'active' : ''}">
                                <a class="page-link" th:href="@{/messages(page=${i}, size=${page.size})}" th:text="${i + 1}">1</a>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link" th:href="@{/messages(page=${totalPages - 1}, size=${page.size})}" th:text="${totalPages}">最后页</a>
                            </li>
                        </th:block>
                        
                        <!-- 当前页在后3页时 -->
                        <th:block th:if="${currentPage >= totalPages - 3}">
                            <li class="page-item">
                                <a class="page-link" th:href="@{/messages(page=0, size=${page.size})}" th:text="1">1</a>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            <li class="page-item" th:each="i : ${#numbers.sequence(totalPages - 5, totalPages - 1)}"
                                th:classappend="${i == currentPage ? 'active' : ''}">
                                <a class="page-link" th:href="@{/messages(page=${i}, size=${page.size})}" th:text="${i + 1}">1</a>
                            </li>
                        </th:block>
                        
                        <!-- 当前页在中间时 -->
                        <th:block th:if="${currentPage > 2 && currentPage < totalPages - 3}">
                            <li class="page-item">
                                <a class="page-link" th:href="@{/messages(page=0, size=${page.size})}" th:text="1">1</a>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            <li class="page-item" th:each="i : ${#numbers.sequence(currentPage - 1, currentPage + 1)}"
                                th:classappend="${i == currentPage ? 'active' : ''}">
                                <a class="page-link" th:href="@{/messages(page=${i}, size=${page.size})}" th:text="${i + 1}">1</a>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link" th:href="@{/messages(page=${totalPages - 1}, size=${page.size})}" th:text="${totalPages}">最后页</a>
                            </li>
                        </th:block>
                    </th:block>
                </th:block>
                
                <!-- 末页按钮 »»» -->
                <li class="page-item" th:classappend="${page.last ? 'disabled' : ''}">
                    <a class="page-link" th:href="${page.last ? '#' : '/messages?page=' + (page.totalPages - 1) + '&size=' + page.size}" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;&raquo;</span>
                    </a>
                </li>
            </ul>
            
            <!-- 页面信息显示 -->
            <div class="text-center text-muted" 
                 th:if="${page.totalPages > 0}" 
                 th:text="'第 ' + (${page.number} + 1) + ' 页 / 共 ' + ${page.totalPages} + ' 页 (' + ${page.totalElements} + ' 条记录)'">
                第 1 页 / 共 22 页 (430 条记录)
            </div>
        </nav>
    </div>

    <!-- 支持搜索的消息页面分页片段 -->
    <div th:fragment="messageSearchPagination(page, keyword)">
        <nav aria-label="Page navigation" class="d-flex justify-content-center flex-column">
            <!-- 分页导航 -->
            <ul class="pagination mb-2 justify-content-center">
                <!-- 首页按钮 «««  -->
                <li class="page-item" th:classappend="${page.first ? 'disabled' : ''}">
                    <a class="page-link" 
                       th:href="${page.first ? '#' : (keyword != null && !#strings.isEmpty(keyword) ? '/messages?page=0&size=' + page.size + '&keyword=' + keyword : '/messages?page=0&size=' + page.size)}" 
                       aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;&laquo;</span>
                    </a>
                </li>
                
                <!-- 页码按钮，实现智能分页显示 -->
                <th:block th:with="totalPages=${page.totalPages}, currentPage=${page.number}">
                    <!-- 当总页数不超过5页时，显示所有页码 -->
                    <th:block th:if="${totalPages <= 5}">
                        <li class="page-item" th:each="i : ${#numbers.sequence(0, totalPages - 1)}"
                            th:classappend="${i == currentPage ? 'active' : ''}">
                            <a class="page-link" 
                               th:href="${keyword != null && !#strings.isEmpty(keyword) ? '/messages?page=' + i + '&size=' + page.size + '&keyword=' + keyword : '/messages?page=' + i + '&size=' + page.size}" 
                               th:text="${i + 1}">1</a>
                        </li>
                    </th:block>
                    
                    <!-- 当总页数超过5页时，使用智能分页显示 -->
                    <th:block th:if="${totalPages > 5}">
                        <!-- 当前页在前3页时 -->
                        <th:block th:if="${currentPage <= 2}">
                            <li class="page-item" th:each="i : ${#numbers.sequence(0, 4)}"
                                th:classappend="${i == currentPage ? 'active' : ''}">
                                <a class="page-link" 
                                   th:href="${keyword != null && !#strings.isEmpty(keyword) ? '/messages?page=' + i + '&size=' + page.size + '&keyword=' + keyword : '/messages?page=' + i + '&size=' + page.size}" 
                                   th:text="${i + 1}">1</a>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link" 
                                   th:href="${keyword != null && !#strings.isEmpty(keyword) ? '/messages?page=' + (totalPages - 1) + '&size=' + page.size + '&keyword=' + keyword : '/messages?page=' + (totalPages - 1) + '&size=' + page.size}" 
                                   th:text="${totalPages}">最后页</a>
                            </li>
                        </th:block>
                        
                        <!-- 当前页在后3页时 -->
                        <th:block th:if="${currentPage >= totalPages - 3}">
                            <li class="page-item">
                                <a class="page-link" 
                                   th:href="${keyword != null && !#strings.isEmpty(keyword) ? '/messages?page=0&size=' + page.size + '&keyword=' + keyword : '/messages?page=0&size=' + page.size}" 
                                   th:text="1">1</a>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            <li class="page-item" th:each="i : ${#numbers.sequence(totalPages - 5, totalPages - 1)}"
                                th:classappend="${i == currentPage ? 'active' : ''}">
                                <a class="page-link" 
                                   th:href="${keyword != null && !#strings.isEmpty(keyword) ? '/messages?page=' + i + '&size=' + page.size + '&keyword=' + keyword : '/messages?page=' + i + '&size=' + page.size}" 
                                   th:text="${i + 1}">1</a>
                            </li>
                        </th:block>
                        
                        <!-- 当前页在中间时 -->
                        <th:block th:if="${currentPage > 2 && currentPage < totalPages - 3}">
                            <li class="page-item">
                                <a class="page-link" 
                                   th:href="${keyword != null && !#strings.isEmpty(keyword) ? '/messages?page=0&size=' + page.size + '&keyword=' + keyword : '/messages?page=0&size=' + page.size}" 
                                   th:text="1">1</a>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            <li class="page-item" th:each="i : ${#numbers.sequence(currentPage - 1, currentPage + 1)}"
                                th:classappend="${i == currentPage ? 'active' : ''}">
                                <a class="page-link" 
                                   th:href="${keyword != null && !#strings.isEmpty(keyword) ? '/messages?page=' + i + '&size=' + page.size + '&keyword=' + keyword : '/messages?page=' + i + '&size=' + page.size}" 
                                   th:text="${i + 1}">1</a>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link" 
                                   th:href="${keyword != null && !#strings.isEmpty(keyword) ? '/messages?page=' + (totalPages - 1) + '&size=' + page.size + '&keyword=' + keyword : '/messages?page=' + (totalPages - 1) + '&size=' + page.size}" 
                                   th:text="${totalPages}">最后页</a>
                            </li>
                        </th:block>
                    </th:block>
                </th:block>
                
                <!-- 末页按钮 »»» -->
                <li class="page-item" th:classappend="${page.last ? 'disabled' : ''}">
                    <a class="page-link" 
                       th:href="${page.last ? '#' : (keyword != null && !#strings.isEmpty(keyword) ? '/messages?page=' + (page.totalPages - 1) + '&size=' + page.size + '&keyword=' + keyword : '/messages?page=' + (page.totalPages - 1) + '&size=' + page.size)}" 
                       aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;&raquo;</span>
                    </a>
                </li>
            </ul>
            
            <!-- 页面信息显示 -->
            <div class="text-center text-muted" 
                 th:if="${page.totalPages > 0}">
                <span th:if="${keyword != null && !#strings.isEmpty(keyword)}" 
                      th:text="'搜索\"' + ${keyword} + '\" - 第 ' + (${page.number} + 1) + ' 页 / 共 ' + ${page.totalPages} + ' 页 (' + ${page.totalElements} + ' 条记录)'">
                    搜索"关键词" - 第 1 页 / 共 22 页 (430 条记录)
                </span>
                <span th:if="${keyword == null || #strings.isEmpty(keyword)}" 
                      th:text="'第 ' + (${page.number} + 1) + ' 页 / 共 ' + ${page.totalPages} + ' 页 (' + ${page.totalElements} + ' 条记录)'">
                    第 1 页 / 共 22 页 (430 条记录)
                </span>
            </div>
        </nav>
    </div>
</body>
</html>