<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>@联想功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        textarea { width: 100%; min-height: 100px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        
        /* @联想功能样式 */
        .mention-dropdown {
            position: absolute;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            min-width: 250px;
            margin-top: 2px;
        }
        
        .mention-search {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        
        .mention-search input {
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 14px;
            outline: none;
        }
        
        .mention-list {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .mention-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f5f5f5;
            display: flex;
            align-items: center;
            transition: background-color 0.2s;
        }
        
        .mention-item:hover,
        .mention-item.active {
            background-color: #f8f9fa;
        }
        
        .mention-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .mentioned-persons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .mentioned-person {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 14px;
            display: flex;
            align-items: center;
            border: 1px solid #bbdefb;
        }
        
        .mentioned-person .remove-btn {
            margin-left: 6px;
            cursor: pointer;
            color: #1976d2;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 50%;
            line-height: 1;
        }
        
        .mentioned-person .remove-btn:hover {
            background: #1976d2;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>@联想功能测试页面</h1>
        <div class="form-group">
            <label for="testContent">测试内容（输入@符号试试）：</label>
            <div style="position: relative;">
                <textarea id="testContent" placeholder="输入@符号可以选择人员..."></textarea>
                <div id="mentionDropdown" class="mention-dropdown" style="display: none;">
                    <div class="mention-search">
                        <input type="text" id="mentionSearch" placeholder="搜索人员...">
                    </div>
                    <div class="mention-list" id="mentionList"></div>
                </div>
            </div>
        </div>
        
        <div id="mentionedPersonsSection" style="display: none;">
            <label>抄送人员：</label>
            <div id="mentionedPersons" class="mentioned-persons"></div>
        </div>
        
        <button onclick="showResult()">查看结果</button>
        <div id="result" style="margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 4px;"></div>
    </div>

    <script>
        // @联想功能相关变量
        let personnelList = ['张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十'];
        let currentMentionStart = -1;
        let selectedIndex = -1;
        let mentionedPersons = new Set();

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化@联想功能...');
            initMentionFeature();
        });

        // 初始化@联想功能
        function initMentionFeature() {
            console.log('正在初始化@联想功能...');
            
            const textarea = document.getElementById('testContent');
            const dropdown = document.getElementById('mentionDropdown');
            const searchInput = document.getElementById('mentionSearch');
            const mentionList = document.getElementById('mentionList');
            
            // 检查元素是否存在
            if (!textarea) {
                console.error('未找到testContent元素');
                return;
            }
            if (!dropdown) {
                console.error('未找到mentionDropdown元素');
                return;
            }
            
            console.log('DOM元素绑定成功，开始监听事件...');
            
            // 监听textarea的输入事件
            textarea.addEventListener('input', function(e) {
                console.log('检测到输入事件:', e.target.value);
                handleTextareaInput(e);
            });
            
            // 监听键盘事件
            textarea.addEventListener('keydown', function(e) {
                if (dropdown.style.display !== 'none') {
                    handleDropdownKeydown(e);
                }
            });
            
            // 监听搜索框输入
            searchInput.addEventListener('input', function(e) {
                filterPersonnelList(e.target.value);
            });
            
            // 点击其他地方关闭下拉列表
            document.addEventListener('click', function(e) {
                if (!dropdown.contains(e.target) && e.target !== textarea) {
                    hideDropdown();
                }
            });
        }

        // 处理textarea输入
        function handleTextareaInput(e) {
            const textarea = e.target;
            const value = textarea.value;
            const cursorPos = textarea.selectionStart;
            
            console.log('处理输入:', value, '光标位置:', cursorPos);
            
            // 查找最后一个@符号的位置
            const lastAtIndex = value.lastIndexOf('@', cursorPos - 1);
            console.log('最后@位置:', lastAtIndex);
            
            if (lastAtIndex !== -1) {
                // 检查@后面是否只有字母、数字、中文字符
                const afterAt = value.substring(lastAtIndex + 1, cursorPos);
                const isValidQuery = /^[\u4e00-\u9fa5a-zA-Z0-9]*$/.test(afterAt);
                
                console.log('@后内容:', afterAt, '是否有效:', isValidQuery);
                
                if (isValidQuery && (lastAtIndex === 0 || /\s/.test(value.charAt(lastAtIndex - 1)))) {
                    // 显示下拉列表
                    currentMentionStart = lastAtIndex;
                    console.log('显示下拉列表，查询:', afterAt);
                    showDropdown(textarea, afterAt);
                    return;
                }
            }
            
            // 隐藏下拉列表
            hideDropdown();
        }

        // 显示下拉列表
        function showDropdown(textarea, query) {
            console.log('显示下拉列表:', query);
            const dropdown = document.getElementById('mentionDropdown');
            const searchInput = document.getElementById('mentionSearch');
            
            // 计算下拉列表位置
            const rect = textarea.getBoundingClientRect();
            dropdown.style.display = 'block';
            dropdown.style.left = rect.left + 'px';
            dropdown.style.top = (rect.bottom + window.scrollY) + 'px';
            dropdown.style.width = Math.max(250, rect.width) + 'px';
            
            // 设置搜索框值并过滤
            searchInput.value = query;
            filterPersonnelList(query);
            
            // 聚焦搜索框
            setTimeout(() => searchInput.focus(), 10);
        }

        // 隐藏下拉列表
        function hideDropdown() {
            const dropdown = document.getElementById('mentionDropdown');
            dropdown.style.display = 'none';
            currentMentionStart = -1;
            selectedIndex = -1;
        }

        // 过滤人员列表
        function filterPersonnelList(query) {
            console.log('过滤人员列表:', query);
            const mentionList = document.getElementById('mentionList');
            const filteredList = personnelList.filter(person => 
                person.toLowerCase().includes(query.toLowerCase())
            );
            
            mentionList.innerHTML = '';
            selectedIndex = -1;
            
            if (filteredList.length === 0) {
                mentionList.innerHTML = '<div class="mention-item" style="color: #999;">未找到匹配的人员</div>';
                return;
            }
            
            filteredList.forEach((person, index) => {
                const item = document.createElement('div');
                item.className = 'mention-item';
                item.innerHTML = `
                    <div class="mention-avatar">${person.charAt(0)}</div>
                    <div class="mention-name">${person}</div>
                `;
                
                item.addEventListener('click', function() {
                    selectPerson(person);
                });
                
                mentionList.appendChild(item);
            });
        }

        // 处理下拉列表键盘事件
        function handleDropdownKeydown(e) {
            const items = document.querySelectorAll('.mention-item');
            const validItems = Array.from(items).filter(item => 
                !item.textContent.includes('未找到匹配的人员')
            );
            
            if (validItems.length === 0) return;
            
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, validItems.length - 1);
                    updateSelection(validItems);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, 0);
                    updateSelection(validItems);
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (selectedIndex >= 0 && selectedIndex < validItems.length) {
                        const selectedPerson = validItems[selectedIndex].querySelector('.mention-name').textContent;
                        selectPerson(selectedPerson);
                    }
                    break;
                case 'Escape':
                    e.preventDefault();
                    hideDropdown();
                    document.getElementById('testContent').focus();
                    break;
            }
        }

        // 更新选中状态
        function updateSelection(items) {
            items.forEach((item, index) => {
                item.classList.toggle('active', index === selectedIndex);
            });
            
            // 滚动到选中项
            if (selectedIndex >= 0 && selectedIndex < items.length) {
                items[selectedIndex].scrollIntoView({ block: 'nearest' });
            }
        }

        // 选择人员
        function selectPerson(person) {
            console.log('选择人员:', person);
            const textarea = document.getElementById('testContent');
            const value = textarea.value;
            const cursorPos = textarea.selectionStart;
            
            // 替换@查询为选中的人员
            const beforeMention = value.substring(0, currentMentionStart);
            const afterCursor = value.substring(cursorPos);
            const newValue = beforeMention + '@' + person + ' ' + afterCursor;
            
            textarea.value = newValue;
            
            // 设置光标位置
            const newCursorPos = beforeMention.length + person.length + 2;
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            
            // 添加到抄送列表
            addMentionedPerson(person);
            
            // 隐藏下拉列表
            hideDropdown();
            
            // 聚焦回textarea
            textarea.focus();
        }

        // 添加抄送人员
        function addMentionedPerson(person) {
            if (mentionedPersons.has(person)) return;
            
            mentionedPersons.add(person);
            updateMentionedPersonsDisplay();
        }

        // 移除抄送人员
        function removeMentionedPerson(person) {
            mentionedPersons.delete(person);
            updateMentionedPersonsDisplay();
            
            // 从textarea中移除@提及
            const textarea = document.getElementById('testContent');
            const value = textarea.value;
            const regex = new RegExp('@' + person.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '\\s*', 'g');
            textarea.value = value.replace(regex, '');
        }

        // 更新抄送人员显示
        function updateMentionedPersonsDisplay() {
            const container = document.getElementById('mentionedPersons');
            const section = document.getElementById('mentionedPersonsSection');
            
            if (mentionedPersons.size === 0) {
                section.style.display = 'none';
                return;
            }
            
            section.style.display = 'block';
            container.innerHTML = '';
            
            Array.from(mentionedPersons).forEach(person => {
                const personEl = document.createElement('div');
                personEl.className = 'mentioned-person';
                personEl.innerHTML = `
                    ${person}
                    <span class="remove-btn" onclick="removeMentionedPerson('${person}')">&times;</span>
                `;
                container.appendChild(personEl);
            });
        }

        // 显示结果
        function showResult() {
            const content = document.getElementById('testContent').value;
            const mentioned = Array.from(mentionedPersons);
            const result = document.getElementById('result');
            
            result.innerHTML = `
                <h3>测试结果：</h3>
                <p><strong>文本内容：</strong> ${content}</p>
                <p><strong>抄送人员：</strong> ${mentioned.join(', ')}</p>
                <p><strong>抄送人员数量：</strong> ${mentioned.length}</p>
            `;
        }
    </script>
</body>
</html>