package com.mylog.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mylog.model.workflow.ApprovalRecord;
import com.mylog.model.workflow.ApprovalRecord.ApprovalAction;
import com.mylog.model.workflow.WorkflowInstance;
import com.mylog.model.workflow.WorkflowInstance.WorkflowStatus;
import com.mylog.model.workflow.WorkflowStep;
import com.mylog.repository.workflow.WorkflowInstanceRepository;
import com.mylog.service.ApprovalRecordService;
import com.mylog.service.PersonnelStatusService;
import com.mylog.service.WorkflowInstanceService;
import com.mylog.service.WorkflowStepService;
import com.mylog.service.TaskService;
import com.mylog.service.UserService;
import com.mylog.service.MessageService;
import com.mylog.util.WeixinMessageUtil;
import com.mylog.model.user.User;
import com.mylog.model.Message;

/**
 * 流程实例服务实现类
 */
@Service
public class WorkflowInstanceServiceImpl implements WorkflowInstanceService {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowInstanceServiceImpl.class);

    @Autowired
    private WorkflowInstanceRepository instanceRepository;

    @Autowired
    private WorkflowStepService stepService;

    @Autowired
    private ApprovalRecordService recordService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private PersonnelStatusService personnelStatusService;

    @Autowired
    private UserService userService;

    @Autowired
    private WeixinMessageUtil weixinMessageUtil;

    @Autowired
    private MessageService messageService;

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowInstance> findAllInstances() {
        return instanceRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkflowInstance> findAllInstances(Pageable pageable) {
        return instanceRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowInstance> findAllInstancesByNativeQuery() {
        logger.info("使用原生SQL查询所有流程实例");

        try {
            // 使用实现的原生SQL查询方法
            List<WorkflowInstance> instances = instanceRepository.findAllByNativeQuery();
            logger.info("原生SQL查询结果: 记录数={}", instances.size());

            // 过滤掉null元素并创建新列表
            List<WorkflowInstance> filteredInstances = new java.util.ArrayList<>();
            for (WorkflowInstance instance : instances) {
                if (instance != null) {
                    filteredInstances.add(instance);
                } else {
                    logger.warn("发现空的实例对象，已被过滤掉");
                }
            }

            // 使用过滤后的列表替换原列表
            instances = filteredInstances;

            // 打印每个实例的详细信息，用于调试
            for (WorkflowInstance instance : instances) {
                logger.debug("实例ID: {}, 标题: {}, 状态: {}, 发起人: {}",
                        instance.getInstanceId(),
                        instance.getTitle(),
                        instance.getStatus(),
                        instance.getInitiator());
            }

            return instances;
        } catch (Exception e) {
            logger.error("使用原生SQL查询流程实例时出错: {}", e.getMessage(), e);
            // 出错时返回空列表而不是抛出异常，确保界面不会崩溃
            return new java.util.ArrayList<>();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<WorkflowInstance> findInstanceById(Long id) {
        return instanceRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowInstance> findInstancesByTemplateId(Long templateId) {
        return instanceRepository.findByTemplateTemplateId(templateId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowInstance> findInstancesByStatus(WorkflowStatus status) {
        return instanceRepository.findByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowInstance> findInstancesByInitiator(String initiator) {
        return instanceRepository.findByInitiator(initiator);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowInstance> findInstancesByInitiatorAndStatus(String initiator, WorkflowStatus status) {
        return instanceRepository.findByInitiatorAndStatus(initiator, status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowInstance> findInstancesByBusiness(String businessType, Long businessId) {
        return instanceRepository.findByBusinessTypeAndBusinessId(businessType, businessId);
    }

    @Override
    @Transactional
    public WorkflowInstance createInstance(WorkflowInstance instance) {
        // 设置创建时间和状态
        instance.setCreatedDateTime(LocalDateTime.now());
        instance.setStatus(WorkflowStatus.DRAFT);

        // 设置步骤数量并获取第一步审批人
        if (instance.getTemplate() != null) {
            List<WorkflowStep> steps = stepService.findStepsByTemplateId(instance.getTemplate().getTemplateId());
            instance.setStepCount(steps.size());

            // 从模板中读取第一步的审批人信息，并设置到流程实例中
            if (!steps.isEmpty()) {
                // 按照步骤顺序排序
                steps.sort((s1, s2) -> s1.getStepOrder().compareTo(s2.getStepOrder()));

                // 获取第一步
                WorkflowStep firstStep = steps.get(0);
                if (firstStep != null && firstStep.getApproverConfig() != null
                        && !firstStep.getApproverConfig().isEmpty()) {
                    // 检查是否含有${initiator}标记，如果有则替换为发起人
                    String approverConfig = firstStep.getApproverConfig();
                    if (approverConfig.contains("${initiator}")) {
                        approverConfig = approverConfig.replace("${initiator}", instance.getInitiator());
                        logger.info("将审批人配置中的${initiator}替换为发起人: {}", instance.getInitiator());
                    }

                    // 将第一步的审批人配置设置为当前审批人
                    instance.setCurrentApprover(approverConfig);
                    logger.info("已从模板中读取第一步审批人配置: {}", approverConfig);
                }
            }
        }

        return instanceRepository.save(instance);
    }

    @Override
    @Transactional
    public WorkflowInstance submitInstance(Long instanceId, String username, String comment) {
        return submitInstance(instanceId, username, comment, null);
    }

    @Override
    @Transactional
    public WorkflowInstance submitInstance(Long instanceId, String username, String comment, String attachments) {
        return submitInstance(instanceId, username, comment, attachments, null);
    }

    @Override
    @Transactional
    public WorkflowInstance submitInstance(Long instanceId, String username, String comment, String attachments,
            String nextApprover) {
        // 获取实例
        Optional<WorkflowInstance> instanceOpt = instanceRepository.findById(instanceId);
        if (!instanceOpt.isPresent()) {
            throw new IllegalArgumentException("流程实例不存在: " + instanceId);
        }

        WorkflowInstance instance = instanceOpt.get();

        // 检查状态
        if (instance.getStatus() != WorkflowStatus.DRAFT) {
            throw new IllegalStateException("流程状态错误，无法提交");
        }

        // 检查是否是发起人
        if (!username.equals(instance.getInitiator())) {
            throw new IllegalStateException("只有发起人才能提交流程");
        }

        // 查找第一个审批步骤
        List<WorkflowStep> steps = stepService.findStepsByTemplateId(instance.getTemplate().getTemplateId());
        if (steps.isEmpty()) {
            throw new IllegalStateException("流程模板未配置审批步骤");
        }

        // 按照顺序排序
        steps.sort((s1, s2) -> s1.getStepOrder().compareTo(s2.getStepOrder()));

        // 获取第一个审批步骤
        WorkflowStep firstStep = steps.get(0);

        // 设置流程状态为处理中
        instance.setStatus(WorkflowStatus.PROCESSING);

        // 设置当前步骤ID
        instance.setCurrentStepId(firstStep.getStepId());

        // 设置提交时间
        instance.setSubmittedDateTime(LocalDateTime.now());

        // 判断第一步是否需要动态指定审批人
        boolean isDynamicApprover = firstStep.getApproverConfig() == null
                || firstStep.getApproverConfig().isEmpty()
                || "DYNAMIC".equals(firstStep.getApproverType().name());

        // 设置当前审批人
        if (nextApprover != null && !nextApprover.isEmpty()) {
            // 如果指定了下一步审批人，使用指定的审批人
            instance.setCurrentApprover(nextApprover);
            logger.info("使用指定的下一步审批人: {}", nextApprover);
        } else if (instance.getCurrentApprover() == null || instance.getCurrentApprover().isEmpty()) {
            // 如果没有指定审批人且实例中也没有审批人，则使用第一个步骤的审批人配置
            String approverConfig = firstStep.getApproverConfig();
            // 检查是否含有${initiator}标记，如果有则替换为发起人
            if (approverConfig != null && approverConfig.contains("${initiator}")) {
                approverConfig = approverConfig.replace("${initiator}", instance.getInitiator());
                logger.info("将第一步审批人配置中的${initiator}替换为发起人: {}", instance.getInitiator());
            }
            instance.setCurrentApprover(approverConfig);

            logger.info("未指定下一步审批人，使用第一步审批人配置: {}", approverConfig);

            // 如果是动态审批人但没有传入nextApprover参数，需要记录此状态，以便前端判断
            if (isDynamicApprover) {
                logger.info("第一步需要动态指定审批人，但未传入审批人参数");
                // 这里可以设置特殊标记，或者在返回时由前端判断
            }

        }

        // 如果当前用户不等于下一步审批人，则发送微信通知
        sendWorkflowNotification(instance, username, "提交");

        // 创建提交记录
        ApprovalRecord record = new ApprovalRecord();
        record.setInstance(instance);
        record.setStep(firstStep); // 设置关联步骤为第一步
        record.setAction(ApprovalAction.SUBMIT);
        record.setApprover(username);
        record.setComment(comment);
        record.setAttachments(attachments);
        record.setCreatedDateTime(LocalDateTime.now());
        recordService.saveRecord(record);

        // 保存更新后的实例
        return instanceRepository.save(instance);
    }

    @Override
    @Transactional
    public WorkflowInstance approveInstance(Long instanceId, String username, String comment, String attachments,
            String nextApprover) {
        // 获取实例
        Optional<WorkflowInstance> instanceOpt = instanceRepository.findById(instanceId);
        if (!instanceOpt.isPresent()) {
            throw new IllegalArgumentException("流程实例不存在: " + instanceId);
        }

        WorkflowInstance instance = instanceOpt.get();

        // 检查状态
        if (instance.getStatus() != WorkflowStatus.PROCESSING) {
            throw new IllegalStateException("流程状态错误，无法审批");
        }

        // 检查是否是当前审批人
        if (!username.equals(instance.getCurrentApprover())) {
            throw new IllegalStateException("只有当前审批人才能审批流程");
        }

        // 获取当前步骤
        Optional<WorkflowStep> currentStepOpt = stepService.findStepById(instance.getCurrentStepId());
        if (!currentStepOpt.isPresent()) {
            throw new IllegalStateException("当前步骤不存在");
        }

        WorkflowStep currentStep = currentStepOpt.get();

        // 获取所有步骤并按顺序排序
        List<WorkflowStep> steps = stepService.findStepsByTemplateId(instance.getTemplate().getTemplateId());
        steps.sort((s1, s2) -> s1.getStepOrder().compareTo(s2.getStepOrder()));

        // 查找当前步骤在步骤列表中的索引
        int currentIndex = -1;
        for (int i = 0; i < steps.size(); i++) {
            if (steps.get(i).getStepId().equals(currentStep.getStepId())) {
                currentIndex = i;
                break;
            }
        }

        if (currentIndex == -1) {
            throw new IllegalStateException("当前步骤不在步骤列表中");
        }
        // 检查是否是最后一个步骤
        boolean isLastStep = currentIndex == steps.size() - 1;

        // 创建审批记录
        ApprovalRecord record = new ApprovalRecord();
        record.setInstance(instance);
        record.setStep(currentStep); // 设置关联步骤
        record.setAction(ApprovalAction.APPROVE);
        record.setApprover(username);
        record.setComment(comment);
        record.setAttachments(attachments);
        record.setCreatedDateTime(LocalDateTime.now());
        recordService.saveRecord(record);
        if (isLastStep) {
            // 如果是最后一个步骤，流程完成
            instance.setStatus(WorkflowStatus.APPROVED);
            instance.setCurrentApprover(null);
            instance.setCompletedDateTime(LocalDateTime.now());
            logger.info("流程审批完成: {}", instance.getInstanceId());

            // 当流程审批通过时，根据流程模板类型更新人员状态
            String templateName = instance.getTemplate().getTemplateName();
            String initiator = instance.getInitiator();
            String startLocation = instance.getStartLocation();
            String endLocation = instance.getEndLocation();
            String startTime = instance.getStartTime();
            String endTime = instance.getEndTime();            // 调用PersonnelStatusService更新人员状态
            personnelStatusService.updatePersonnelStatusAfterWorkflowApproved(
                    initiator, templateName, startLocation, endLocation, startTime, endTime, instance.getInstanceId());

            logger.info("流程审批完成后更新了人员状态信息 - 模板: {}, 发起人: {}, 流程实例ID: {}", templateName, initiator, instance.getInstanceId());
            
            // 如果业务类型是任务，则更新任务状态
            if ("任务".equals(instance.getBusinessType()) && instance.getBusinessId() != null) {
                String taskCompletionStatus = instance.getRemark3(); // 从remark3获取任务完成状态
                if (taskCompletionStatus == null || taskCompletionStatus.trim().isEmpty()) {
                    taskCompletionStatus = "completed"; // 默认为完成状态
                }
                
                try {
                    if ("paused".equals(taskCompletionStatus)) {
                        // 暂停任务
                        taskService.pauseTask(instance.getBusinessId());
                        logger.info("流程审批完成后，已将任务 {} 标记为已暂停", instance.getBusinessId());
                    } else {
                        // 完成任务
                        taskService.completeTask(instance.getBusinessId());
                        logger.info("流程审批完成后，已将任务 {} 标记为已完成", instance.getBusinessId());
                    }
                } catch (Exception e) {
                    logger.error("流程审批完成后更新任务状态时出错: {}", e.getMessage());
                    // 不抛出异常，避免影响工作流完成
                }
            }
        } else {
            // 不是最后一个步骤，进入下一步
            WorkflowStep nextStep = steps.get(currentIndex + 1);
            instance.setCurrentStepId(nextStep.getStepId());

            // 如果指定了下一步审批人，使用指定的审批人
            if (nextApprover != null && !nextApprover.isEmpty()) {
                instance.setCurrentApprover(nextApprover);
            } else {
                // 否则使用步骤中配置的审批人
                String approverConfig = nextStep.getApproverConfig();
                // 检查是否含有${initiator}标记，如果有则替换为发起人
                if (approverConfig != null && approverConfig.contains("${initiator}")) {
                    approverConfig = approverConfig.replace("${initiator}", instance.getInitiator());
                    logger.info("将下一步审批人配置中的${initiator}替换为发起人: {}", instance.getInitiator());
                }
                instance.setCurrentApprover(approverConfig);

            }

            // 如果当前用户不等于下一步审批人，则发送微信通知

            sendWorkflowNotification(instance, username, "审批");

            logger.info("流程进入下一步: {}, 审批人: {}", nextStep.getStepName(), instance.getCurrentApprover());
        }

        // 保存更新后的实例
        return instanceRepository.save(instance);
    }

    @Override
    @Transactional
    public WorkflowInstance rejectInstance(Long instanceId, String username, String comment, String attachments) {
        // 获取实例
        Optional<WorkflowInstance> instanceOpt = instanceRepository.findById(instanceId);
        if (!instanceOpt.isPresent()) {
            throw new IllegalArgumentException("流程实例不存在: " + instanceId);
        }

        WorkflowInstance instance = instanceOpt.get();

        // 检查状态
        if (instance.getStatus() != WorkflowStatus.PROCESSING) {
            throw new IllegalStateException("流程状态错误，无法拒绝");
        }

        // 检查是否是当前审批人
        if (!username.equals(instance.getCurrentApprover())) {
            throw new IllegalStateException("只有当前审批人才能拒绝流程");
        }

        // 获取当前步骤
        Optional<WorkflowStep> currentStepOpt = stepService.findStepById(instance.getCurrentStepId());
        if (!currentStepOpt.isPresent()) {
            throw new IllegalStateException("当前步骤不存在");
        }

        WorkflowStep currentStep = currentStepOpt.get();
        // 检查步骤是否允许拒绝
        if (!currentStep.getAllowReject()) {
            throw new IllegalStateException("当前步骤不允许拒绝");
        }

        // 创建拒绝记录
        ApprovalRecord record = new ApprovalRecord();
        record.setInstance(instance);
        record.setStep(currentStep); // 设置关联步骤
        record.setAction(ApprovalAction.REJECT);
        record.setApprover(username);
        record.setComment(comment);
        record.setAttachments(attachments);
        record.setCreatedDateTime(LocalDateTime.now());
        recordService.saveRecord(record);

        // 设置流程状态为拒绝
        instance.setStatus(WorkflowStatus.REJECTED);
        instance.setCurrentApprover(null);
        instance.setCompletedDateTime(LocalDateTime.now());

        // 保存更新后的实例
        return instanceRepository.save(instance);
    }

    @Override
    @Transactional
    public WorkflowInstance transferInstance(Long instanceId, String fromUsername, String toUsername, String comment) {
        // 获取实例
        Optional<WorkflowInstance> instanceOpt = instanceRepository.findById(instanceId);
        if (!instanceOpt.isPresent()) {
            throw new IllegalArgumentException("流程实例不存在: " + instanceId);
        }

        WorkflowInstance instance = instanceOpt.get();

        // 检查状态
        if (instance.getStatus() != WorkflowStatus.PROCESSING) {
            throw new IllegalStateException("流程状态错误，无法转交");
        }

        // 检查是否是当前审批人
        if (!fromUsername.equals(instance.getCurrentApprover())) {
            throw new IllegalStateException("只有当前审批人才能转交流程");
        }

        // 获取当前步骤
        Optional<WorkflowStep> currentStepOpt = stepService.findStepById(instance.getCurrentStepId());
        if (!currentStepOpt.isPresent()) {
            throw new IllegalStateException("当前步骤不存在");
        }

        WorkflowStep currentStep = currentStepOpt.get();
        // 检查步骤是否允许转交
        if (!currentStep.getAllowTransfer()) {
            throw new IllegalStateException("当前步骤不允许转交");
        }

        // 创建转交记录
        ApprovalRecord record = new ApprovalRecord();
        record.setInstance(instance);
        record.setStep(currentStep); // 设置关联步骤
        record.setAction(ApprovalAction.TRANSFER);
        record.setApprover(fromUsername);
        record.setComment(comment);
        record.setNextApprover(toUsername);
        record.setCreatedDateTime(LocalDateTime.now());
        recordService.saveRecord(record); // 更新当前审批人
        instance.setCurrentApprover(toUsername);

        // 发送微信通知给被转交的审批人
        sendWorkflowNotification(instance, fromUsername, "转交");
        logger.info("流程已从 {} 转交给 {}, 已发送微信通知", fromUsername, toUsername);

        // 保存更新后的实例
        return instanceRepository.save(instance);
    }

    @Override
    @Transactional
    public WorkflowInstance withdrawInstance(Long instanceId, String username, String comment) {
        // 获取实例
        Optional<WorkflowInstance> instanceOpt = instanceRepository.findById(instanceId);
        if (!instanceOpt.isPresent()) {
            throw new IllegalArgumentException("流程实例不存在: " + instanceId);
        }

        WorkflowInstance instance = instanceOpt.get();

        // 检查状态
        if (instance.getStatus() != WorkflowStatus.PROCESSING) {
            throw new IllegalStateException("流程状态错误，无法撤回");
        }
        // 检查是否是发起人
        if (!username.equals(instance.getInitiator())) {
            throw new IllegalStateException("只有发起人才能撤回流程");
        }

        // 获取当前步骤
        Optional<WorkflowStep> currentStepOpt = stepService.findStepById(instance.getCurrentStepId());
        WorkflowStep currentStep = currentStepOpt.orElse(null);

        // 创建撤回记录
        ApprovalRecord record = new ApprovalRecord();
        record.setInstance(instance);
        record.setStep(currentStep); // 设置关联步骤
        record.setAction(ApprovalAction.WITHDRAW);
        record.setApprover(username);
        record.setComment(comment);
        record.setCreatedDateTime(LocalDateTime.now());
        recordService.saveRecord(record);

        // 设置流程状态为撤回
        instance.setStatus(WorkflowStatus.CANCELED);
        instance.setCurrentApprover(null);
        instance.setCompletedDateTime(LocalDateTime.now());        // 如果是任务类型的业务，并且有有效的业务ID，需要重置任务的审批状态
        if ("任务".equals(instance.getBusinessType()) && instance.getBusinessId() != null) {
            try {
                // 使用已经注入的taskService
                taskService.updateTaskApprovalStatus(instance.getBusinessId(), 0, null, null, null);
                logger.info("已重置任务 {} 的审批状态和需要审批字段为0", instance.getBusinessId());
            } catch (Exception e) {
                logger.error("重置任务审批状态时出错: {}", e.getMessage(), e);
                // 继续执行，不影响流程撤回
            }
        }

        // 保存更新后的实例
        return instanceRepository.save(instance);
    }

    @Override
    @Transactional
    public WorkflowInstance terminateInstance(Long instanceId, String username, String comment) {
        // 获取实例
        Optional<WorkflowInstance> instanceOpt = instanceRepository.findById(instanceId);
        if (!instanceOpt.isPresent()) {
            throw new IllegalArgumentException("流程实例不存在: " + instanceId);
        }

        WorkflowInstance instance = instanceOpt.get();
        // 检查状态
        if (instance.getStatus() != WorkflowStatus.PROCESSING) {
            throw new IllegalStateException("流程状态错误，无法终止");
        }

        // 获取当前步骤
        Optional<WorkflowStep> currentStepOpt = stepService.findStepById(instance.getCurrentStepId());
        WorkflowStep currentStep = currentStepOpt.orElse(null);

        // 创建终止记录
        ApprovalRecord record = new ApprovalRecord();
        record.setInstance(instance);
        record.setStep(currentStep); // 设置关联步骤
        record.setAction(ApprovalAction.TERMINATE);
        record.setApprover(username);
        record.setComment(comment);
        record.setCreatedDateTime(LocalDateTime.now());
        recordService.saveRecord(record);

        // 设置流程状态为终止
        instance.setStatus(WorkflowStatus.TERMINATED);
        instance.setCurrentApprover(null);
        instance.setCompletedDateTime(LocalDateTime.now());

        // 保存更新后的实例
        return instanceRepository.save(instance);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowInstance> findTodoTasks(String username) {
        return instanceRepository.findTodoTasks(username);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkflowInstance> findTodoTasks(String approver, Pageable pageable) {
        return instanceRepository.findTodoTasks(approver, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowInstance> findDoneTasks(String username) {
        return instanceRepository.findDoneTasks(username);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkflowInstance> findDoneTasks(String approver, Pageable pageable) {
        return instanceRepository.findDoneTasks(approver, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isNextStepDynamicApprover(WorkflowInstance instance) {
        if (instance == null || instance.getCurrentStepId() == null) {
            return false;
        }

        // 获取当前步骤
        Optional<WorkflowStep> currentStepOpt = stepService.findStepById(instance.getCurrentStepId());
        if (!currentStepOpt.isPresent()) {
            return false;
        }

        WorkflowStep currentStep = currentStepOpt.get();

        // 获取所有步骤并按顺序排序
        List<WorkflowStep> steps = stepService.findStepsByTemplateId(instance.getTemplate().getTemplateId());
        steps.sort((s1, s2) -> s1.getStepOrder().compareTo(s2.getStepOrder()));

        // 查找当前步骤在步骤列表中的索引
        int currentIndex = -1;
        for (int i = 0; i < steps.size(); i++) {
            if (steps.get(i).getStepId().equals(currentStep.getStepId())) {
                currentIndex = i;
                break;
            }
        }

        if (currentIndex == -1 || currentIndex >= steps.size() - 1) {
            return false; // 当前步骤不在列表中或已是最后一步
        }

        // 获取下一步骤
        WorkflowStep nextStep = steps.get(currentIndex + 1);

        // 判断下一步是否需要动态指定审批人
        // 如果审批人配置为空或特定标记，则需要动态指定
        return nextStep.getApproverConfig() == null
                || nextStep.getApproverConfig().isEmpty()
                || nextStep.getApproverType().name().equals("DYNAMIC");
    }    @Override
    @Transactional(readOnly = true)
    public Page<WorkflowInstance> searchInstances(String keyword, WorkflowStatus status, String initiator,
            Long templateId, String businessType, String businessIdStr, Pageable pageable) {
        logger.info("执行搜索 - 关键词: '{}', 状态: {}, 发起人: '{}', 模板ID: {}, 业务类型: '{}', 业务ID: '{}'", 
                 keyword, status, initiator, templateId, businessType, businessIdStr);
        
        try {
            Page<WorkflowInstance> result = instanceRepository.searchInstances(keyword, status, initiator, templateId, businessType, businessIdStr,
                pageable);
            logger.info("搜索完成，结果数: {}", result.getTotalElements());
            return result;
        } catch (Exception e) {
            logger.error("搜索过程中发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public WorkflowInstance updateInstance(WorkflowInstance instance) {
        // 检查实例是否存在
        if (instance == null || instance.getInstanceId() == null) {
            throw new IllegalArgumentException("流程实例不能为空且必须有ID");
        }

        Optional<WorkflowInstance> existingInstance = instanceRepository.findById(instance.getInstanceId());
        if (!existingInstance.isPresent()) {
            throw new IllegalArgumentException("流程实例不存在: " + instance.getInstanceId());
        }

        // 保存更新后的实例
        return instanceRepository.save(instance);
    }

    @Override
    @Transactional(readOnly = true)
    public long countProcessingInstances() {
        try {
            return instanceRepository.countByStatus(WorkflowStatus.PROCESSING);
        } catch (Exception e) {
            logger.error("统计处理中流程实例数量时出错: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 发送流程审批通知
     *
     * @param instance   流程实例
     * @param fromUser   操作人（发起人或上一步审批人）
     * @param actionType 操作类型（提交、审批等）
     */
    private void sendWorkflowNotification(WorkflowInstance instance, String fromUser, String actionType) {
        try {
            String nextApprover = instance.getCurrentApprover();
            if (nextApprover == null || nextApprover.isEmpty()) {
                logger.warn("下一步审批人为空，无法发送通知");
                return;
            }

            if (fromUser.equals(instance.getCurrentApprover())) {
                logger.info("当前用户 {} 是下一步审批人，跳过发送通知", fromUser);
                return;
            }

            // 查找下一步审批人的微信ID
            String weixinID = "";
            Optional<User> nextApproverUser = userService.findUserByUsername(nextApprover);
            if (nextApproverUser.isPresent() && nextApproverUser.get().getWeixinID() != null
                    && !nextApproverUser.get().getWeixinID().isEmpty()) {
                weixinID = nextApproverUser.get().getWeixinID();
                logger.debug("找到用户 {} 的微信ID: {}", nextApprover, weixinID);
            } else {
                logger.warn("未找到用户 {} 的微信ID，使用用户名代替", nextApprover);
                weixinID = nextApprover;
            }

            // 构建微信消息内容
            StringBuilder contentBuilder = new StringBuilder();

            if ("提交".equals(actionType)) {
                contentBuilder.append("<@").append(weixinID).append("> ，您有新的流程需要审批\n\n");
            } else {
                contentBuilder.append("<@").append(weixinID).append("> ，流程已流转到您，请进行审批\n\n");
            }

            String instanceMarkdownLink = String.format("[%s](https://prj.cpolar.cn/workflow/instances/%d)", 
                instance.getTitle(), instance.getInstanceId());
            contentBuilder.append("**流程标题**: ").append(instanceMarkdownLink).append("\n");

            if ("提交".equals(actionType)) {
                contentBuilder.append("**上一步操作**: 发起人提交").append("\n");
            } else {
                contentBuilder.append("**上一步操作**: ").append(fromUser).append(" 审批通过").append("\n");
            }            // contentBuilder.append("**当前步骤**: ").append("第").append(getCurrentStepOrder(instance)).append("步")
            //         .append("\n");
            // if (instance.getTemplate() != null) {
            //     contentBuilder.append("**流程类型**: ").append(instance.getTemplate().getTemplateName()).append("\n");
            // }
            contentBuilder.append("**发起人**: ").append(instance.getInitiator()).append("  ");
            
            // 格式化提交时间为 yyyy-MM-dd HH:mm:ss 格式
            LocalDateTime submittedDateTime = instance.getSubmittedDateTime();
            String formattedSubmittedTime = submittedDateTime != null ? 
                submittedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "-";
            contentBuilder.append("**提交时间**: ").append(formattedSubmittedTime).append(" ");
            //contentBuilder.append("请及时登录系统进行审批处理。"); // 发送企业微信消息
            List<String> mentionList = new java.util.ArrayList<>();
            mentionList.add(weixinID);
            weixinMessageUtil.sendWeixinMessage("流程审批通知", contentBuilder.toString(), mentionList);

            // 同时发送系统内部消息
            Message systemMessage = new Message();
            systemMessage.setReceiver(nextApprover);
            systemMessage.setMessageTitle("流程审批通知");
            String instanceLink = String.format("<a href=\"/workflow/instances/%d\" target=\"_blank\">%s</a>", 
                instance.getInstanceId(), instance.getTitle());
            if ("提交".equals(actionType)) {
                systemMessage.setMessageContent("您有新的流程 " + instanceLink + " 需要审批，请及时处理。");
            } else {
                systemMessage.setMessageContent("流程 " + instanceLink + " 已流转到您，请进行审批处理。");
            }
            systemMessage.setRelatedType("WorkflowInstance");
            systemMessage.setRelatedId(instance.getInstanceId());
            systemMessage.setRead(false);
            systemMessage.setCreatedDate(LocalDateTime.now());
            messageService.saveMessage(systemMessage);

            logger.info("已发送流程{}通知给用户: {}", actionType, nextApprover);

        } catch (Exception e) {
            logger.error("发送流程{}通知失败: {}", actionType, e.getMessage(), e);
        }
    }

    /**
     * 获取当前步骤序号
     *
     * @param instance 流程实例
     * @return 当前步骤序号
     */
    private int getCurrentStepOrder(WorkflowInstance instance) {
        try {
            if (instance.getCurrentStepId() == null) {
                return 1;
            }

            Optional<WorkflowStep> currentStepOpt = stepService.findStepById(instance.getCurrentStepId());
            if (currentStepOpt.isPresent()) {
                return currentStepOpt.get().getStepOrder();
            }
        } catch (Exception e) {
            logger.warn("获取当前步骤序号失败: {}", e.getMessage(), e);
        }
        return 1;
    }
      @Override
    @Transactional
    public boolean deleteInstance(Long instanceId) {
        logger.info("开始删除流程实例: {}", instanceId);
        
        try {
            // 查找流程实例
            Optional<WorkflowInstance> instanceOpt = findInstanceById(instanceId);
            if (!instanceOpt.isPresent()) {
                logger.warn("流程实例不存在: {}", instanceId);
                return false;
            }
            
            WorkflowInstance instance = instanceOpt.get();
              // 处理关联的业务对象（如任务）
        if (instance.getBusinessType() != null && instance.getBusinessType().equals("任务") && instance.getBusinessId() != null) {
                // 将任务的审批状态更新为不需要审批
                taskService.updateTaskApprovalStatus(instance.getBusinessId(), 0, null, null, null);
                logger.info("更新任务审批状态为不需要审批: {}", instance.getBusinessId());
            }
            
            // 删除所有关联的审批记录
            List<ApprovalRecord> records = recordService.findRecordsByInstanceId(instanceId);
            for (ApprovalRecord record : records) {
                // 这里假设ApprovalRecordRepository有deleteById方法
                recordService.deleteRecord(record.getRecordId());
                logger.info("删除审批记录: {}", record.getRecordId());
            }
            
            // 删除流程实例
            instanceRepository.deleteById(instanceId);
            logger.info("成功删除流程实例: {}", instanceId);
            
            return true;
        } catch (Exception e) {
            logger.error("删除流程实例时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean deleteDraftInstance(Long instanceId, String initiator) {
        logger.info("发起人 {} 尝试删除草稿流程实例: {}", initiator, instanceId);
        
        try {
            // 查找流程实例
            Optional<WorkflowInstance> instanceOpt = findInstanceById(instanceId);
            if (!instanceOpt.isPresent()) {
                logger.warn("流程实例不存在: {}", instanceId);
                throw new IllegalArgumentException("流程实例不存在");
            }
            
            WorkflowInstance instance = instanceOpt.get();
            
            // 验证状态：只能删除草稿状态的流程
            if (instance.getStatus() != WorkflowStatus.DRAFT) {
                logger.warn("只能删除草稿状态的流程，当前状态: {}", instance.getStatus());
                throw new IllegalStateException("只能删除草稿状态的流程");
            }
            
            // 验证权限：只有发起人可以删除自己的草稿
            if (!initiator.equals(instance.getInitiator())) {
                logger.warn("只有发起人才能删除草稿流程，发起人: {}, 操作人: {}", instance.getInitiator(), initiator);
                throw new IllegalStateException("只有发起人才能删除自己的草稿流程");
            }
            
            // 处理关联的业务对象（如任务）
            if (instance.getBusinessType() != null && instance.getBusinessType().equals("任务") && instance.getBusinessId() != null) {
                // 将任务的审批状态更新为不需要审批
                taskService.updateTaskApprovalStatus(instance.getBusinessId(), 0, null, null, null);
                logger.info("更新任务审批状态为不需要审批: {}", instance.getBusinessId());
            }
            
            // 删除所有关联的审批记录（草稿通常没有审批记录，但为了保险起见）
            List<ApprovalRecord> records = recordService.findRecordsByInstanceId(instanceId);
            for (ApprovalRecord record : records) {
                recordService.deleteRecord(record.getRecordId());
                logger.info("删除审批记录: {}", record.getRecordId());
            }
            
            // 删除流程实例
            instanceRepository.deleteById(instanceId);
            logger.info("成功删除草稿流程实例: {}", instanceId);
            
            return true;
        } catch (Exception e) {
            logger.error("删除草稿流程实例时出错: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public long countInstancesByInitiatorAndTitleKeywordInMonth(String initiator, String titleKeyword, int year, int month) {
        try {
            // 计算指定月份的开始和结束时间
            java.time.LocalDateTime startOfMonth = java.time.LocalDateTime.of(year, month, 1, 0, 0, 0);
            java.time.LocalDateTime endOfMonth = startOfMonth.plusMonths(1);
            
            // 转换为字符串格式，匹配数据库中的字符串字段
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String startDateStr = startOfMonth.format(formatter);
            String endDateStr = endOfMonth.format(formatter);
            
            logger.info("统计发起人 {} 在 {}-{:02d} 月发生包含关键词 '{}' 的流程数量", 
                    initiator, year, month, titleKeyword);
            logger.debug("查询时间范围: {} 到 {}", startDateStr, endDateStr);
            
            long count = instanceRepository.countByInitiatorAndTitleContainingAndSubmittedDateTimeBetween(
                    initiator, titleKeyword, startDateStr, endDateStr);
            
            logger.info("发起人 {} 在 {}-{:02d} 月发生包含关键词 '{}' 的流程数量: {}", 
                    initiator, year, month, titleKeyword, count);
            
            return count;
        } catch (Exception e) {
            logger.error("统计流程数量时出错: {}", e.getMessage(), e);
            return 0;
        }
    }
}
