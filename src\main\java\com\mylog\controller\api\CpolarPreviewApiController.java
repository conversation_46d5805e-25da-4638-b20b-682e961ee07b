package com.mylog.controller.api;

import com.mylog.controller.BaseController;
import com.mylog.model.Submit2;
import com.mylog.service.Submit2Service;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.service.impl.CpolarPreviewService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * cpolar预览文件API控制器
 * 为Office Web Viewer提供通过cpolar隧道访问本地文件的接口
 */
@RestController
@RequestMapping("/api/files")
public class CpolarPreviewApiController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(CpolarPreviewApiController.class);

    @Autowired
    private Submit2Service submit2Service;

    @Autowired
    private CpolarPreviewService cpolarPreviewService;

    @Autowired
    private UserActivityLogService activityLogService;

    @Autowired
    private UserService userService;

    @Value("${mylog.data.path:data}")
    private String dataPath;

    /**
     * cpolar预览文件端点
     * 用于Office Web Viewer访问，提供必要的CORS头部
     */
    @GetMapping("/cpolar-preview/{submitId}")
    public ResponseEntity<Resource> getCpolarPreviewFile(
            @PathVariable String submitId,
            @RequestParam String file,
            @RequestParam(required = false) String token) {

        logger.info("cpolar预览文件请求: submitId={}, file={}, hasToken={}", 
                   submitId, file, token != null);

        try {
            // 验证token
            if (!cpolarPreviewService.isValidPreviewToken(token, submitId, file)) {
                logger.warn("无效的预览token: submitId={}, file={}, token={}", submitId, file, token);
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .header("Access-Control-Allow-Origin", "*")
                    .body(null);
            }

            // 获取提交记录
            Long submitIdLong = Long.parseLong(submitId);
            Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitIdLong);
            if (!submitOpt.isPresent()) {
                logger.warn("提交记录不存在: submitId={}", submitId);
                return ResponseEntity.notFound()
                    .header("Access-Control-Allow-Origin", "*")
                    .build();
            }

            Submit2 submit = submitOpt.get();
            int fileNumber = Integer.parseInt(file);
            String filePath = getFilePath(submit, fileNumber);

            if (filePath == null || filePath.isEmpty()) {
                logger.warn("文件路径为空: submitId={}, fileNumber={}", submitId, fileNumber);
                return ResponseEntity.notFound()
                    .header("Access-Control-Allow-Origin", "*")
                    .build();
            }

            // 检查文件是否存在
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                Path relativePath = Paths.get(dataPath, filePath);
                if (Files.exists(relativePath)) {
                    path = relativePath;
                } else {
                    logger.warn("文件不存在: filePath={}, relativePath={}", filePath, relativePath);
                    return ResponseEntity.notFound()
                        .header("Access-Control-Allow-Origin", "*")
                        .build();
                }
            }

            String filename = path.getFileName().toString();
            logger.info("准备返回文件: {}, 路径: {}", filename, path.toAbsolutePath());

            // 记录访问日志
            try {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                String currentUsername = authentication != null ? authentication.getName() : "anonymous";
                String ipAddress = getClientIpAddress();
                Long userId = null;

                if (authentication != null && !"anonymous".equals(currentUsername)) {
                    com.mylog.model.user.User user = userService.findUserByUsername(currentUsername).orElse(null);
                    if (user != null) {
                        userId = user.getUserId();
                    }
                }

                activityLogService.logView(
                        userId,
                        currentUsername,
                        "cpolar预览文件: " + filename,
                        ipAddress,
                        "CpolarFileAccess",
                        submitIdLong,
                        getAccessType());
            } catch (Exception e) {
                logger.error("记录访问日志失败", e);
            }

            // 创建文件资源
            Resource resource = new FileSystemResource(path);

            // 获取文件的MIME类型
            String contentType = getOfficeDocumentMimeType(filename);
            if (contentType == null) {
                contentType = Files.probeContentType(path);
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
            }

            logger.info("文件MIME类型: {}", contentType);

            // 构建响应，设置Office Web Viewer需要的CORS头部
            HttpHeaders headers = new HttpHeaders();
            headers.add("Access-Control-Allow-Origin", "*");
            headers.add("Access-Control-Allow-Methods", "GET, HEAD, OPTIONS");
            headers.add("Access-Control-Allow-Headers", "Range, Content-Type, Authorization, Accept");
            headers.add("Access-Control-Expose-Headers", "Content-Length, Content-Range, ETag, Content-Type");
            headers.add("Cache-Control", "public, max-age=3600");
            headers.add("Accept-Ranges", "bytes");
            headers.add("X-Content-Type-Options", "nosniff");
            
            // 使用安全的文件名编码
            String encodedFilename = java.net.URLEncoder.encode(filename, "UTF-8").replaceAll("\\+", "%20");
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline; filename*=UTF-8''" + encodedFilename);

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.parseMediaType(contentType))
                    .contentLength(Files.size(path))
                    .body(resource);

        } catch (NumberFormatException e) {
            logger.error("参数格式错误: submitId={}, file={}", submitId, file, e);
            return ResponseEntity.badRequest()
                .header("Access-Control-Allow-Origin", "*")
                .build();
        } catch (Exception e) {
            logger.error("cpolar预览文件失败: submitId={}, file={}", submitId, file, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .header("Access-Control-Allow-Origin", "*")
                .build();
        }
    }

    /**
     * 处理HEAD请求（Office Web Viewer需要）
     */
    @RequestMapping(value = "/cpolar-preview/{submitId}", method = RequestMethod.HEAD)
    public ResponseEntity<Void> headCpolarPreviewFile(
            @PathVariable String submitId,
            @RequestParam String file,
            @RequestParam(required = false) String token) {

        logger.info("cpolar预览文件HEAD请求: submitId={}, file={}", submitId, file);

        try {
            // 验证token
            if (!cpolarPreviewService.isValidPreviewToken(token, submitId, file)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .header("Access-Control-Allow-Origin", "*")
                    .build();
            }

            // 获取提交记录
            Long submitIdLong = Long.parseLong(submitId);
            Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitIdLong);
            if (!submitOpt.isPresent()) {
                return ResponseEntity.notFound()
                    .header("Access-Control-Allow-Origin", "*")
                    .build();
            }

            Submit2 submit = submitOpt.get();
            int fileNumber = Integer.parseInt(file);
            String filePath = getFilePath(submit, fileNumber);

            if (filePath == null || filePath.isEmpty()) {
                return ResponseEntity.notFound()
                    .header("Access-Control-Allow-Origin", "*")
                    .build();
            }

            // 检查文件是否存在
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                Path relativePath = Paths.get(dataPath, filePath);
                if (Files.exists(relativePath)) {
                    path = relativePath;
                } else {
                    return ResponseEntity.notFound()
                        .header("Access-Control-Allow-Origin", "*")
                        .build();
                }
            }

            String filename = path.getFileName().toString();
            String contentType = getOfficeDocumentMimeType(filename);
            if (contentType == null) {
                contentType = Files.probeContentType(path);
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
            }

            // 返回HEAD响应
            HttpHeaders headers = new HttpHeaders();
            headers.add("Access-Control-Allow-Origin", "*");
            headers.add("Access-Control-Allow-Methods", "GET, HEAD, OPTIONS");
            headers.add("Access-Control-Allow-Headers", "Range, Content-Type, Authorization, Accept");
            headers.add("Access-Control-Expose-Headers", "Content-Length, Content-Range, ETag, Content-Type");
            headers.add("Cache-Control", "public, max-age=3600");
            headers.add("Accept-Ranges", "bytes");
            
            String encodedFilename = java.net.URLEncoder.encode(filename, "UTF-8").replaceAll("\\+", "%20");
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline; filename*=UTF-8''" + encodedFilename);

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.parseMediaType(contentType))
                    .contentLength(Files.size(path))
                    .build();

        } catch (Exception e) {
            logger.error("cpolar预览文件HEAD请求失败: submitId={}, file={}", submitId, file, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .header("Access-Control-Allow-Origin", "*")
                .build();
        }
    }

    /**
     * 处理OPTIONS预检请求（CORS）
     */
    @RequestMapping(value = "/cpolar-preview/{submitId}", method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleCpolarPreviewOptions(@PathVariable String submitId) {
        return ResponseEntity.ok()
                .header("Access-Control-Allow-Origin", "*")
                .header("Access-Control-Allow-Methods", "GET, HEAD, OPTIONS")
                .header("Access-Control-Allow-Headers", "Range, Content-Type, Authorization")
                .header("Access-Control-Max-Age", "3600")
                .build();
    }

    /**
     * 测试cpolar URL可访问性
     */
    @GetMapping("/test-cpolar-access")
    public ResponseEntity<Map<String, Object>> testCpolarAccess() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取cpolar服务的调试信息
            Map<String, Object> debugInfo = cpolarPreviewService.getDebugInfo();
            result.putAll(debugInfo);
            
            boolean isEnabled = cpolarPreviewService.isEnabled();
            result.put("timestamp", System.currentTimeMillis());
            result.put("message", isEnabled ? "cpolar预览功能已启用" : "cpolar预览功能未启用");
            
            if (isEnabled) {
                // 获取配置的域名
                String domain = (String) debugInfo.get("domain");
                String testUrl = String.format("https://%s", domain);
                result.put("testUrl", testUrl);
                result.put("apiTestUrl", testUrl + "/api/files/test-cpolar-access");
            }
            
            return ResponseEntity.ok()
                .header("Access-Control-Allow-Origin", "*")
                .body(result);
                
        } catch (Exception e) {
            logger.error("测试cpolar访问失败", e);
            result.put("error", e.getMessage());
            result.put("cpolarEnabled", false);
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .header("Access-Control-Allow-Origin", "*")
                .body(result);
        }
    }

    /**
     * cpolar测试页面
     */
    @GetMapping("/cpolar-test")
    public String cpolarTestPage() {
        return "redirect:/cpolar-test.html";
    }

    /**
     * 调试cpolar预览URL生成（仅用于测试）
     */
    @PostMapping("/debug-cpolar-preview/{submitId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> debugCpolarPreview(
            @PathVariable String submitId,
            @RequestParam String file) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取cpolar配置信息
            Map<String, Object> debugInfo = cpolarPreviewService.getDebugInfo();
            result.put("config", debugInfo);
            
            // 检查权限
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            boolean isAdmin = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
            boolean isManager = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

            result.put("user", currentUsername);
            result.put("hasPermission", isAdmin || isManager);
            
            if (!isAdmin && !isManager) {
                result.put("success", false);
                result.put("message", "权限不足，需要ADMIN或MANAGER角色");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
            }

            // 检查提交记录是否存在
            Long submitIdLong = Long.parseLong(submitId);
            Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitIdLong);
            result.put("submitExists", submitOpt.isPresent());
            
            if (!submitOpt.isPresent()) {
                result.put("success", false);
                result.put("message", "提交记录不存在: " + submitId);
                return ResponseEntity.notFound().build();
            }

            Submit2 submit = submitOpt.get();
            int fileNumber = Integer.parseInt(file);
            String filePath = fileNumber == 1 ? submit.getFilePath1() : submit.getFilePath2();
            
            result.put("filePath", filePath);
            result.put("fileNumber", fileNumber);
            
            if (filePath == null || filePath.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件路径为空");
                return ResponseEntity.badRequest().body(result);
            }

            // 检查文件是否存在
            Path path = Paths.get(filePath);
            boolean fileExists = Files.exists(path);
            result.put("fileExistsAbsolute", fileExists);
            
            if (!fileExists) {
                Path relativePath = Paths.get(dataPath, filePath);
                boolean relativeExists = Files.exists(relativePath);
                result.put("fileExistsRelative", relativeExists);
                result.put("relativePath", relativePath.toString());
                
                if (relativeExists) {
                    path = relativePath;
                    fileExists = true;
                }
            }
            
            result.put("finalPath", path.toString());
            result.put("fileExists", fileExists);
            
            if (fileExists) {
                String filename = path.getFileName().toString();
                result.put("filename", filename);
                result.put("fileSize", Files.size(path));
                
                // 生成cpolar预览URL
                if (cpolarPreviewService.isEnabled()) {
                    String previewUrl = cpolarPreviewService.generatePreviewUrl(submitId, file);
                    result.put("previewUrl", previewUrl);
                    result.put("success", true);
                    result.put("message", "调试信息生成成功");
                } else {
                    result.put("success", false);
                    result.put("message", "cpolar预览功能未启用");
                }
            } else {
                result.put("success", false);
                result.put("message", "文件不存在");
            }
            
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("调试cpolar预览失败: submitId={}, file={}", submitId, file, e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 根据文件编号获取对应的文件路径
     */
    private String getFilePath(Submit2 submit, int fileNumber) {
        switch (fileNumber) {
            case 1: return submit.getFilePath1();
            case 2: return submit.getFilePath2();
            case 3: return submit.getFilePath3();
            case 4: return submit.getFilePath4();
            default: return null;
        }
    }

    /**
     * 获取Office文档的正确MIME类型
     */
    private String getOfficeDocumentMimeType(String filename) {
        if (filename == null) return null;
        
        String lowerName = filename.toLowerCase();
        if (lowerName.endsWith(".doc")) {
            return "application/msword";
        } else if (lowerName.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (lowerName.endsWith(".xls")) {
            return "application/vnd.ms-excel";
        } else if (lowerName.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else if (lowerName.endsWith(".ppt")) {
            return "application/vnd.ms-powerpoint";
        } else if (lowerName.endsWith(".pptx")) {
            return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
        } else if (lowerName.endsWith(".pdf")) {
            return "application/pdf";
        }
        return null;
    }
}
