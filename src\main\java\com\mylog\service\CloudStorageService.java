package com.mylog.service;

import java.nio.file.Path;

/**
 * 云存储服务接口
 */
public interface CloudStorageService {
    
    /**
     * 检查云存储服务是否可用
     */
    boolean isAvailable();
    
    /**
     * 上传文件到云存储
     * 
     * @param filePath 本地文件路径
     * @param filename 文件名
     * @return 公网可访问的URL
     * @throws Exception 上传失败时抛出异常
     */
    String uploadFile(Path filePath, String filename) throws Exception;
    
    /**
     * 删除云存储中的文件
     * 
     * @param fileUrl 文件的公网URL
     * @return 是否删除成功
     */
    boolean deleteFile(String fileUrl);
    
    /**
     * 获取云存储服务的类型
     */
    String getServiceType();
}
