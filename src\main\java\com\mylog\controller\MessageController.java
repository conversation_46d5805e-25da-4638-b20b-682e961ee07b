package com.mylog.controller;

import com.mylog.model.Message;
import com.mylog.service.MessageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/messages")
public class MessageController {

    private static final Logger logger = LoggerFactory.getLogger(MessageController.class);

    @Autowired
    private MessageService messageService;

    @ModelAttribute("unreadMessageCount")
    public Long getUnreadMessageCount() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser")) {
                String username = auth.getName();
                return messageService.countUnreadMessagesByReceiver(username);
            }
        } catch (Exception e) {
            // 如果获取未读消息数量失败，不影响页面正常显示
        }
        return 0L;
    }

    @GetMapping
    public String listMessages(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "jumpPage", required = false) Integer jumpPage,
            Model model) {
        // 获取当前登录用户
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String username = auth.getName();

        // 处理jumpPage参数 - 如果提供了jumpPage，将其转换为0-based的page
        if (jumpPage != null && jumpPage > 0) {
            logger.debug("收到jumpPage参数: {}, 原page参数: {}", jumpPage, page);
            page = jumpPage - 1; // 将1-based转换为0-based
            logger.debug("转换后的page参数: {}", page);
        }

        // 创建分页请求，每页最多20条记录
        // 不需要指定排序，因为在Repository中已经使用原生SQL指定了排序
        // 确保页码不为负数
        Pageable pageable = PageRequest.of(Math.max(0, page), Math.min(size, 20));

        // 获取用户的消息（分页版本）
        Page<Message> messagePage;
        if (keyword != null && !keyword.trim().isEmpty()) {
            // 如果有搜索关键词，执行搜索
            messagePage = messageService.searchMessagesByContent(username, keyword, pageable);
            model.addAttribute("keyword", keyword);
            model.addAttribute("isSearching", true);
        } else {
            // 否则显示所有消息
            messagePage = messageService.findMessagesByReceiver(username, pageable);
            model.addAttribute("keyword", ""); // 确保keyword不为null
            model.addAttribute("isSearching", false);
        }

        // 确保所有消息的日期字段都被初始化
        logger.debug("加载了 {} 条消息", messagePage.getContent().size());
        for (Message message : messagePage.getContent()) {
            // 只是初始化日期字段，不记录日志
            message.getCreatedDate();
        }

        model.addAttribute("messagePage", messagePage);
        model.addAttribute("activeMenu", "messages");

        // 获取未读消息数量
        Long unreadCount = messageService.countUnreadMessagesByReceiver(username);
        model.addAttribute("unreadMessageCount", unreadCount);

        return "messages/list";
    }

    @GetMapping("/{id}")
    public String viewMessage(@PathVariable("id") Long id, Model model, RedirectAttributes redirectAttributes) {
        Optional<Message> messageOpt = messageService.findMessageById(id);
        if (messageOpt.isPresent()) {
            Message message = messageOpt.get();

            // 获取当前登录用户
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String username = auth.getName();

            // 验证消息接收者是当前用户
            if (!message.getReceiver().equals(username)) {
                redirectAttributes.addFlashAttribute("error", "您无权查看此消息！");
                return "redirect:/messages";
            }

            // 确保日期字段被初始化
            message.getCreatedDate();

            // 标记消息为已读
            if (!message.isRead()) {
                messageService.markAsRead(id);
                message.setRead(true);
            }

            model.addAttribute("message", message);
            model.addAttribute("activeMenu", "messages");
            return "messages/view";
        } else {
            redirectAttributes.addFlashAttribute("error", "消息不存在！");
            return "redirect:/messages";
        }
    }

    @PostMapping("/mark-read")
    public String markAsRead(@RequestParam("messageId") Long id, RedirectAttributes redirectAttributes) {
        messageService.markAsRead(id);
        redirectAttributes.addFlashAttribute("message", "消息已标记为已读！");
        return "redirect:/messages";
    }

    @PostMapping("/mark-all-read")
    public String markAllAsRead(RedirectAttributes redirectAttributes) {
        // 获取当前登录用户
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String username = auth.getName();

        messageService.markAllAsRead(username);
        redirectAttributes.addFlashAttribute("message", "所有消息已标记为已读！");
        return "redirect:/messages";
    }

    @PostMapping("/delete")
    public String deleteMessage(@RequestParam("messageId") Long id, RedirectAttributes redirectAttributes) {
        messageService.deleteMessage(id);
        redirectAttributes.addFlashAttribute("message", "消息已删除！");
        return "redirect:/messages";
    }
}