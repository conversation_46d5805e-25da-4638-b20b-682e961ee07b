package com.mylog.service.impl;

import com.mylog.service.CloudStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.nio.file.Path;

/**
 * 禁用状态的云存储服务实现
 */
@Service
@ConditionalOnProperty(name = "cloud.storage.type", havingValue = "disabled", matchIfMissing = true)
public class DisabledCloudStorageService implements CloudStorageService {
    
    private static final Logger logger = LoggerFactory.getLogger(DisabledCloudStorageService.class);
    
    @Override
    public boolean isAvailable() {
        return false;
    }
    
    @Override
    public String uploadFile(Path filePath, String filename) throws Exception {
        logger.warn("云存储服务已禁用，无法上传文件: {}", filename);
        throw new UnsupportedOperationException("云存储服务已禁用。请在配置文件中启用云存储服务（设置 cloud.storage.type）");
    }
    
    @Override
    public boolean deleteFile(String fileUrl) {
        logger.warn("云存储服务已禁用，无法删除文件: {}", fileUrl);
        return false;
    }
    
    @Override
    public String getServiceType() {
        return "disabled";
    }
}
