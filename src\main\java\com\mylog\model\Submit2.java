package com.mylog.model;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Entity
@Table(name = "Submits2")
@Data
public class Submit2 {
    
    private static final Logger logger = LoggerFactory.getLogger(Submit2.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long submitId;
    
    @Column(nullable = false)
    private Long taskId;
    
    @Column(columnDefinition = "TEXT")
    private String submitDate;
    
    @Column(columnDefinition = "TEXT")
    private String remarks;
    
    @Column(columnDefinition = "TEXT")
    private String submitName;
    
    private String filePath1;
    
    private String filePath2;
    
    private String filePath3;
    
    private String filePath4;
    
    @Column(nullable = true)
    private String submitter;
    
    /**
     * 工作流程实例ID
     * 关联的审批流程实例，用于跟踪提交记录与审批流程的关系
     */
    @Column(name = "workflow_instance_id", nullable = true)
    private Long workflowInstanceId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "taskId", insertable = false, updatable = false)
    private ProjectTask task;
    
    public Submit2() {
        setSubmitDateTime(LocalDateTime.now());
    }
    
    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            logger.error("提交日期解析错误", e);
            return null;
        }
    }
    
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMATTER) : null;
    }
    
    public String getSubmitDate() {
        return this.submitDate;
    }
    
    public void setSubmitDate(String date) {
        this.submitDate = date;
    }
    
    public LocalDateTime getSubmitDateTime() {
        return parseDateTime(this.submitDate);
    }
    
    public void setSubmitDateTime(LocalDateTime dateTime) {
        this.submitDate = formatDateTime(dateTime);
    }
}