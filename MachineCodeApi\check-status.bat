@echo off
echo 检查机器码API项目状态...
echo.

cd /d "c:\mylog-web\MachineCodeApi"

echo [1/6] 检查项目文件结构...
if exist "MachineCodeApi.csproj" (
    echo ✓ MachineCodeApi.csproj 存在
) else (
    echo ✗ MachineCodeApi.csproj 不存在
)

if exist "Controllers\MachineCodeController.cs" (
    echo ✓ MachineCodeController.cs 存在
) else (
    echo ✗ MachineCodeController.cs 不存在
)

if exist "Models\MachineCodeRequest.cs" (
    echo ✓ MachineCodeRequest.cs 存在
) else (
    echo ✗ MachineCodeRequest.cs 不存在
)

if exist "Models\MachineCodeResponse.cs" (
    echo ✓ MachineCodeResponse.cs 存在
) else (
    echo ✗ MachineCodeResponse.cs 不存在
)

echo.
echo [2/6] 检查ClassPs.dll...
if exist "ClassPs.dll" (
    echo ✓ ClassPs.dll 存在
) else (
    echo ✗ ClassPs.dll 不存在，正在从上级目录复制...
    if exist "..\ClassPs.dll" (
        copy "..\ClassPs.dll" "ClassPs.dll"
        echo ✓ ClassPs.dll 复制成功
    ) else (
        echo ✗ 未找到 ClassPs.dll 文件
    )
)

echo.
echo [3/6] 检查.NET SDK...
dotnet --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ .NET SDK 已安装
    dotnet --version
) else (
    echo ✗ .NET SDK 未安装
)

echo.
echo [4/6] 恢复NuGet包...
dotnet restore >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ NuGet包恢复成功
) else (
    echo ✗ NuGet包恢复失败
)

echo.
echo [5/6] 编译项目...
dotnet build >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ 项目编译成功
) else (
    echo ✗ 项目编译失败
    echo 运行以下命令查看详细错误：
    echo dotnet build
)

echo.
echo [6/6] 检查端口5000是否被占用...
netstat -an | findstr :5000 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ⚠ 端口5000已被占用，可能API已在运行
) else (
    echo ✓ 端口5000可用
)

echo.
echo ========================================
echo 检查完成！
echo.
echo 如果所有检查都通过，您可以：
echo 1. 运行 start-api.bat 启动API服务
echo 2. 或手动执行：dotnet run --urls "http://localhost:5000"
echo ========================================
pause
