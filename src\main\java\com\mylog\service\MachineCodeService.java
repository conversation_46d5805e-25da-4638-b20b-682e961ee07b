package com.mylog.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

/**
 * 机器码获取服务
 * 通过HTTP调用C# Web API获取机器码
 * 
 * <AUTHOR>
 */
@Service
public class MachineCodeService {

    private static final Logger logger = LoggerFactory.getLogger(MachineCodeService.class);

    // C# Web API服务的地址，可以通过配置文件配置
    @Value("${machine.code.api.url:http://localhost:5000/api/machinecode}")
    private String apiUrl;

    @Value("${machine.code.api.timeout:5}")
    private int timeoutSeconds;

    private final HttpClient httpClient;

    public MachineCodeService() {
        // 创建HTTP客户端
        this.httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build();
        
        logger.info("MachineCodeService初始化完成，API地址将在运行时从配置中获取");
    }

    /**
     * 获取机器码
     * 
     * @return 机器码字符串，如果获取失败则返回错误信息
     */
    public String getMachineCode() {
        try {
            logger.info("开始调用机器码API: {}", apiUrl);
            
            // 创建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(apiUrl))
                .timeout(Duration.ofSeconds(timeoutSeconds))
                .header("Content-Type", "application/json")
                .header("Accept", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString("{\"key\":\"tomsorvision\"}"))
                .build();

            // 发送请求并获取响应
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            
            logger.info("API响应状态码: {}", response.statusCode());
            
            if (response.statusCode() == 200) {
                String responseBody = response.body();
                logger.info("API响应内容: {}", responseBody);
                
                // 简单的JSON解析，假设返回格式为 {"machineCode":"xxx","success":true}
                if (responseBody.contains("\"success\":true") && responseBody.contains("\"machineCode\"")) {
                    // 提取machineCode值
                    String machineCode = extractMachineCodeFromJson(responseBody);
                    if (machineCode != null && !machineCode.trim().isEmpty()) {
                        logger.info("成功获取机器码");
                        return machineCode.trim();
                    }
                }
                
                // 如果解析失败，返回原始响应
                return "错误：API返回格式异常 - " + responseBody;
                
            } else {
                String errorMsg = String.format("API调用失败，状态码: %d, 响应: %s", 
                    response.statusCode(), response.body());
                logger.error(errorMsg);
                return "错误：" + errorMsg;
            }

        } catch (IOException e) {
            logger.error("网络请求失败：{}", e.getMessage(), e);
            return "错误：网络请求失败 - " + e.getMessage();
        } catch (InterruptedException e) {
            logger.error("请求被中断：{}", e.getMessage(), e);
            Thread.currentThread().interrupt();
            return "错误：请求被中断 - " + e.getMessage();
        } catch (Exception e) {
            logger.error("获取机器码时发生未知错误：{}", e.getMessage(), e);
            return "错误：" + e.getMessage();
        }
    }
    
    /**
     * 从JSON响应中提取机器码
     */
    private String extractMachineCodeFromJson(String json) {
        try {
            // 简单的字符串解析，寻找 "machineCode":"value" 模式
            String pattern = "\"machineCode\":\"";
            int startIndex = json.indexOf(pattern);
            if (startIndex != -1) {
                startIndex += pattern.length();
                int endIndex = json.indexOf("\"", startIndex);
                if (endIndex != -1) {
                    return json.substring(startIndex, endIndex);
                }
            }
            return null;
        } catch (Exception e) {
            logger.error("解析JSON时出错：{}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 检查机器码API是否可用
     * 
     * @return true如果API可用，false否则
     */
    public boolean isDllAvailable() {
        try {
            // 发送一个简单的健康检查请求
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(apiUrl + "/health"))
                .timeout(Duration.ofSeconds(3))
                .GET()
                .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            return response.statusCode() == 200;
        } catch (Exception e) {
            logger.debug("API健康检查失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 计算注册码
     * 
     * @param machineCode 机器码
     * @return 注册码字符串，如果计算失败则返回错误信息
     */
    public String calculateLicense(String machineCode) {
        try {
            logger.info("开始计算注册码，机器码: {}", machineCode);

            if (machineCode == null || machineCode.trim().isEmpty()) {
                return "错误：机器码不能为空";
            }

            // 构建请求体
            String requestBody = "{\"key\":\"" + machineCode.trim() + "\"}";
            
            // 构建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(apiUrl + "/calculate-license"))
                .timeout(Duration.ofSeconds(timeoutSeconds))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();
            
            logger.debug("发送计算注册码请求到: {}", apiUrl + "/calculate-license");
            logger.debug("请求体: {}", requestBody);
            
            // 发送请求
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            
            logger.debug("收到响应，状态码: {}", response.statusCode());
            logger.debug("响应内容: {}", response.body());
            
            if (response.statusCode() == 200) {
                String responseBody = response.body();
                
                // 检查API响应中的success字段
                if (responseBody.contains("\"success\":true")) {
                    String licenseCode = extractMachineCodeFromJson(responseBody);
                    if (licenseCode != null) {
                        logger.info("成功计算注册码");
                        return licenseCode;
                    } else {
                        logger.error("无法从响应中提取注册码");
                        return "错误：无法从API响应中提取注册码";
                    }
                } else {
                    // 提取错误消息
                    String errorMessage = extractErrorMessage(responseBody);
                    logger.error("API返回错误: {}", errorMessage);
                    return "错误：" + errorMessage;
                }
            } else {
                logger.error("API请求失败，状态码: {}", response.statusCode());
                return "错误：API请求失败，状态码：" + response.statusCode();
            }
            
        } catch (IOException e) {
            logger.error("网络请求异常：{}", e.getMessage(), e);
            return "错误：网络请求失败 - " + e.getMessage();
        } catch (InterruptedException e) {
            logger.error("请求被中断：{}", e.getMessage(), e);
            Thread.currentThread().interrupt();
            return "错误：请求被中断 - " + e.getMessage();
        } catch (Exception e) {
            logger.error("计算注册码时发生未知错误：{}", e.getMessage(), e);
            return "错误：" + e.getMessage();
        }
    }

    /**
     * 从JSON响应中提取错误消息
     */
    private String extractErrorMessage(String json) {
        try {
            String pattern = "\"message\":\"";
            int startIndex = json.indexOf(pattern);
            if (startIndex != -1) {
                startIndex += pattern.length();
                int endIndex = json.indexOf("\"", startIndex);
                if (endIndex != -1) {
                    return json.substring(startIndex, endIndex);
                }
            }
            return "未知错误";
        } catch (Exception e) {
            logger.error("解析错误消息时出错：{}", e.getMessage(), e);
            return "解析错误消息失败";
        }
    }
}
