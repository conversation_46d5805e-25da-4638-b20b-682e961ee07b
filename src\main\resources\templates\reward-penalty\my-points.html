<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head('我的积分')}">
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">

    <div class="content-wrapper">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                <i class="bi bi-star me-2"></i>我的积分
            </h1>
        </div>

        <!-- 错误信息 -->
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <span th:text="${errorMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 成功信息 -->
        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <span th:text="${successMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 积分概览 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="card-title mb-0" th:text="${totalPoints != null ? totalPoints : 0}">0</h4>
                                <p class="card-text">当前总积分</p>
                            </div>
                            <div>
                                <i class="bi bi-star-fill" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="card-title mb-0" th:text="${#lists.size(records)}">0</h4>
                                <p class="card-text">记录总数</p>
                            </div>
                            <div>
                                <i class="bi bi-list-ul" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 积分记录表格 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-table me-2"></i>积分记录详情
                </h5>
            </div>
            <div class="card-body">
                <div th:if="${#lists.isEmpty(records)}" class="text-center text-muted py-5">
                    <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                    <p class="mt-3">暂无积分记录</p>
                </div>
                <div th:if="${!#lists.isEmpty(records)}" class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-light text-dark">
                            <tr>
                                <th>#</th>
                                <th>类型</th>
                                <th>事由</th>
                                <th>变化积分</th>
                                <th>存量积分</th>
                                <th>发生时间</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="record, iterStat : ${records}">
                                <td th:text="${iterStat.count}"></td>
                                <td>
                                    <span class="badge"
                                        th:classappend="${record.points > 0 ? 'bg-success' : 'bg-danger'}"
                                        th:text="${record.type}"></span>
                                </td>
                                <td>
                                    <!-- 事由以链接形式显示，优先提取任务ID，其次流程ID -->
                                    <span
                                        th:with="taskId=${#strings.contains(record.reason, '任务ID：') ? #strings.substringAfter(record.reason, '任务ID：') : ''},
                                                 processId=${#strings.contains(record.reason, '流程ID：') ? #strings.substringAfter(record.reason, '流程ID：') : ''}">
                                        <a th:if="${!#strings.isEmpty(taskId)}" th:href="@{/tasks/{id}(id=${taskId})}"
                                            target="_blank" th:text="${record.reason}"></a>
                                        <a th:if="${#strings.isEmpty(taskId) && !#strings.isEmpty(processId)}"
                                            th:href="@{/workflow/approval/{id}(id=${processId})}" target="_blank"
                                            th:text="${record.reason}"></a>
                                        <span th:if="${#strings.isEmpty(taskId) && #strings.isEmpty(processId)}"
                                            th:text="${record.reason}"></span>
                                    </span>
                                </td>
                                <td>
                                    <span th:classappend="${record.points > 0 ? 'text-success' : 'text-danger'}"
                                        th:text="${record.points > 0 ? '+' + record.points : record.points}"></span>
                                </td>
                                <td>
                                    <strong th:text="${record.totalPoints}"></strong>
                                </td>
                                <td th:text="${record.occurTime}"></td>
                                <td th:text="${record.remarks ?: '-'}"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面特定的JavaScript代码
    </script>

</body>

</html>