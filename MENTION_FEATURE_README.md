# 子任务评论@联想提及功能使用说明

## 功能概述

在子任务评论页面 (`/subtasks/new?taskId=xxx`) 新增了@联想提及功能，允许用户在输入评论时使用@符号来提及相关人员，系统会自动解析并发送通知消息。

## 功能特性

### 1. @符号触发联想
- 在评论文本框中输入`@`符号
- 系统会自动弹出人员选择下拉框
- 支持实时搜索过滤人员

### 2. 多种选择方式
- **鼠标选择**：点击下拉列表中的人员
- **键盘选择**：使用上下箭头键选择，回车确认
- **搜索过滤**：在搜索框中输入姓名进行过滤

### 3. 自动消息通知
- 评论保存后，系统会自动解析@提及的人员
- 向被@的人员发送系统消息通知
- 同时发送企业微信消息（如果配置了的话）

### 4. 智能提及解析
- 只有在有效人员列表中的姓名才会被识别
- 支持中文姓名、英文姓名和数字组合
- 自动去重，同一人员只会收到一次通知

## 使用步骤

1. 访问子任务评论页面：`http://localhost:8080/subtasks/new?taskId=3598`
2. 在"内容"文本框中正常输入评论
3. 需要提及人员时，输入`@`符号
4. 在弹出的下拉框中选择或搜索人员
5. 选中的人员会自动插入到文本中（如：@张三）
6. 提交表单完成保存，系统自动发送通知给被@的人员

## 技术实现

### 前端实现
- **HTML结构**：使用`position: relative`容器包含文本框和下拉列表
- **CSS样式**：美观的下拉列表、搜索框、人员标签样式  
- **JavaScript功能**：
  - 监听文本框输入，检测@符号
  - AJAX获取人员列表数据
  - 键盘导航和鼠标点击处理
  - 抄送人员的添加和移除

### 后端实现
- **API接口**：`/subtasks/api/personnel` 获取人员列表
- **提及解析**：使用正则表达式解析评论中的@提及
- **消息系统**：保存消息到Messages表，发送企业微信通知
- **数据源**：通过`optionsService.getPersonnel()`获取人员数据

### 核心功能
- **extractMentionedPersons()**: 从评论文本中提取@提及的人员
- **sendMentionNotification()**: 创建系统消息记录
- **sendWeixinMentionNotification()**: 发送企业微信通知

## 界面预览

### @联想下拉框
- 美观的人员列表，带头像和姓名
- 实时搜索过滤功能
- 键盘友好的导航体验

### 消息通知界面
- 被@的人员会收到系统消息通知
- 消息包含完整的评论内容和任务信息
- 支持企业微信消息推送

## 注意事项

1. **权限控制**：确保只有有权限的用户才能访问人员列表
2. **数据验证**：前端选择的人员会在后端进行验证
3. **性能优化**：人员列表会在页面加载时一次性获取并缓存
4. **兼容性**：支持现代浏览器，使用标准Web API

## 扩展计划

- 支持@群组功能
- 添加消息已读状态跟踪
- 支持在已有评论中编辑@提及
- 移动端适配优化
- 支持邮件通知方式