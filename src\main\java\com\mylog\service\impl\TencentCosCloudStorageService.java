package com.mylog.service.impl;

/*
 * 腾讯云COS云存储服务实现
 * 
 * 要使用此实现，请：
 * 1. 在pom.xml中添加腾讯云COS SDK依赖
 * 2. 在application.properties中配置相关参数
 * 3. 取消注释此类
 * 
 * Maven依赖：
 * <dependency>
 *     <groupId>com.qcloud</groupId>
 *     <artifactId>cos_api</artifactId>
 *     <version>5.6.155</version>
 * </dependency>
 */


import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.model.CannedAccessControlList;
import com.qcloud.cos.region.Region;
import com.mylog.config.CloudStorageConfig;
import com.mylog.service.CloudStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 腾讯云COS云存储服务实现
 * 
 * 使用前请：
 * 1. 取消相关import的注释
 * 2. 取消类注解的注释
 * 3. 在pom.xml中添加腾讯云COS SDK依赖
 */
 @Service
 @ConditionalOnProperty(name = "cloud.storage.type", havingValue = "tencent-cos")
public class TencentCosCloudStorageService implements CloudStorageService {
    
    private static final Logger logger = LoggerFactory.getLogger(TencentCosCloudStorageService.class);
    
    @Autowired
    private CloudStorageConfig config;
    
    @Override
    public boolean isAvailable() {
        logger.info("检查腾讯云COS服务可用性...");
        logger.info("配置对象: {}", config != null ? "存在" : "null");
        
        if (config == null) {
            logger.error("CloudStorageConfig 配置对象为null");
            return false;
        }
        
        logger.info("配置启用状态: {}", config.isEnabled());
        logger.info("配置类型: {}", config.getType());
        logger.info("存储桶名称: {}", config.getBucket().getName());
        logger.info("地域: {}", config.getRegion());
        logger.info("访问密钥ID: {}", config.getAccess().getKey().getId() != null ? "已设置" : "未设置");
        logger.info("访问密钥Secret: {}", config.getAccess().getKey().getSecret() != null ? "已设置" : "未设置");
        
        // 检查配置是否完整
        boolean available = config != null && 
               config.isEnabled() && 
               "tencent-cos".equalsIgnoreCase(config.getType()) &&
               config.getAccess().getKey().getId() != null &&
               config.getAccess().getKey().getSecret() != null &&
               config.getBucket().getName() != null &&
               config.getRegion() != null;
               
        logger.info("腾讯云COS服务可用性检查结果: {}", available);
        return available;
    }
    
    @Override
    public String uploadFile(Path filePath, String filename) throws Exception {
        logger.info("=== TencentCosCloudStorageService.uploadFile 开始执行 ===");
        logger.info("文件路径: {}", filePath.toAbsolutePath());
        logger.info("文件名: {}", filename);
        
        if (!isAvailable()) {
            logger.error("腾讯云COS配置不完整，无法上传文件");
            throw new UnsupportedOperationException("腾讯云COS配置不完整");
        }
        
        logger.info("开始上传文件到腾讯云COS: {}", filename);
        
        COSClient cosClient = null;
        try {
            // 创建COS凭证
            COSCredentials cred = new BasicCOSCredentials(
                config.getAccess().getKey().getId(),
                config.getAccess().getKey().getSecret()
            );
            
            // 设置存储桶的地域
            Region region = new Region(config.getRegion());
            ClientConfig clientConfig = new ClientConfig(region);
            
            // 创建COS客户端
            cosClient = new COSClient(cred, clientConfig);
            
            // 生成对象键（文件在COS中的路径）
            String objectKey = generateObjectKey(filename);
            logger.info("生成的对象键: {}", objectKey);
            
            // 设置对象元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(Files.size(filePath));
            metadata.setContentType(getContentType(filename));
            
            // 设置缓存控制和CORS相关头部
            metadata.setCacheControl("public, max-age=3600");
            metadata.setHeader("Access-Control-Allow-Origin", "*");
            metadata.setHeader("Access-Control-Allow-Methods", "GET, HEAD, OPTIONS");
            metadata.setHeader("Access-Control-Allow-Headers", "Range, Content-Type, Authorization");
            metadata.setHeader("Access-Control-Expose-Headers", "ETag, Content-Length");
            
            // 设置对象为公共读权限（重要：确保文件可以被公开访问）
            logger.info("设置文件为公共读权限以支持Office Web Viewer访问");
            
            logger.info("文件大小: {} bytes", Files.size(filePath));
            logger.info("Content-Type: {}", metadata.getContentType());
            
            // 上传文件
            FileInputStream fileInputStream = new FileInputStream(filePath.toFile());
            PutObjectRequest putRequest = new PutObjectRequest(
                config.getBucket().getName(),
                objectKey,
                fileInputStream,
                metadata
            );
            
            // 设置对象为公共读权限，确保Office Web Viewer可以访问
            putRequest.setCannedAcl(CannedAccessControlList.PublicRead);
            
            PutObjectResult putResult = cosClient.putObject(putRequest);
            logger.info("文件上传到腾讯云COS成功，ETag: {}", putResult.getETag());
            
            // 生成公网访问URL
            String publicUrl = generatePublicUrl(objectKey);
            
            // 验证文件是否可以公开访问
            logger.info("验证文件公开访问性...");
            try {
                java.net.HttpURLConnection connection = (java.net.HttpURLConnection) new java.net.URL(publicUrl).openConnection();
                connection.setRequestMethod("HEAD");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                int responseCode = connection.getResponseCode();
                logger.info("文件访问测试结果: HTTP {}", responseCode);
                
                if (responseCode == 200) {
                    logger.info("✓ 文件可以正常公开访问");
                } else if (responseCode == 403) {
                    logger.warn("⚠ 文件访问被拒绝(403)，请检查存储桶权限配置");
                    logger.warn("建议：在腾讯云控制台设置存储桶为公共读或配置存储桶策略");
                } else {
                    logger.warn("⚠ 文件访问异常，HTTP状态码: {}", responseCode);
                }
                connection.disconnect();
            } catch (Exception e) {
                logger.warn("无法验证文件公开访问性: {}", e.getMessage());
            }
            
            logger.info("文件上传到腾讯云COS成功: {} -> {}", filename, publicUrl);
            logger.info("请确保腾讯云COS存储桶已配置CORS规则以允许Office Web Viewer访问");
            logger.info("如果Office Web Viewer访问失败，请检查存储桶的公共读权限设置");
            
            return publicUrl;
            
        } catch (CosServiceException e) {
            logger.error("腾讯云COS服务异常: ErrorCode={}, ErrorMessage={}, StatusCode={}", 
                        e.getErrorCode(), e.getErrorMessage(), e.getStatusCode(), e);
            throw new Exception("腾讯云COS服务异常: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            logger.error("腾讯云COS客户端异常: {}", e.getMessage(), e);
            throw new Exception("腾讯云COS客户端异常: " + e.getMessage(), e);
        } finally {
            if (cosClient != null) {
                cosClient.shutdown();
            }
        }
    }
    
    @Override
    public boolean deleteFile(String fileUrl) {
        if (!isAvailable()) {
            return false;
        }
        
        COSClient cosClient = null;
        try {
            COSCredentials cred = new BasicCOSCredentials(
                config.getAccess().getKey().getId(),
                config.getAccess().getKey().getSecret()
            );
            
            Region region = new Region(config.getRegion());
            ClientConfig clientConfig = new ClientConfig(region);
            cosClient = new COSClient(cred, clientConfig);
            
            // 从URL中提取对象键
            String objectKey = extractObjectKeyFromUrl(fileUrl);
            
            // 删除对象
            cosClient.deleteObject(config.getBucket().getName(), objectKey);
            
            logger.info("从腾讯云COS删除文件成功: {}", fileUrl);
            return true;
            
        } catch (Exception e) {
            logger.error("从腾讯云COS删除文件失败: {}", fileUrl, e);
            return false;
        } finally {
            if (cosClient != null) {
                cosClient.shutdown();
            }
        }
    }
    
    @Override
    public String getServiceType() {
        return "tencent-cos";
    }
    
    /**
     * 生成对象键（文件在COS中的路径）
     */
    private String generateObjectKey(String filename) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String randomSuffix = String.valueOf(System.currentTimeMillis() % 10000);
        
        // 清理文件名：移除或替换特殊字符，保留扩展名
        String cleanFilename = sanitizeFilename(filename);
        
        return String.format("temp/%s_%s_%s", timestamp, randomSuffix, cleanFilename);
    }
    
    /**
     * 清理文件名，移除可能导致URL问题的特殊字符
     */
    private String sanitizeFilename(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "file";
        }
        
        // 提取文件扩展名
        String extension = "";
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            extension = filename.substring(lastDotIndex);
            filename = filename.substring(0, lastDotIndex);
        }
        
        // 替换中文字符、特殊符号为下划线或删除
        String cleanName = filename
            .replaceAll("[\\u4e00-\\u9fa5]", "_")  // 中文字符替换为下划线
            .replaceAll("[()（）\\[\\]{}【】]", "_")  // 括号替换为下划线
            .replaceAll("[\\s+]", "_")  // 空格替换为下划线
            .replaceAll("[^a-zA-Z0-9._-]", "_")  // 其他特殊字符替换为下划线
            .replaceAll("_{2,}", "_")  // 多个连续下划线替换为单个
            .replaceAll("^_|_$", "");  // 移除开头和结尾的下划线
        
        // 如果清理后为空，使用默认名称
        if (cleanName.isEmpty()) {
            cleanName = "file_" + System.currentTimeMillis();
        }
        
        // 限制文件名长度
        if (cleanName.length() > 50) {
            cleanName = cleanName.substring(0, 50);
        }
        
        return cleanName + extension;
    }
    
    /**
     * 生成公网访问URL
     */
    private String generatePublicUrl(String objectKey) {
        String urlPrefix = config.getUrl().getPrefix();
        if (urlPrefix != null && !urlPrefix.isEmpty()) {
            return urlPrefix + "/" + objectKey;
        }
        
        // 使用腾讯云COS的默认域名格式
        // 注意：objectKey已经是安全的，不需要再次编码
        return String.format("https://%s.cos.%s.myqcloud.com/%s",
            config.getBucket().getName(),
            config.getRegion(),
            objectKey
        );
    }
    
    /**
     * 从URL中提取对象键
     */
    private String extractObjectKeyFromUrl(String fileUrl) {
        // 腾讯云COS URL格式：https://bucket-name.cos.region.myqcloud.com/objectKey
        String bucketDomain = config.getBucket().getName() + ".cos." + config.getRegion() + ".myqcloud.com/";
        if (fileUrl.contains(bucketDomain)) {
            return fileUrl.substring(fileUrl.indexOf(bucketDomain) + bucketDomain.length());
        }
        return fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
    }
    
    /**
     * 根据文件名获取Content-Type
     */
    private String getContentType(String filename) {
        String extension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
        switch (extension) {
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "doc":
                return "application/msword";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "xls":
                return "application/vnd.ms-excel";
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "ppt":
                return "application/vnd.ms-powerpoint";
            case "pdf":
                return "application/pdf";
            default:
                return "application/octet-stream";
        }
    }
}
