<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head('积分管理')}">
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">

    <div class="content-wrapper">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                <i class="bi bi-award me-2"></i>积分管理
            </h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <a th:href="@{/reward-penalty/create}" class="btn btn-primary" sec:authorize="hasRole('ADMIN')">
                        <i class="bi bi-plus-circle me-1"></i>新建奖罚
                    </a>
                </div>
            </div>
        </div>

        <!-- 错误信息 -->
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <span th:text="${errorMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 成功信息 -->
        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <span th:text="${successMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div> <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="card-title mb-0" th:text="${totalItems}">0</h4>
                                <p class="card-text">总记录数</p>
                            </div>
                            <div>
                                <i class="bi bi-list-ul" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 查询条件 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-search me-2"></i>查询条件
                </h5>
            </div>
            <div class="card-body">
                <form th:action="@{/points-management}" method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="name" class="form-label">姓名（支持模糊查询）</label>
                        <!-- 优先显示下拉列表，选项来自 personnel；为了保留模糊查询能力，下面保留一个可编辑的输入框，当下拉选择为非空时会同步填写该输入框 -->
                        <select id="nameSelect" class="form-select mb-2">
                            <option value="">-- 请选择 --</option>
                            <option th:each="p : ${personnel}" th:value="${p}" th:text="${p}"
                                th:selected="${p == name}"></option>
                        </select>
                        <input type="text" class="form-control" id="name" name="name" th:value="${name}"
                            placeholder="输入姓名关键字或选择下拉列表">
                    </div>
                    <div class="col-md-3">
                        <label for="startDate" class="form-label">发生时间（开始）</label>
                        <input type="date" class="form-control" id="startDate" name="startDate" th:value="${startDate}">
                    </div>
                    <div class="col-md-3">
                        <label for="endDate" class="form-label">发生时间（结束）</label>
                        <input type="date" class="form-control" id="endDate" name="endDate" th:value="${endDate}">
                    </div>
                    <div class="col-md-3">
                        <label for="type" class="form-label">类型（支持模糊查询）</label>
                        <input type="text" class="form-control" id="type" name="type" th:value="${type}"
                            placeholder="输入类型关键字">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>查询
                        </button>
                        <a th:href="@{/points-management}" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-1"></i>重置
                        </a>
                        <button type="button" class="btn btn-info ms-2" id="btnQueryLatestPoints">
                            <i class="bi bi-people me-1"></i>查询所有人的存量积分
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 奖罚记录表格 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-table me-2"></i>所有奖罚记录
                </h5>
            </div>
            <div class="card-body">
                <div th:if="${#lists.isEmpty(records)}" class="text-center text-muted py-5">
                    <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                    <p class="mt-3">暂无奖罚记录</p>
                    <a th:href="@{/reward-penalty/create}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>创建第一条记录
                    </a>
                </div>
                <div th:if="${!#lists.isEmpty(records)}" class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark text-black">
                            <tr>
                                <th class="text-black">编号</th>
                                <th class="text-black">姓名</th>
                                <th class="text-black">类型</th>
                                <th class="text-black">事由</th>
                                <th class="text-black">变化积分</th>
                                <th class="text-black">存量积分</th>
                                <th class="text-black">发生时间</th>
                                <th class="text-black">备注</th>
                                <th class="text-black" sec:authorize="hasRole('ADMIN')">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="record, iterStat : ${records}">
                                <td th:text="${iterStat.count}"></td>
                                <td>
                                    <strong th:text="${record.name}"></strong>
                                <td>
                                    <span class="badge"
                                        th:classappend="${record.points > 0 ? 'bg-success' : 'bg-danger'}"
                                        th:text="${record.type}"></span>
                                </td>
                                <td>
                                    <!-- 违规原因以链接形式显示，优先提取任务ID，其次流程ID -->
                                    <span
                                        th:with="taskId=${#strings.contains(record.reason, '任务ID：') ? #strings.substringAfter(record.reason, '任务ID：') : ''},
                                                 processId=${#strings.contains(record.reason, '流程ID：') ? #strings.substringAfter(record.reason, '流程ID：') : ''}">
                                        <a th:if="${!#strings.isEmpty(taskId)}" th:href="@{/tasks/{id}(id=${taskId})}"
                                            target="_blank" th:text="${record.reason}"></a>
                                        <a th:if="${#strings.isEmpty(taskId) && !#strings.isEmpty(processId)}"
                                            th:href="@{/workflow/approval/{id}(id=${processId})}" target="_blank"
                                            th:text="${record.reason}"></a>
                                        <span th:if="${#strings.isEmpty(taskId) && #strings.isEmpty(processId)}"
                                            th:text="${record.reason}"></span>
                                    </span>
                                </td>
                                <td>
                                    <span th:classappend="${record.points > 0 ? 'text-success' : 'text-danger'}"
                                        th:text="${record.points > 0 ? '+' + record.points : record.points}"></span>
                                </td>
                                <td>
                                    <strong th:text="${record.totalPoints}"></strong>
                                </td>
                                <td th:text="${record.occurTime}"></td>
                                <td th:text="${record.remarks ?: '-'}"></td>
                                <td sec:authorize="hasRole('ADMIN')">
                                    <div class="btn-group" role="group">
                                        <a th:href="@{/reward-penalty/edit/{id}(id=${record.id})}"
                                            class="btn btn-sm btn-outline-primary" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" title="删除"
                                            data-bs-toggle="modal" th:data-bs-target="'#deleteModal' + ${record.id}">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>

                                    <!-- 删除确认模态框 -->
                                    <div class="modal fade" th:id="'deleteModal' + ${record.id}" tabindex="-1">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">确认删除</h5>
                                                    <button type="button" class="btn-close"
                                                        data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>您确定要删除以下奖罚记录吗？</p>
                                                    <div class="alert alert-info">
                                                        <strong>姓名:</strong> <span th:text="${record.name}"></span><br>
                                                        <strong>类型:</strong> <span th:text="${record.type}"></span><br>
                                                        <strong>事由:</strong> <span
                                                            th:text="${record.reason}"></span><br>
                                                        <strong>积分:</strong> <span th:text="${record.points}"></span>
                                                    </div>
                                                    <p class="text-danger">
                                                        <i class="bi bi-exclamation-triangle me-1"></i>
                                                        此操作不可撤销！
                                                    </p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary"
                                                        data-bs-dismiss="modal">取消</button>
                                                    <form th:action="@{/reward-penalty/delete/{id}(id=${record.id})}"
                                                        method="post" style="display: inline;">
                                                        <input type="hidden" name="currentUsername"
                                                            th:value="${currentUsername}">
                                                        <button type="submit" class="btn btn-danger">确认删除</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- 分页控件 -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span class="text-muted">显示 <span th:text="${#lists.size(records)}">0</span> 条记录，共 <span
                                    th:text="${totalItems}">0</span> 条</span>
                        </div>
                        <nav aria-label="Page navigation" th:if="${totalPages > 1}">
                            <ul class="pagination">
                                <!-- 首页 -->
                                <li class="page-item" th:classappend="${currentPage == 1 ? 'disabled' : ''}">
                                    <a class="page-link"
                                        th:href="@{/points-management(page=1, name=${name}, startDate=${startDate}, endDate=${endDate})}"
                                        aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <!-- 上一页 -->
                                <li class="page-item" th:classappend="${currentPage == 1 ? 'disabled' : ''}">
                                    <a class="page-link"
                                        th:href="@{/points-management(page=${currentPage - 1}, name=${name}, startDate=${startDate}, endDate=${endDate})}"
                                        aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <!-- 页码 -->
                                <li class="page-item" th:each="i : ${#numbers.sequence(1, totalPages)}"
                                    th:if="${(i >= currentPage - 2) && (i <= currentPage + 2)}"
                                    th:classappend="${i == currentPage ? 'active' : ''}">
                                    <a class="page-link"
                                        th:href="@{/points-management(page=${i}, name=${name}, startDate=${startDate}, endDate=${endDate})}"
                                        th:text="${i}"></a>
                                </li>
                                <!-- 下一页 -->
                                <li class="page-item" th:classappend="${currentPage == totalPages ? 'disabled' : ''}">
                                    <a class="page-link"
                                        th:href="@{/points-management(page=${currentPage + 1}, name=${name}, startDate=${startDate}, endDate=${endDate})}"
                                        aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <!-- 末页 -->
                                <li class="page-item" th:classappend="${currentPage == totalPages ? 'disabled' : ''}">
                                    <a class="page-link"
                                        th:href="@{/points-management(page=${totalPages}, name=${name}, startDate=${startDate}, endDate=${endDate})}"
                                        aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:inline="none">
        // 页面特定的JavaScript代码
        document.addEventListener('DOMContentLoaded', function () {
            var sel = document.getElementById('nameSelect');
            var input = document.getElementById('name');
            if (sel && input) {
                sel.addEventListener('change', function () {
                    input.value = this.value;
                });
            }

            // 查询所有人的存量积分按钮事件
            var btnLatest = document.getElementById('btnQueryLatestPoints');
            if (btnLatest) {
                btnLatest.addEventListener('click', function () {
                    window.location.href = '/points-management/latest-points';
                });
            }
        });
    </script>

</body>

</html>