package com.mylog.service;

import com.mylog.model.workflow.WorkflowInstance;
import com.mylog.model.workflow.WorkflowInstance.WorkflowStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 流程实例服务接口
 */
public interface WorkflowInstanceService {

    /**
     * 查找所有流程实例
     */
    List<WorkflowInstance> findAllInstances();

    /**
     * 分页查找所有流程实例
     */
    Page<WorkflowInstance> findAllInstances(Pageable pageable);
    
    /**
     * 使用原生SQL查询所有流程实例
     * 此方法能够更好地处理数据库中可能存在的异常日期时间格式
     */
    List<WorkflowInstance> findAllInstancesByNativeQuery();

    /**
     * 根据ID查找流程实例
     */
    Optional<WorkflowInstance> findInstanceById(Long id);

    /**
     * 根据模板ID查找流程实例列表
     */
    List<WorkflowInstance> findInstancesByTemplateId(Long templateId);

    /**
     * 根据状态查找流程实例列表
     */
    List<WorkflowInstance> findInstancesByStatus(WorkflowStatus status);

    /**
     * 根据发起人查找流程实例列表
     */
    List<WorkflowInstance> findInstancesByInitiator(String initiator);

    /**
     * 根据发起人和状态查找流程实例列表
     */
    List<WorkflowInstance> findInstancesByInitiatorAndStatus(String initiator, WorkflowStatus status);

    /**
     * 根据业务类型和业务ID查找流程实例列表
     */
    List<WorkflowInstance> findInstancesByBusiness(String businessType, Long businessId);

    /**
     * 创建流程实例
     */
    WorkflowInstance createInstance(WorkflowInstance instance);

    /**
     * 更新流程实例
     */
    WorkflowInstance updateInstance(WorkflowInstance instance);

    /**
     * 提交流程实例
     */
    WorkflowInstance submitInstance(Long instanceId, String username, String comment);

    /**
     * 提交流程实例（带附件）
     */
    WorkflowInstance submitInstance(Long instanceId, String username, String comment, String attachments);

    /**
     * 提交流程实例（带附件和下一步审批人）
     * @param instanceId 流程实例ID
     * @param username 提交人用户名
     * @param comment 提交意见
     * @param attachments 附件
     * @param nextApprover 指定的下一步审批人，可为null
     * @return 更新后的流程实例
     */
    WorkflowInstance submitInstance(Long instanceId, String username, String comment, String attachments, String nextApprover);

    /**
     * 审批流程实例
     * @param instanceId 流程实例ID
     * @param username 审批人用户名
     * @param comment 审批意见
     * @param attachments 附件
     * @param nextApprover 指定的下一步审批人，可为null
     * @return 更新后的流程实例
     */
    WorkflowInstance approveInstance(Long instanceId, String username, String comment, String attachments, String nextApprover);

    /**
     * 拒绝流程实例
     */
    WorkflowInstance rejectInstance(Long instanceId, String username, String comment, String attachments);

    /**
     * 转交流程实例
     */
    WorkflowInstance transferInstance(Long instanceId, String fromUsername, String toUsername, String comment);

    /**
     * 撤回流程实例
     */
    WorkflowInstance withdrawInstance(Long instanceId, String username, String comment);

    /**
     * 终止流程实例
     */
    WorkflowInstance terminateInstance(Long instanceId, String username, String comment);

    /**
     * 查找用户的待办任务列表
     */
    List<WorkflowInstance> findTodoTasks(String username);

    /**
     * 查找用户的待办任务列表（分页版本）
     */
    Page<WorkflowInstance> findTodoTasks(String username, Pageable pageable);

    /**
     * 查找用户的已办任务列表
     */
    List<WorkflowInstance> findDoneTasks(String username);

    /**
     * 查找用户的已办任务列表（分页版本）
     */
    Page<WorkflowInstance> findDoneTasks(String username, Pageable pageable);

    /**
     * 综合搜索流程实例
     */
    Page<WorkflowInstance> searchInstances(
            String keyword,
            WorkflowStatus status,
            String initiator,
            Long templateId,
            String businessType,
            String businessIdStr,
            Pageable pageable);
            
    /**
     * 判断流程实例的下一步是否需要动态指定审批人
     * @param instance 流程实例
     * @return 如果下一步需要动态指定审批人，返回true；否则返回false
     */
    boolean isNextStepDynamicApprover(WorkflowInstance instance);
    
    /**
     * 删除流程实例
     * 
     * @param instanceId 流程实例ID
     * @return 是否删除成功
     */
    boolean deleteInstance(Long instanceId);
    
    /**
     * 删除草稿状态的流程实例
     * 只允许发起人删除自己的草稿流程
     * 
     * @param instanceId 流程实例ID
     * @param initiator 发起人用户名
     * @return 是否删除成功
     */
    boolean deleteDraftInstance(Long instanceId, String initiator);
    
    /**
     * 获取处理中状态的流程实例数量
     * @return 处理中的流程实例数量
     */
    long countProcessingInstances();
    
    /**
     * 统计某个发起人在指定月份创建的包含特定标题关键词的流程数量
     * @param initiator 发起人用户名
     * @param titleKeyword 流程标题关键词
     * @param year 年份
     * @param month 月份
     * @return 符合条件的流程数量
     */
    long countInstancesByInitiatorAndTitleKeywordInMonth(String initiator, String titleKeyword, int year, int month);
}
