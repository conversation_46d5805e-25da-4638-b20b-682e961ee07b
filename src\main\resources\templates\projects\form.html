﻿<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head(${project.projectId == null ? '新建项目' : '编辑项目'})}">
    <meta charset="UTF-8">
    <title>新建/编辑项目</title>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script#customScript})}">
    <div class="content-wrapper">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2" th:text="${project.projectId == null ? '新建项目' : '编辑项目'}">新建/编辑项目</h1>
        </div>

        <!-- 表单 -->
        <div class="row">
            <div class="col-md-12">
                <form th:action="@{/projects/save}" method="post" th:object="${project}" class="needs-validation"
                    novalidate>
                    <input type="hidden" th:field="*{projectId}" />

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="projectCode" class="form-label">项目编号</label>
                            <input type="text" class="form-control" id="projectCode"
                                th:value="${project.projectCode != null ? project.projectCode : projectCode}" readonly>
                            <input type="hidden" th:field="*{projectCode}"
                                th:value="${project.projectCode != null ? project.projectCode : projectCode}" />
                        </div>
                        <div class="col-md-4">
                            <label for="projectName" class="form-label">项目名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="projectName" th:field="*{projectName}" required>
                            <div class="invalid-feedback">请输入项目名称</div>
                        </div>
                        <div class="col-md-4">
                            <label for="customerName" class="form-label">客户名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="customerName" th:field="*{customerName}"
                                required>
                            <div class="invalid-feedback">请输入客户名称</div>
                        </div>

                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="projectType" class="form-label">项目类型<span class="text-danger">*</span></label>
                            <select class="form-select" id="projectType" th:field="*{projectType}" required>
                                <option value="">请选择项目类型</option>
                                <option th:each="type : ${projectTypes}" th:value="${type}" th:text="${type}">项目类型
                                </option>
                                <!-- 如果当前值不在列表中，添加一个选项 -->
                                <option
                                    th:if="${project.projectType != null && !projectTypes.contains(project.projectType)}"
                                    th:value="${project.projectType}" th:text="${project.projectType}" selected>
                                    [[${project.projectType}]]</option>
                            </select>
                            <div class="invalid-feedback">请选择项目类型</div>
                        </div>
                        <div class="col-md-4">
                            <label for="status" class="form-label">状态<span class="text-danger">*</span></label>
                            <select class="form-select" id="status" th:field="*{status}" required>
                                <option value="">请选择状态</option>
                                <!-- 只有新建项目时才显示"未开始"选项 -->
                                <option th:if="${project.projectId == null}" value="未开始">未开始</option>
                                <option value="进行中">进行中</option>
                                <option value="已暂停">已暂停</option>
                                <option value="已取消">已取消</option>
                                <!-- 管理员可以选择"已完成"状态 -->
                                <option th:if="${#authorization.expression('hasRole(''ADMIN'')')}" value="已完成">已完成
                                </option>
                                <!-- 编辑现有项目时，如果当前状态是"已完成"且非管理员，也要显示该选项（保持当前状态） -->
                                <option
                                    th:if="${project.projectId != null && project.status == '已完成' && !#authorization.expression('hasRole(''ADMIN'')')}"
                                    value="已完成">已完成</option>
                            </select>
                            <div class="invalid-feedback">请选择状态</div>
                        </div>
                        <div class="col-md-4">
                            <label for="risk" class="form-label">风险等级</label>
                            <select class="form-select" id="risk" th:field="*{risk}">
                                <option value="正常">正常</option>
                                <option value="低">低</option>
                                <option value="中">中</option>
                                <option value="高">高</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="responsible" class="form-label">责任人 <span class="text-danger">*</span></label>
                            <select class="form-select" id="responsible" th:field="*{responsible}" required>
                                <option value="">请选择责任人</option>
                                <option th:each="person : ${personnel}" th:value="${person}" th:text="${person}">
                                </option>
                            </select>
                            <div class="invalid-feedback">请选择责任人</div>
                        </div>
                        <div class="col-md-4">
                            <label for="supervisor1" class="form-label">监理人1</label>
                            <select class="form-select select2-control" id="supervisor1"
                                th:field="*{supervisor1}">
                                <option value="">请选择监理人1</option>
                                <option th:each="person : ${personnel}" th:value="${person}"
                                    th:text="${person}">监理人1</option>
                                <!-- 如果当前值不在列表中，添加一个选项 -->
                                <option
                                    th:if="${project.supervisor1 != null && !personnel.contains(project.supervisor1)}"
                                    th:value="${project.supervisor1}"
                                    th:text="${project.supervisor1}" selected>
                                    [[${project.supervisor1}]]</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="supervisor2" class="form-label">监理人2</label>
                            <select class="form-select select2-control" id="supervisor2"
                                th:field="*{supervisor2}">
                                <option value="">请选择监理人2</option>
                                <option th:each="person : ${personnel}" th:value="${person}"
                                    th:text="${person}">监理人2</option>
                                <!-- 如果当前值不在列表中，添加一个选项 -->
                                <option
                                    th:if="${project.supervisor2 != null && !personnel.contains(project.supervisor2)}"
                                    th:value="${project.supervisor2}"
                                    th:text="${project.supervisor2}" selected>
                                    [[${project.supervisor2}]]</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="electricalResponsible" class="form-label">电气责任人</label>
                            <select class="form-select select2-control" id="electricalResponsible"
                                th:field="*{electricalResponsible}">
                                <option value="">请选择电气责任人</option>
                                <option th:each="person : ${electricalPersonnel}" th:value="${person}"
                                    th:text="${person}">电气责任人</option>
                                <!-- 如果当前值不在列表中，添加一个选项 -->
                                <option
                                    th:if="${project.electricalResponsible != null && !electricalPersonnel.contains(project.electricalResponsible)}"
                                    th:value="${project.electricalResponsible}"
                                    th:text="${project.electricalResponsible}" selected>
                                    [[${project.electricalResponsible}]]</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="mechanicalResponsible" class="form-label">机械责任人</label>
                            <select class="form-select select2-control" id="mechanicalResponsible"
                                th:field="*{mechanicalResponsible}">
                                <option value="">请选择机械责任人</option>
                                <option th:each="person : ${mechanicalPersonnel}" th:value="${person}"
                                    th:text="${person}">机械责任人</option>
                                <!-- 如果当前值不在列表中，添加一个选项 -->
                                <option
                                    th:if="${project.mechanicalResponsible != null && !mechanicalPersonnel.contains(project.mechanicalResponsible)}"
                                    th:value="${project.mechanicalResponsible}"
                                    th:text="${project.mechanicalResponsible}" selected>
                                    [[${project.mechanicalResponsible}]]</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">                        <div class="col-md-4">
                            <label for="productPartNumber" class="form-label">产品料号</label>
                            <textarea class="form-control" id="productPartNumber"
                                th:field="*{productPartNumber}" rows="3"></textarea>
                        </div>

                        <div class="col-md-4">
                            <label for="salesOrderNumber" class="form-label">销售订单号：</label>
                            <textarea class="form-control" id="salesOrderNumber"
                                th:field="*{salesOrderNumber}" rows="3"></textarea>
                        </div>

                        <div class="col-md-4">
                            <label for="visionType" class="form-label">视觉类型<span class="text-danger">*</span></label>
                            <select class="form-select select2-control" id="visionType" name="visionTypeList" multiple
                                required>
                                <option th:each="type : ${visionTypes}" th:value="${type}" th:text="${type}"
                                    th:selected="${project.visionTypeList != null and project.visionTypeList.contains(type)}">
                                    视觉类型</option>
                            </select>
                            <div class="invalid-feedback">请选择至少一种视觉类型</div>

                        </div>



                    </div>

                    <div class="row mb-3">
                                                <div class="col-md-4">
                            <label for="salesResponsible" class="form-label">销售责任人</label>
                            <select class="form-select select2-control" id="salesResponsible"
                                th:field="*{salesResponsible}">
                                <option value="">请选择销售责任人</option>
                                <option th:each="person : ${salesPersonnel}" th:value="${person}" th:text="${person}">
                                    销售责任人</option>
                                <!-- 如果当前值不在列表中，添加一个选项 -->
                                <option
                                    th:if="${project.salesResponsible != null && !salesPersonnel.contains(project.salesResponsible)}"
                                    th:value="${project.salesResponsible}" th:text="${project.salesResponsible}"
                                    selected>[[${project.salesResponsible}]]</option>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label for="visionCost" class="form-label">单机成本1（多种视觉时，此处只填自制）</label>
                            <input type="number" step="0.01" class="form-control" id="visionCost"
                                th:field="*{visionCost}" min="0">
                        </div>
                        <div class="col-md-4">
                            <label for="visionCost2" class="form-label">单机成本2</label>
                            <input type="number" step="0.01" class="form-control" id="visionCost2"
                                th:field="*{visionCost2}" min="0">
                        </div>

                    </div>

                    <div class="row mb-3">
                                                <div class="col-md-4">
                            <label for="quantity" class="form-label">设备数量</label>
                            <input type="number" step="0.01" class="form-control" id="quantity" th:field="*{quantity}"
                                min="0">
                        </div>

                        <div class="col-md-4">
                            <label for="totalCost1" class="form-label">总成本1</label>
                            <input type="number" step="0.01" class="form-control" id="totalCost1"
                                th:field="*{totalCost1}" min="0">
                        </div>
                        <div class="col-md-4">
                            <label for="totalCost2" class="form-label">总成本2</label>
                            <input type="number" step="0.01" class="form-control" id="totalCost2"
                                th:field="*{totalCost2}" min="0">
                        </div>
                    </div>


                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="cameraQuantity" class="form-label">相机数量</label>
                            <input type="number" step="1" class="form-control" id="cameraQuantity"
                                th:field="*{cameraQuantity}" min="0">
                        </div>

                        <div class="col-md-4">
                            <label for="difficultyCoefficient" class="form-label">难度系数</label>
                            <input type="number" step="0.01" class="form-control" id="difficultyCoefficient"
                                th:field="*{difficultyCoefficient}" min="0">
                        </div>
                        <div class="col-md-4">
                            <label for="ratedDurationDays" class="form-label">额定工期(天)（10 + 设备数量 * 相机数量 * 难度系数）</label>
                            <input type="number" step="0.01" class="form-control" id="ratedDurationDays"
                                th:field="*{ratedDurationDays}" placeholder="自动计算">
                            <small class="form-text text-muted">点击此输入框会自动计算额定工期</small>
                        </div>

                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="remarks" class="form-label">备注</label>
                            <textarea class="form-control" id="remarks" th:field="*{remarks}" rows="3"
                                placeholder="请输入项目备注信息"></textarea>
                        </div>
                    </div>

                    <div class="row mb-3" sec:authorize="hasRole('ADMIN')">
                        <div class="col-md-12">
                            <label for="note" class="form-label">注意事项</label>
                            <textarea class="form-control" id="note" th:field="*{note}" rows="3"
                                placeholder="请输入项目注意事项"></textarea>
                        </div>
                    </div>

                    <!-- 计划日期和自动创建任务行 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="plannedStartDate">计划开始日期：</label>
                                <input type="datetime-local" class="form-control" id="plannedStartDate"
                                    name="plannedStartDate" th:value="${project.plannedStartDate}" step="1"
                                    pattern="yyyy-MM-dd HH:mm:ss">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="plannedEndDate">计划结束日期：</label>
                                <input type="datetime-local" class="form-control" id="plannedEndDate"
                                    name="plannedEndDate" th:value="${project.plannedEndDate}" step="1"
                                    pattern="yyyy-MM-dd HH:mm:ss">
                            </div>
                        </div>
                        <div class="col-md-4" th:if="${project.projectId == null}">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="autoCreateTasks"
                                    name="autoCreateTasks" checked>
                                <label class="form-check-label" for="autoCreateTasks">
                                    自动创建订单任务
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 隐藏字段，保留实际开始和结束日期的值 -->
                    <input type="hidden" id="actualStartDate" name="actualStartDate"
                        th:value="${project.actualStartDate}" />
                    <input type="hidden" id="actualEndDate" name="actualEndDate" th:value="${project.actualEndDate}" />
                    <input type="hidden" name="createdBy" th:value="${project.createdBy}" />
                    <input type="hidden" name="createdDate" th:value="${project.createdDate}" />
                    <!-- 添加隐藏的来源页面字段 -->
                    <input type="hidden" name="referer" th:value="${referer}" />

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a th:href="${referer != null ? referer : '#'}" onclick="return handleCancel(event)"
                            class="btn btn-secondary me-md-2">取消</a>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script id="customScript">
        // 页面加载时标记为表单页面
        document.addEventListener('DOMContentLoaded', function () {
            if (typeof markAsFormPage === 'function') {
                markAsFormPage();
            }
        });

        // 引入必要的CSS
        const linkSelect2CSS = document.createElement('link');
        linkSelect2CSS.rel = 'stylesheet';
        linkSelect2CSS.href = 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css';
        document.head.appendChild(linkSelect2CSS);

        const linkSelect2Theme = document.createElement('link');
        linkSelect2Theme.rel = 'stylesheet';
        linkSelect2Theme.href = 'https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css';
        document.head.appendChild(linkSelect2Theme);

        // 引入必要的JS
        const scriptSelect2 = document.createElement('script');
        scriptSelect2.src = 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js';
        document.body.appendChild(scriptSelect2);

        const scriptHistoryTracker = document.createElement('script');
        scriptHistoryTracker.src = '/js/history-tracker.js';
        document.body.appendChild(scriptHistoryTracker);

        const scriptFormHistory = document.createElement('script');
        scriptFormHistory.src = '/js/form-history.js';
        document.body.appendChild(scriptFormHistory);

        // 等待Select2加载完成后初始化
        scriptSelect2.onload = function () {
            // 初始化Select2
            $('.select2-control').select2({
                theme: 'bootstrap-5',
                width: '100%',
                tags: true,
                placeholder: "请选择或输入...",
                allowClear: true
            });

            // 为视觉类型选择器配置特殊设置
            $('#visionType').select2({
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: "请选择视觉类型...",
                allowClear: true,
                closeOnSelect: false
            });
        };

        // 表单验证
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        }
                        form.classList.add('was-validated')
                    }, false)
                })
        })()

        // 表单提交前处理日期格式
        document.querySelector('form').addEventListener('submit', function (event) {
            event.preventDefault();

            try {
                // 获取日期时间字段
                const plannedStartDate = document.getElementById('plannedStartDate');
                const plannedEndDate = document.getElementById('plannedEndDate');

                // 创建隐藏字段存储格式化后的日期
                if (plannedStartDate.value) {
                    const startDate = new Date(plannedStartDate.value);
                    const formattedStartDate = startDate.getFullYear() + '-' +
                        String(startDate.getMonth() + 1).padStart(2, '0') + '-' +
                        String(startDate.getDate()).padStart(2, '0') + ' ' +
                        String(startDate.getHours()).padStart(2, '0') + ':' +
                        String(startDate.getMinutes()).padStart(2, '0') + ':00';

                    const startHidden = document.createElement('input');
                    startHidden.type = 'hidden';
                    startHidden.name = 'plannedStartDate';
                    startHidden.value = formattedStartDate;
                    this.appendChild(startHidden);
                }

                if (plannedEndDate.value) {
                    const endDate = new Date(plannedEndDate.value);
                    const formattedEndDate = endDate.getFullYear() + '-' +
                        String(endDate.getMonth() + 1).padStart(2, '0') + '-' +
                        String(endDate.getDate()).padStart(2, '0') + ' ' +
                        String(endDate.getHours()).padStart(2, '0') + ':' +
                        String(endDate.getMinutes()).padStart(2, '0') + ':00';

                    const endHidden = document.createElement('input');
                    endHidden.type = 'hidden';
                    endHidden.name = 'plannedEndDate';
                    endHidden.value = formattedEndDate;
                    this.appendChild(endHidden);
                }

                // 提交表单
                this.submit();
            } catch (error) {
                console.error('日期处理出错:', error);
                alert('日期格式化出错，请检查日期格式是否正确');
            }
        });

        // 处理取消按钮
        function handleCancel(event) {
            // 如果已经有处理函数，则使用它
            if (window.handleFormCancel) {
                return window.handleFormCancel(event);
            }
            return true; // 默认行为
        }        // 自动计算额定工期的函数
        function calculateRatedDurationDays() {
            const quantity = parseFloat(document.getElementById('quantity').value) || 0;
            const cameraQuantity = parseFloat(document.getElementById('cameraQuantity').value) || 0;
            const difficultyCoefficient = parseFloat(document.getElementById('difficultyCoefficient').value) || 0;

            const ratedDurationDays = 10 + quantity * cameraQuantity * difficultyCoefficient;
            document.getElementById('ratedDurationDays').value = ratedDurationDays.toFixed(2);
        }

        // 自动计算总成本1和总成本2的函数
        function calculateTotalCosts() {
            const visionCost = parseFloat(document.getElementById('visionCost').value) || 0;
            const visionCost2 = parseFloat(document.getElementById('visionCost2').value) || 0;
            const quantity = parseFloat(document.getElementById('quantity').value) || 0;

            // 计算总成本1 = 单机成本1 * 设备数量
            const totalCost1 = visionCost * quantity;
            document.getElementById('totalCost1').value = totalCost1.toFixed(2);

            // 计算总成本2 = 单机成本2 * 设备数量
            const totalCost2 = visionCost2 * quantity;
            document.getElementById('totalCost2').value = totalCost2.toFixed(2);
        }

        // 为相关输入框添加事件监听器
        document.addEventListener('DOMContentLoaded', function () {
            const ratedDurationDaysInput = document.getElementById('ratedDurationDays');
            if (ratedDurationDaysInput) {
                ratedDurationDaysInput.addEventListener('click', calculateRatedDurationDays);
                ratedDurationDaysInput.addEventListener('focus', calculateRatedDurationDays);
            }

            // 为单机成本1、单机成本2、设备数量输入框添加事件监听器
            const visionCostInput = document.getElementById('visionCost');
            const visionCost2Input = document.getElementById('visionCost2');
            const quantityInput = document.getElementById('quantity');

            if (visionCostInput) {
                visionCostInput.addEventListener('input', calculateTotalCosts);
                visionCostInput.addEventListener('change', calculateTotalCosts);
            }

            if (visionCost2Input) {
                visionCost2Input.addEventListener('input', calculateTotalCosts);
                visionCost2Input.addEventListener('change', calculateTotalCosts);
            }

            if (quantityInput) {
                quantityInput.addEventListener('input', calculateTotalCosts);
                quantityInput.addEventListener('change', calculateTotalCosts);
                // 设备数量变化时也重新计算额定工期
                quantityInput.addEventListener('input', calculateRatedDurationDays);
                quantityInput.addEventListener('change', calculateRatedDurationDays);
            }

            // 页面加载时执行一次计算（如果有初始值）
            calculateTotalCosts();
        });
    </script>
</body>

</html>