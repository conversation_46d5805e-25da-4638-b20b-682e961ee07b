package com.mylog.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * cpolar隧道穿透预览服务
 * 用于生成通过cpolar访问本地文件的公网URL，支持Office Web Viewer预览
 */
@Service
public class CpolarPreviewService {

    private static final Logger logger = LoggerFactory.getLogger(CpolarPreviewService.class);
    
    @Value("${cpolar.preview.enabled:false}")
    private boolean cpolarPreviewEnabled;
    
    @Value("${cpolar.public.domain:}")
    private String cpolarPublicDomain;
    
    @Value("${cpolar.preview.token.expire.minutes:60}")
    private int tokenExpireMinutes;
    
    // 临时token存储，用于验证预览访问的安全性
    private final Map<String, TokenInfo> previewTokens = new ConcurrentHashMap<>();
    
    // 定期清理过期token的调度器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    public CpolarPreviewService() {
        // 每10分钟清理一次过期token
        scheduler.scheduleAtFixedRate(this::cleanExpiredTokens, 10, 10, TimeUnit.MINUTES);
    }
    
    /**
     * 检查cpolar预览功能是否启用
     */
    public boolean isEnabled() {
        boolean hasValidDomain = cpolarPublicDomain != null && !cpolarPublicDomain.trim().isEmpty() 
                                && !cpolarPublicDomain.equals("your-domain.cpolar.top");
        return cpolarPreviewEnabled && hasValidDomain;
    }
    
    /**
     * 生成cpolar预览URL
     */
    public String generatePreviewUrl(String submitId, String fileNumber) {
        if (!isEnabled()) {
            throw new IllegalStateException("cpolar预览功能未启用或域名未配置");
        }
        
        // 生成临时访问token
        String token = generatePreviewToken(submitId, fileNumber);
        
        // 构建cpolar公网URL
        String previewUrl = String.format("https://%s/api/files/cpolar-preview/%s?file=%s&token=%s", 
                                        cpolarPublicDomain, submitId, fileNumber, token);
        
        logger.info("生成cpolar预览URL: submitId={}, fileNumber={}, url={}", submitId, fileNumber, previewUrl);
        
        return previewUrl;
    }
    
    /**
     * 生成预览访问token
     */
    private String generatePreviewToken(String submitId, String fileNumber) {
        // 创建token载荷
        String payload = submitId + ":" + fileNumber + ":" + System.currentTimeMillis();
        String token = Base64.getEncoder().encodeToString(payload.getBytes());
        
        // 计算过期时间
        LocalDateTime expireTime = LocalDateTime.now().plusMinutes(tokenExpireMinutes);
        
        // 存储token信息
        TokenInfo tokenInfo = new TokenInfo(submitId, fileNumber, expireTime);
        previewTokens.put(token, tokenInfo);
        
        logger.debug("生成预览token: {}, 过期时间: {}", token, expireTime);
        
        return token;
    }
    
    /**
     * 验证预览token的有效性
     */
    public boolean isValidPreviewToken(String token, String submitId, String fileNumber) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }
        
        // 清理token - 移除可能被Office Web Viewer添加的路径信息
        String cleanToken = token;
        if (token.contains("/")) {
            cleanToken = token.substring(0, token.indexOf("/"));
            logger.debug("清理token: 原始={}, 清理后={}", token, cleanToken);
        }
        
        TokenInfo tokenInfo = previewTokens.get(cleanToken);
        if (tokenInfo == null) {
            logger.warn("无效的预览token: 原始={}, 清理后={}", token, cleanToken);
            return false;
        }
        
        // 检查是否过期
        if (LocalDateTime.now().isAfter(tokenInfo.expireTime)) {
            logger.warn("预览token已过期: {}", cleanToken);
            previewTokens.remove(cleanToken);
            return false;
        }
        
        // 检查submitId和fileNumber是否匹配
        boolean valid = tokenInfo.submitId.equals(submitId) && tokenInfo.fileNumber.equals(fileNumber);
        
        if (!valid) {
            logger.warn("预览token参数不匹配: token={}, expected={}:{}, actual={}:{}", 
                       cleanToken, tokenInfo.submitId, tokenInfo.fileNumber, submitId, fileNumber);
        }
        
        return valid;
    }
    
    /**
     * 清理过期的token
     */
    private void cleanExpiredTokens() {
        LocalDateTime now = LocalDateTime.now();
        int removedCount = 0;
        
        for (Map.Entry<String, TokenInfo> entry : previewTokens.entrySet()) {
            if (now.isAfter(entry.getValue().expireTime)) {
                previewTokens.remove(entry.getKey());
                removedCount++;
            }
        }
        
        if (removedCount > 0) {
            logger.debug("清理了{}个过期的预览token", removedCount);
        }
    }
    
    /**
     * 获取当前有效token数量（用于监控）
     */
    public int getActiveTokenCount() {
        return previewTokens.size();
    }
    
    /**
     * 获取cpolar配置信息（调试用）
     */
    public Map<String, Object> getDebugInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("enabled", cpolarPreviewEnabled);
        info.put("domain", cpolarPublicDomain);
        info.put("tokenExpireMinutes", tokenExpireMinutes);
        info.put("activeTokens", getActiveTokenCount());
        info.put("isEnabled", isEnabled());
        return info;
    }

    /**
     * Token信息类
     */
    private static class TokenInfo {
        final String submitId;
        final String fileNumber;
        final LocalDateTime expireTime;
        
        TokenInfo(String submitId, String fileNumber, LocalDateTime expireTime) {
            this.submitId = submitId;
            this.fileNumber = fileNumber;
            this.expireTime = expireTime;
        }
    }
}
