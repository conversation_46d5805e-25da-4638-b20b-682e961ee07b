<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head(${action == 'create' ? '新建奖罚记录' : '编辑奖罚记录'})}">
    <meta charset="UTF-8">
    <title>奖罚记录 - 项目管理系统</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-plus-circle me-2" th:if="${action == 'create'}"></i>
                        <i class="bi bi-pencil me-2" th:if="${action == 'edit'}"></i>
                        <span th:text="${action == 'create' ? '新建奖罚记录' : '编辑奖罚记录'}">奖罚记录</span>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a th:href="@{/points-management}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-1"></i>返回列表
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 错误信息 -->
                <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span th:text="${errorMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-form-text me-2"></i>奖罚记录信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <form th:action="${action == 'create' ? '/reward-penalty/create' : '/reward-penalty/update/' + record.id}" 
                                      method="post" th:object="${record}">
                                    <input type="hidden" name="currentUsername" th:value="${currentUsername}">
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="name" class="form-label">
                                                <i class="bi bi-person me-1"></i>姓名 <span class="text-danger">*</span>
                                            </label>
                                            <select id="nameSelect" class="form-select mb-2">
                                                <option value="">-- 请选择 --</option>
                                                <option th:each="p : ${personnel}" th:value="${p}" th:text="${p}" th:selected="${p == record.name}"></option>
                                            </select>
                                            <input type="text" class="form-control" id="name" th:field="*{name}" placeholder="输入姓名关键字或选择下拉列表" required>
                                            <script th:inline="none">
                                                document.addEventListener('DOMContentLoaded', function() {
                                                    var sel = document.getElementById('nameSelect');
                                                    var input = document.getElementById('name');
                                                    if (sel && input) {
                                                        sel.addEventListener('change', function() {
                                                            input.value = this.value;
                                                        });
                                                    }
                                                });
                                            </script>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="type" class="form-label">
                                                <i class="bi bi-tag me-1"></i>奖罚类型 <span class="text-danger">*</span>
                                            </label>
                                            <input type="text" class="form-control" id="type" th:field="*{type}" placeholder="请输入类型，如奖励、违规、项目等" required>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="reason" class="form-label">
                                            <i class="bi bi-file-text me-1"></i>奖罚事由 <span class="text-danger">*</span>
                                        </label>
                                        <textarea class="form-control" id="reason" th:field="*{reason}" rows="3" 
                                                  placeholder="请详细描述奖罚原因" required></textarea>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="points" class="form-label">
                                                <i class="bi bi-star me-1"></i>积分变化 <span class="text-danger">*</span>
                                            </label>
                                            <input type="number" class="form-control" id="points" th:field="*{points}" 
                                                   placeholder="正数为奖励，负数为扣分" required>
                                            <div class="form-text">
                                                <i class="bi bi-info-circle me-1"></i>
                                                输入正数表示奖励积分，输入负数表示扣除积分
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="occurTime" class="form-label">
                                                <i class="bi bi-calendar me-1"></i>发生时间
                                            </label>
                                            <input type="datetime-local" class="form-control" id="occurTime" th:field="*{occurTime}">
                                            <div class="form-text">
                                                <i class="bi bi-info-circle me-1"></i>
                                                留空则使用当前时间
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="remarks" class="form-label">
                                            <i class="bi bi-chat-dots me-1"></i>备注
                                        </label>
                                        <textarea class="form-control" id="remarks" th:field="*{remarks}" rows="2" 
                                                  placeholder="可以添加额外的备注信息（可选）"></textarea>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a th:href="@{/points-management}" class="btn btn-secondary">
                                            <i class="bi bi-x-circle me-1"></i>取消
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-circle me-1"></i>
                                            <span th:text="${action == 'create' ? '创建记录' : '更新记录'}">保存</span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="bi bi-lightbulb me-1"></i>填写说明
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="small">
                                    <h6>积分规则：</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-check-circle text-success me-1"></i>正数：奖励积分</li>
                                        <li><i class="bi bi-x-circle text-danger me-1"></i>负数：扣除积分</li>
                                    </ul>

                                    <h6 class="mt-3">常见类型：</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-award text-warning me-1"></i>任务奖励：完成任务表现优秀</li>
                                        <li><i class="bi bi-trophy text-success me-1"></i>项目奖励：项目贡献突出</li>
                                        <li><i class="bi bi-exclamation-triangle text-danger me-1"></i>任务违规：未按时完成任务</li>
                                        <li><i class="bi bi-slash-circle text-danger me-1"></i>纪律违规：违反工作纪律</li>
                                    </ul>

                                    <h6 class="mt-3">注意事项：</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-info-circle text-info me-1"></i>系统将自动计算存量积分</li>
                                        <li><i class="bi bi-clock text-secondary me-1"></i>发生时间默认为当前时间</li>
                                        <li><i class="bi bi-shield-check text-primary me-1"></i>记录创建后可编辑但需谨慎</li>                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>

    <script>
        // 实时预览积分类型
        document.getElementById('points').addEventListener('input', function() {
            const value = this.value;
            const formText = this.nextElementSibling;
            
            if (value > 0) {
                formText.innerHTML = '<i class="bi bi-plus-circle text-success me-1"></i>奖励 ' + value + ' 积分';
            } else if (value < 0) {
                formText.innerHTML = '<i class="bi bi-dash-circle text-danger me-1"></i>扣除 ' + Math.abs(value) + ' 积分';
            } else {
                formText.innerHTML = '<i class="bi bi-info-circle me-1"></i>输入正数表示奖励积分，输入负数表示扣除积分';
            }
        });

        // 设置默认发生时间为当前时间
        document.addEventListener('DOMContentLoaded', function() {
            const occurTimeInput = document.getElementById('occurTime');
            if (!occurTimeInput.value) {
                const now = new Date();
                now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
                occurTimeInput.value = now.toISOString().slice(0, 16);
            }
        });
    </script>
</body>
</html>
