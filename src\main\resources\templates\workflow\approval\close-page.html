<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>操作完成</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f8f9fa;
        }
        .success-container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 400px;
        }
        .success-icon {
            color: #28a745;
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .success-message {
            color: #333;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        .countdown {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">✓</div>
        <div class="success-message">操作已完成！</div>
        <div class="countdown">页面将在 <span id="countdown">3</span> 秒后自动关闭</div>
    </div>

    <script>
        // 倒计时关闭页面
        let countdown = 3;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                // 尝试关闭窗口
                closeWindow();
            }
        }, 1000);
        
        function closeWindow() {
            try {
                // 方法1：直接关闭窗口
                window.close();
                
                // 如果window.close()不起作用，尝试其他方法
                setTimeout(() => {
                    // 方法2：尝试关闭父窗口
                    if (window.parent && window.parent !== window) {
                        window.parent.close();
                    } else {
                        // 方法3：返回上一页
                        if (window.history.length > 1) {
                            window.history.back();
                        } else {
                            // 方法4：跳转到主页
                            window.location.href = '/';
                        }
                    }
                }, 500);
            } catch (e) {
                console.error('关闭页面失败:', e);
                // 如果所有方法都失败，跳转到主页
                window.location.href = '/';
            }
        }
        
        // 立即尝试关闭（用户可能期望立即关闭）
        setTimeout(() => {
            window.close();
        }, 100);
    </script>
</body>
</html>
