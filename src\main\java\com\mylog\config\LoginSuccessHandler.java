package com.mylog.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.service.RewardPenaltyRecordService;
import com.mylog.util.UserAgentUtils;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 登录成功处理器，用于处理登录成功后的操作
 */
@Component
public class LoginSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {    private static final Logger logger = LoggerFactory.getLogger(LoginSuccessHandler.class);
    private final UserActivityLogService activityLogService;
    private final UserService userService;
    private final RewardPenaltyRecordService rewardPenaltyRecordService;

    @Autowired
    public LoginSuccessHandler(UserActivityLogService activityLogService,
                               UserService userService,
                               RewardPenaltyRecordService rewardPenaltyRecordService) {
        this.activityLogService = activityLogService;
        this.userService = userService;
        this.rewardPenaltyRecordService = rewardPenaltyRecordService;

        // 设置默认登录成功后跳转的页面
        setDefaultTargetUrl("/dashboard");
    }    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {

        logger.info("用户 {} 登录成功", authentication.getName());

        try {
            // 记录用户登录日志
            String username = authentication.getName();
            userService.findUserByUsername(username).ifPresent(user -> {
                String ipAddress = getClientIpAddress(request);
                String accessType = UserAgentUtils.getAccessType(request);
                activityLogService.logLogin(user.getUserId(), username, ipAddress, accessType);
                logger.info("已记录用户 {} 的登录日志，IP: {}，设备类型: {}", username, ipAddress, accessType);
                
                // 统计用户最近3天的奖罚记录数量
                try {
                    Long recentRecordCount = rewardPenaltyRecordService.countRecentRecords(username);
                    if (recentRecordCount > 0) {
                        // 将徽章数量存储到session中
                        HttpSession session = request.getSession();
                        session.setAttribute("recentRewardPenaltyCount", recentRecordCount);
                        logger.info("用户 {} 最近3天有 {} 条奖罚记录，已存储到session", username, recentRecordCount);
                    }
                } catch (Exception e) {
                    logger.error("统计用户 {} 最近3天奖罚记录时出错: {}", username, e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            logger.error("登录处理过程中发生错误: {}", e.getMessage(), e);
        }

        // 调用父类方法完成剩余的登录成功处理
        super.onAuthenticationSuccess(request, response, authentication);
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}