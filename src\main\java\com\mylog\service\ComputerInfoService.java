package com.mylog.service;

import com.mylog.model.ComputerInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ComputerInfoService {
    
    /**
     * 根据项目ID获取电脑信息列表
     */
    List<ComputerInfo> getComputerInfoByProjectId(Integer projectId);
    
    /**
     * 根据ID获取电脑信息
     */
    Optional<ComputerInfo> getComputerInfoById(Long id);
    
    /**
     * 保存电脑信息
     */
    ComputerInfo saveComputerInfo(ComputerInfo computerInfo);
    
    /**
     * 删除电脑信息
     */
    void deleteComputerInfo(Long id);
    
    /**
     * 获取所有电脑信息
     */
    List<ComputerInfo> getAllComputerInfo();
    
    /**
     * 根据机器码查找电脑信息
     */
    List<ComputerInfo> findByMachineCode(String machineCode);
    
    /**
     * 根据注册码查找电脑信息
     */
    List<ComputerInfo> findByLicenseCode(String licenseCode);
    
    /**
     * 分页获取所有电脑信息
     */
    Page<ComputerInfo> findAllComputerInfoPaged(Pageable pageable);
    
    /**
     * 动态搜索电脑信息
     */
    Page<ComputerInfo> dynamicSearchComputerInfo(Map<String, Object> searchCriteria, Pageable pageable);
    
    /**
     * 检查某个项目ID是否存在电脑信息记录
     */
    boolean hasComputerInfoByProjectId(Long projectId);
}
