package com.mylog.controller;

import com.mylog.model.ComputerInfo;
import com.mylog.model.Project;
import com.mylog.model.user.User;
import com.mylog.service.ComputerInfoService;
import com.mylog.service.ProjectService;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.service.OptionsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Controller
@RequestMapping("/computer-info")
public class ComputerInfoController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(ComputerInfoController.class);
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Autowired
    private ComputerInfoService computerInfoService;
    
    @Autowired
    private UserActivityLogService activityLogService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private OptionsService optionsService;
    
    @Autowired
    private ProjectService projectService;
    
    /**
     * 电脑信息管理页面
     */
    @GetMapping("/management")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String computerInfoManagement(Model model,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        logger.info("加载电脑信息管理页面，页码: {}, 每页大小: {}", page, size);
        
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            
            // 获取用户主题
            userService.findUserByUsername(currentUsername).ifPresent(user -> {
                String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
                model.addAttribute("userTheme", "theme-" + theme);
                logger.info("用户主题: {}", theme);
            });
            
            // 设置分页，按ID降序排序
            Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
            
            // 获取分页的电脑信息列表
            Page<ComputerInfo> computerInfoPage = computerInfoService.findAllComputerInfoPaged(pageable);
            
            logger.info("成功获取电脑信息列表，总数: {}, 当前页: {}, 每页大小: {}",
                    computerInfoPage.getTotalElements(), page, size);
            
            // 创建项目ID到项目名称的映射
            Map<Integer, String> projectNameMap = new HashMap<>();
            for (ComputerInfo computerInfo : computerInfoPage.getContent()) {
                if (computerInfo.getProjectId() != null && !projectNameMap.containsKey(computerInfo.getProjectId())) {
                    try {
                        Optional<Project> projectOpt = projectService.findProjectById(computerInfo.getProjectId().longValue());
                        if (projectOpt.isPresent()) {
                            projectNameMap.put(computerInfo.getProjectId(), projectOpt.get().getProjectName());
                        } else {
                            projectNameMap.put(computerInfo.getProjectId(), "项目不存在");
                        }
                    } catch (Exception e) {
                        logger.warn("查询项目ID {} 的项目名称失败: {}", computerInfo.getProjectId(), e.getMessage());
                        projectNameMap.put(computerInfo.getProjectId(), "查询失败");
                    }
                }
            }
            
            // 添加到模型
            model.addAttribute("computerInfoPage", computerInfoPage);
            model.addAttribute("projectNameMap", projectNameMap);
            model.addAttribute("activeMenu", "computerinfo");
            model.addAttribute("totalComputerInfo", computerInfoPage.getTotalElements());
            
            // 添加人员列表到模型中，用于创建人下拉选择
            List<String> personnelList = optionsService.getPersonnel();
            logger.info("电脑信息管理页面 - 获取到的人员数据数量: {}", personnelList.size());
            if (!personnelList.isEmpty()) {
                logger.info("电脑信息管理页面 - 前几个人员: {}", personnelList.subList(0, Math.min(3, personnelList.size())));
            }
            model.addAttribute("personnel", personnelList);
            
            return "computer-info/management";
            
        } catch (Exception e) {
            logger.error("加载电脑信息管理页面时出错: {}", e.getMessage(), e);
            model.addAttribute("error", "加载电脑信息列表失败: " + e.getMessage());
            return "error";
        }
    }
    
    /**
     * 高级搜索电脑信息
     */
    @GetMapping("/advanced-search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String advancedSearchComputerInfo(
            @RequestParam(required = false) List<String> fieldNames,
            HttpServletRequest request,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Model model) {
        
        logger.info("电脑信息高级搜索请求，字段数量: {}", fieldNames != null ? fieldNames.size() : 0);
        
        // 获取当前登录用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        
        // 获取用户主题
        userService.findUserByUsername(currentUsername).ifPresent(user -> {
            String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
            model.addAttribute("userTheme", "theme-" + theme);
            logger.info("用户主题: {}", theme);
        });
        
        // 如果没有指定字段，则返回管理页面
        if (fieldNames == null || fieldNames.isEmpty()) {
            logger.info("没有搜索字段，返回管理页面");
            return computerInfoManagement(model, page, size);
        }
        
        // 构建搜索条件
        Map<String, Object> searchCriteria = new HashMap<>();
        
        for (String fieldName : fieldNames) {
            if (fieldName == null || fieldName.trim().isEmpty()) {
                continue;
            }
            
            logger.info("处理搜索字段: {}", fieldName);
            
            String value = request.getParameter("field_" + fieldName);
            if (value != null && !value.trim().isEmpty()) {
                searchCriteria.put(fieldName, value.trim());
                logger.info("添加搜索条件: {} = {}", fieldName, value.trim());
            }
        }
        
        // 如果没有有效的查询条件，则返回所有电脑信息
        if (searchCriteria.isEmpty()) {
            logger.info("没有有效的搜索条件，返回所有电脑信息");
            return computerInfoManagement(model, page, size);
        }
        
        try {
            logger.info("执行动态搜索，条件数量: {}", searchCriteria.size());
            Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
            
            // 执行搜索
            Page<ComputerInfo> computerInfoPage = computerInfoService.dynamicSearchComputerInfo(searchCriteria, pageable);
            
            logger.info("搜索结果: 总数 = {}, 当前页电脑信息数 = {}",
                    computerInfoPage.getTotalElements(),
                    computerInfoPage.getContent().size());
            
            // 创建项目ID到项目名称的映射
            Map<Integer, String> projectNameMap = new HashMap<>();
            for (ComputerInfo computerInfo : computerInfoPage.getContent()) {
                if (computerInfo.getProjectId() != null && !projectNameMap.containsKey(computerInfo.getProjectId())) {
                    try {
                        Optional<Project> projectOpt = projectService.findProjectById(computerInfo.getProjectId().longValue());
                        if (projectOpt.isPresent()) {
                            projectNameMap.put(computerInfo.getProjectId(), projectOpt.get().getProjectName());
                        } else {
                            projectNameMap.put(computerInfo.getProjectId(), "项目不存在");
                        }
                    } catch (Exception e) {
                        logger.warn("查询项目ID {} 的项目名称失败: {}", computerInfo.getProjectId(), e.getMessage());
                        projectNameMap.put(computerInfo.getProjectId(), "查询失败");
                    }
                }
            }
            
            // 添加到模型
            model.addAttribute("computerInfoPage", computerInfoPage);
            model.addAttribute("projectNameMap", projectNameMap);
            model.addAttribute("activeMenu", "computerinfo");
            model.addAttribute("totalComputerInfo", computerInfoPage.getTotalElements());
            model.addAttribute("isAdvancedSearch", true);
            model.addAttribute("searchCriteria", searchCriteria);
            
            // 添加人员列表到模型中，用于创建人下拉选择
            List<String> personnelList = optionsService.getPersonnel();
            logger.info("电脑信息高级搜索页面 - 获取到的人员数据数量: {}", personnelList.size());
            if (!personnelList.isEmpty()) {
                logger.info("电脑信息高级搜索页面 - 前几个人员: {}", personnelList.subList(0, Math.min(3, personnelList.size())));
            }
            model.addAttribute("personnel", personnelList);
            
            return "computer-info/management";
            
        } catch (Exception e) {
            logger.error("搜索电脑信息时出错: {}", e.getMessage(), e);
            model.addAttribute("error", "搜索电脑信息失败: " + e.getMessage());
            return "error";
        }
    }
    
    /**
     * 显示新增电脑信息页面
     */
    @GetMapping("/new")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String showNewComputerInfoForm(@RequestParam(required = false) Integer projectId, Model model) {
        ComputerInfo computerInfo = new ComputerInfo();
        if (projectId != null) {
            computerInfo.setProjectId(projectId);
            
            // 查找同一项目ID的最新一条记录
            List<ComputerInfo> existingRecords = computerInfoService.getComputerInfoByProjectId(projectId);
            if (!existingRecords.isEmpty()) {
                // 按创建时间降序排列，获取最新一条记录
                ComputerInfo latestRecord = existingRecords.stream()
                    .filter(record -> record.getCreatedTime() != null)
                    .max((r1, r2) -> r1.getCreatedTime().compareTo(r2.getCreatedTime()))
                    .orElse(existingRecords.get(0)); // 如果没有创建时间，取第一条
                
                // 复制除ID和项目ID之外的其他信息
                computerInfo.setMainVersion(latestRecord.getMainVersion());
                computerInfo.setManagerVersion(latestRecord.getManagerVersion());
                computerInfo.setToolVersion(latestRecord.getToolVersion());
                computerInfo.setComputerModel(latestRecord.getComputerModel());
                computerInfo.setOperatingSystem(latestRecord.getOperatingSystem());
                computerInfo.setWorkstationInfo(latestRecord.getWorkstationInfo());
                // 注意：不复制机器码和注册码，因为这些通常是每台电脑唯一的
                // computerInfo.setMachineCode(latestRecord.getMachineCode());
                // computerInfo.setLicenseCode(latestRecord.getLicenseCode());
                
                logger.info("为项目ID {} 的新电脑信息记录预填充了来自记录ID {} 的信息", 
                    projectId, latestRecord.getId());
            }
        }
        model.addAttribute("computerInfo", computerInfo);
        model.addAttribute("projectId", projectId);
        return "computer-info/form";
    }
    
    /**
     * 显示编辑电脑信息页面
     */
    @GetMapping("/{id}/edit")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String showEditComputerInfoForm(@PathVariable Long id, Model model) {
        Optional<ComputerInfo> computerInfoOpt = computerInfoService.getComputerInfoById(id);
        if (computerInfoOpt.isPresent()) {
            model.addAttribute("computerInfo", computerInfoOpt.get());
            model.addAttribute("projectId", computerInfoOpt.get().getProjectId());
            return "computer-info/form";
        } else {
            return "redirect:/projects";
        }
    }
    
    /**
     * 保存电脑信息
     */
    @PostMapping("/save")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String saveComputerInfo(@ModelAttribute ComputerInfo computerInfo) {
        try {
            // 获取当前用户信息
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = auth.getName();
            
            // 判断是新建还是编辑
            boolean isNewRecord = computerInfo.getId() == null;
            String oldMachineCode = null;
            String oldLicenseCode = null;
            String oldWorkstationInfo = null;
            
            // 如果是新建记录，设置创建人和创建时间
            if (isNewRecord) {
                computerInfo.setCreatedBy(currentUsername);
                computerInfo.setCreatedTime(LocalDateTime.now().format(DATE_TIME_FORMATTER));
                logger.info("新建电脑信息，设置创建人: {}，创建时间: {}", currentUsername, computerInfo.getCreatedTime());
            } else {
                // 如果是编辑现有记录，保持原有的创建人和创建时间信息，并记录变更前的信息
                Optional<ComputerInfo> existingOpt = computerInfoService.getComputerInfoById(computerInfo.getId());
                if (existingOpt.isPresent()) {
                    ComputerInfo existing = existingOpt.get();
                    computerInfo.setCreatedBy(existing.getCreatedBy());
                    computerInfo.setCreatedTime(existing.getCreatedTime());
                    oldMachineCode = existing.getMachineCode();
                    oldLicenseCode = existing.getLicenseCode();
                    oldWorkstationInfo = existing.getWorkstationInfo();
                    logger.info("编辑电脑信息，保持原有创建人: {}，创建时间: {}", existing.getCreatedBy(), existing.getCreatedTime());
                }
            }
            
            ComputerInfo savedInfo = computerInfoService.saveComputerInfo(computerInfo);
            logger.info("电脑信息保存成功: {}", savedInfo);
            
            // 记录活动日志
            try {
                Optional<User> userOpt = userService.findUserByUsername(currentUsername);
                if (userOpt.isPresent()) {
                    User user = userOpt.get();
                    String description;
                    
                    if (isNewRecord) {
                        description = String.format("创建了电脑信息记录 ID: %d，机器码: %s", 
                            savedInfo.getId(), 
                            savedInfo.getMachineCode() != null ? savedInfo.getMachineCode() : "未设置");
                        
                        activityLogService.logCreate(
                            user.getUserId(),
                            currentUsername,
                            description,
                            getClientIpAddress(),
                            "ComputerInfo",
                            savedInfo.getId(),
                            getAccessType()
                        );
                        logger.info("已记录电脑信息创建活动日志: {}", description);
                    } else {
                        // 构建修改描述
                        StringBuilder changes = new StringBuilder();
                        if (oldMachineCode != null && !oldMachineCode.equals(savedInfo.getMachineCode())) {
                            changes.append(String.format("机器码: %s → %s; ", oldMachineCode, savedInfo.getMachineCode()));
                        }
                        if (oldLicenseCode != null && !oldLicenseCode.equals(savedInfo.getLicenseCode())) {
                            changes.append(String.format("注册码: %s → %s; ", oldLicenseCode, savedInfo.getLicenseCode()));
                        }
                        if (oldWorkstationInfo != null && !oldWorkstationInfo.equals(savedInfo.getWorkstationInfo())) {
                            changes.append(String.format("工位信息: %s → %s; ", oldWorkstationInfo, savedInfo.getWorkstationInfo()));
                        }
                        
                        if (changes.length() > 0) {
                            description = String.format("修改了电脑信息记录 ID: %d，变更内容: %s", 
                                savedInfo.getId(), changes.toString());
                        } else {
                            description = String.format("更新了电脑信息记录 ID: %d", savedInfo.getId());
                        }
                        
                        activityLogService.logUpdate(
                            user.getUserId(),
                            currentUsername,
                            description,
                            getClientIpAddress(),
                            "ComputerInfo",
                            savedInfo.getId(),
                            getAccessType()
                        );
                        logger.info("已记录电脑信息修改活动日志: {}", description);
                    }
                } else {
                    logger.warn("未找到当前用户信息，无法记录活动日志: {}", currentUsername);
                }
            } catch (Exception e) {
                logger.error("记录电脑信息活动日志时出错: {}", e.getMessage(), e);
                // 不影响主要功能，继续执行
            }
            
            // 返回到项目详情页面
            if (computerInfo.getProjectId() != null) {
                return "redirect:/projects/" + computerInfo.getProjectId();
            } else {
                return "redirect:/projects";
            }
        } catch (Exception e) {
            logger.error("保存电脑信息失败", e);
            return "redirect:/projects";
        }
    }
    
    /**
     * 删除电脑信息
     */
    @PostMapping("/{id}/delete")
    @PreAuthorize("hasRole('ADMIN')")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteComputerInfo(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取要删除的电脑信息（用于记录日志）
            Optional<ComputerInfo> computerInfoOpt = computerInfoService.getComputerInfoById(id);
            if (computerInfoOpt.isPresent()) {
                ComputerInfo computerInfo = computerInfoOpt.get();
                
                // 获取当前用户信息
                Authentication auth = SecurityContextHolder.getContext().getAuthentication();
                String currentUsername = auth.getName();
                
                // 删除电脑信息
                computerInfoService.deleteComputerInfo(id);
                logger.info("电脑信息删除成功: ID = {}", id);
                
                // 记录活动日志
                try {
                    Optional<User> userOpt = userService.findUserByUsername(currentUsername);
                    if (userOpt.isPresent()) {
                        User user = userOpt.get();
                        String description = String.format("删除了电脑信息记录 ID: %d，机器码: %s", 
                            computerInfo.getId(),
                            computerInfo.getMachineCode() != null ? computerInfo.getMachineCode() : "未设置");
                        
                        activityLogService.logDelete(
                            user.getUserId(),
                            currentUsername,
                            description,
                            getClientIpAddress(),
                            "ComputerInfo",
                            computerInfo.getId(),
                            getAccessType()
                        );
                        logger.info("已记录电脑信息删除活动日志: {}", description);
                    } else {
                        logger.warn("未找到当前用户信息，无法记录活动日志: {}", currentUsername);
                    }
                } catch (Exception e) {
                    logger.error("记录电脑信息删除活动日志时出错: {}", e.getMessage(), e);
                    // 不影响主要功能，继续执行
                }
                
                response.put("success", true);
                response.put("message", "电脑信息删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "电脑信息不存在");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            logger.error("删除电脑信息失败: ID = {}", id, e);
            response.put("success", false);
            response.put("message", "删除失败：" + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
