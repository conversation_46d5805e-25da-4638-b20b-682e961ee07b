<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head('我的项目')}">
    <meta charset="UTF-8">
    <title>我的项目</title>
    <script th:inline="javascript">
        // 将视觉类型列表保存为全局变量
        const visionTypeList = /*[[${visionTypes}]]*/[];
        console.log('视觉类型列表:', visionTypeList); // 添加调试日志

        // 将项目类型列表保存为全局变量
        const projectTypeList = /*[[${projectTypes}]]*/[];
        console.log('项目类型列表:', projectTypeList); // 添加调试日志
    </script>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">我的项目 <small class="fs-6">（<span class="badge bg-primary rounded-pill"
                        th:text="'进行中 ' + ${myInProgressProjectCount}">进行中 23</span>/<span
                        th:text="${projectPage.totalElements}">52</span>）</small></h1>
        </div>

        <!-- 搜索栏 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">项目搜索</h5>
                        <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse"
                            data-bs-target="#searchCollapse" aria-expanded="true" aria-controls="searchCollapse">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                    <div class="collapse show" id="searchCollapse">
                        <div class="card-body">
                            <!-- 高级搜索 -->
                            <form th:action="@{/projects/advanced-search}" method="get" class="row g-3"
                                id="advancedSearchForm">
                                <!-- 动态搜索条件 -->
                                <div id="searchConditions">
                                    <div class="search-condition row mb-3">
                                        <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                                            <select class="form-select search-field" onchange="updateValueField(this)">
                                                <option value="">选择字段</option>
                                                <option value="projectName">项目名称</option>
                                                <option value="projectCode">项目编号</option>
                                                <option value="customerName">客户名称</option>
                                                <option value="projectType">项目类型</option>
                                                <option value="visionType">视觉类型</option>
                                                <option value="status">状态</option>
                                                <option value="hasComputerInfo">编码</option>
                                                <option value="totalCost1">总成本1</option>
                                                <option value="totalCost2">总成本2</option>
                                                <option value="ratedDurationDays">额定工期</option>
                                                <option value="difficultyCoefficient">难度系数</option>
                                                <option value="cameraQuantity">相机数量</option>
                                                <option value="quantity">设备数量</option>
                                                <option value="salesOrderNumber">销售订单号</option>
                                                <option value="productPartNumber">料号</option>
                                            </select>
                                        </div>
                                        <div class="col-10 col-sm-5 col-md-7 value-container">
                                            <!-- 值输入框将根据选择的字段动态生成 -->
                                            <input type="text" class="form-control search-value" disabled
                                                placeholder="请先选择字段">
                                        </div>
                                        <div class="col-2 col-sm-1 col-md-2">
                                            <button type="button" class="btn btn-outline-danger"
                                                onclick="removeCondition(this)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 按钮组 - 更新为完全响应式布局 -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="d-flex flex-wrap gap-2">
                                            <button type="button" class="btn btn-outline-primary"
                                                onclick="addSearchCondition()">
                                                <i class="bi bi-plus"></i> 添加条件
                                            </button>
                                            <button type="button" class="btn btn-outline-primary"
                                                onclick="addTimeCondition()">
                                                <i class="bi bi-calendar"></i> 添加创建时间条件
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-search"></i> 应用筛选
                                            </button>
                                            <a th:href="@{/projects/my}" class="btn btn-outline-secondary">
                                                <i class="bi bi-arrow-counterclockwise"></i> 重置
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目列表 -->
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th style="width: 15%">项目名称</th>
                        <th style="width: 10%">客户名称</th>
                        <th style="width: 8%">负责人</th>
                        <th style="width: 10%">视觉类型</th>
                        <th style="width: 7%">状态</th>
                        <th style="width: 6%">编码</th>
                        <th style="width: 8%">总成本1</th>
                        <th style="width: 20%">进行中任务</th>
                        <th style="width: 15%">创建日期</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="project : ${projectPage.content}">
                        <td><a th:href="@{/projects/{id}(id=${project.projectId})}"
                                th:text="${project.projectName}">示例项目</a></td>
                        <td th:text="${project.customerName}">示例客户</td>
                        <td th:text="${project.responsible}">张三</td>
                        <td th:text="${project.visionType != null ? project.visionType.replace(',', '、') : '-'}">标准视觉
                        </td>
                        <td>
                            <div th:switch="${project.status}">
                                <span th:case="'进行中'" class="badge bg-primary" th:text="${project.status}">进行中</span>
                                <span th:case="'已完成'" class="badge bg-success" th:text="${project.status}">已完成</span>
                                <span th:case="'已暂停'" class="badge bg-warning" th:text="${project.status}">已暂停</span>
                                <span th:case="'已取消'" class="badge bg-dark" th:text="${project.status}">已取消</span>
                                <span th:case="'未开始'" class="badge bg-secondary" th:text="${project.status}">未开始</span>
                                <span th:case="*" class="badge bg-info" th:text="${project.status}">其他状态</span>
                            </div>
                        </td>
                        <td>
                            <span th:if="${project.hasComputerInfo == '有'}" class="badge bg-success">有</span>
                            <span th:if="${project.hasComputerInfo == '无'}" class="badge bg-secondary">无</span>
                            <span th:if="${project.hasComputerInfo == null or project.hasComputerInfo == ''}" class="badge bg-secondary">无</span>
                        </td>
                        <td
                            th:text="${project.totalCost1 != null ? #numbers.formatDecimal(project.totalCost1, 1, 2) + ' 元' : '-'}">
                            -</td>
                        <td th:text="${project.note ?: '-'}">-</td>
                        <td th:text="${project.createdDate}">2024-01-01 12:00:00</td>
                    </tr>
                    <tr th:if="${projectPage.empty}">
                        <td colspan="9" class="text-center">暂无项目</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页控件 -->
        <div class="d-flex justify-content-center mt-4" th:if="${projectPage.totalPages > 0}">
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    <!-- 首页 -->
                    <li class="page-item" th:classappend="${projectPage.first ? 'disabled' : ''}">
                        <a class="page-link pagination-link" href="#" data-page="0" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>

                    <!-- 上一页 -->
                    <li class="page-item" th:classappend="${projectPage.first ? 'disabled' : ''}">
                        <a class="page-link pagination-link" href="#" th:data-page="${projectPage.number - 1}"
                            aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>

                    <!-- 页码 -->
                    <th:block
                        th:with="maxPages=20,
                                            totalPages=${projectPage.totalPages},
                                            currentPage=${projectPage.number},
                                            visiblePages=${totalPages <= maxPages ? totalPages : maxPages},
                                            halfVisible=${visiblePages / 2},
                                            startPage=${totalPages <= maxPages ? 0 : (currentPage - halfVisible + 1 < 0 ? 0 : (currentPage + halfVisible >= totalPages ? totalPages - visiblePages : currentPage - halfVisible + 1))},
                                            endPage=${totalPages <= maxPages ? totalPages - 1 : (startPage + visiblePages - 1 >= totalPages - 1 ? totalPages - 1 : startPage + visiblePages - 1)}">

                        <!-- 第一页，当不在可见范围内时显示 -->
                        <li class="page-item" th:if="${startPage > 0}"
                            th:classappend="${currentPage == 0 ? 'active' : ''}">
                            <a class="page-link pagination-link" href="#" data-page="0">1</a>
                        </li>

                        <!-- 左侧省略号 -->
                        <li class="page-item disabled" th:if="${startPage > 1}">
                            <span class="page-link">...</span>
                        </li>

                        <!-- 中间页码 -->
                        <li class="page-item" th:each="i : ${#numbers.sequence(startPage, endPage)}"
                            th:classappend="${i == currentPage ? 'active' : ''}">
                            <a class="page-link pagination-link" href="#" th:data-page="${i}" th:text="${i + 1}">1</a>
                        </li>

                        <!-- 右侧省略号 -->
                        <li class="page-item disabled" th:if="${endPage < totalPages - 2}">
                            <span class="page-link">...</span>
                        </li>

                        <!-- 最后一页，当不在可见范围内时显示 -->
                        <li class="page-item" th:if="${endPage < totalPages - 1}"
                            th:classappend="${currentPage == totalPages - 1 ? 'active' : ''}">
                            <a class="page-link pagination-link" href="#" th:data-page="${totalPages - 1}"
                                th:text="${totalPages}">最后页</a>
                        </li>
                    </th:block>

                    <!-- 下一页 -->
                    <li class="page-item" th:classappend="${projectPage.last ? 'disabled' : ''}">
                        <a class="page-link pagination-link" href="#" th:data-page="${projectPage.number + 1}"
                            aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>

                    <!-- 末页 -->
                    <li class="page-item" th:classappend="${projectPage.last ? 'disabled' : ''}">
                        <a class="page-link pagination-link" href="#" th:data-page="${projectPage.totalPages - 1}"
                            aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <script th:src="@{/js/history-tracker.js}"></script>
    <script>
        // 格式化日期为标准格式 yyyy-MM-dd HH:mm:ss
        function formatDateToStandard(dateInput) {
            if (dateInput.value) {
                // 获取日期值
                const selectedDate = new Date(dateInput.value);

                // 格式化为 yyyy-MM-dd HH:mm:ss
                const year = selectedDate.getFullYear();
                const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
                const day = String(selectedDate.getDate()).padStart(2, '0');

                // 如果是开始日期，设置为当天开始时间 00:00:00
                // 如果是结束日期，设置为当天结束时间 23:59:59
                let hours = '00', minutes = '00', seconds = '00';
                if (dateInput.name === 'field_createdDate_end') {
                    hours = '23';
                    minutes = '59';
                    seconds = '59';
                }

                // 创建标准格式的日期字符串
                const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

                // 创建隐藏字段存储格式化后的日期
                let hiddenInput = document.querySelector(`input[type="hidden"][name="${dateInput.name}_formatted"]`);
                if (!hiddenInput) {
                    hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = `${dateInput.name}_formatted`;
                    dateInput.parentNode.appendChild(hiddenInput);
                }
                hiddenInput.value = formattedDate;

                console.log(`格式化日期: ${dateInput.name} = ${formattedDate}`);
            }
        }

        // 高级搜索相关函数
        function updateValueField(selectElement) {
            const fieldName = selectElement.value;
            const valueContainer = selectElement.closest('.search-condition').querySelector('.value-container');

            // 清空当前值容器
            valueContainer.innerHTML = '';

            if (!fieldName) {
                // 如果没有选择字段，显示禁用的输入框
                valueContainer.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';
                return;
            }

            // 根据字段类型创建不同的输入控件
            if (fieldName === 'status') {
                // 状态字段使用下拉列表
                const select = document.createElement('select');
                select.className = 'form-select search-value';
                select.name = 'field_' + fieldName;

                const options = [
                    { value: '', text: '全部状态' },
                    { value: '未开始', text: '未开始' },
                    { value: '进行中', text: '进行中' },
                    { value: '已完成', text: '已完成' },
                    { value: '已暂停', text: '已暂停' },
                    { value: '已取消', text: '已取消' }
                ];

                options.forEach(opt => {
                    const option = document.createElement('option');
                    option.value = opt.value;
                    option.textContent = opt.text;
                    select.appendChild(option);
                });

                valueContainer.appendChild(select);
            } else if (fieldName === 'visionType') {
                // 视觉类型字段使用下拉列表
                const select = document.createElement('select');
                select.className = 'form-select search-value';
                select.name = 'field_' + fieldName;

                // 添加空选项
                const emptyOption = document.createElement('option');
                emptyOption.value = '';
                emptyOption.textContent = '请选择视觉类型';
                select.appendChild(emptyOption);

                // 使用全局变量 visionTypeList
                visionTypeList.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    select.appendChild(option);
                });                // 添加下拉列表
                valueContainer.appendChild(select);
            } else if (fieldName === 'projectType') {
                // 项目类型字段使用下拉列表
                const select = document.createElement('select');
                select.className = 'form-select search-value';
                select.name = 'field_' + fieldName;

                // 添加空选项
                const emptyOption = document.createElement('option');
                emptyOption.value = '';
                emptyOption.textContent = '请选择项目类型';
                select.appendChild(emptyOption);

                // 使用全局变量 projectTypeList
                projectTypeList.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    select.appendChild(option);
                });

                // 添加下拉列表
                valueContainer.appendChild(select);
            } else if (fieldName === 'hasComputerInfo') {
                // 编码字段使用下拉列表
                const select = document.createElement('select');
                select.className = 'form-select search-value';
                select.name = 'field_' + fieldName;

                const options = [
                    { value: '', text: '请选择编码' },
                    { value: '有', text: '有' },
                    { value: '无', text: '无' }
                ];

                options.forEach(opt => {
                    const option = document.createElement('option');
                    option.value = opt.value;
                    option.textContent = opt.text;
                    select.appendChild(option);
                });

                valueContainer.appendChild(select);
            } else if (fieldName === 'totalCost1') {
                // 总成本1字段使用最小值和最大值输入框
                const rowDiv = document.createElement('div');
                rowDiv.className = 'row';

                // 最小值输入框
                const minColDiv = document.createElement('div');
                minColDiv.className = 'col-6';

                const minInputGroup = document.createElement('div');
                minInputGroup.className = 'input-group';

                const minSpan = document.createElement('span');
                minSpan.className = 'input-group-text';
                minSpan.textContent = '最小';

                const minInput = document.createElement('input');
                minInput.type = 'number';
                minInput.step = '0.01';
                minInput.className = 'form-control';
                minInput.name = 'field_totalCost1_min';
                minInput.placeholder = '最小值';

                minInputGroup.appendChild(minSpan);
                minInputGroup.appendChild(minInput);
                minColDiv.appendChild(minInputGroup);

                // 最大值输入框
                const maxColDiv = document.createElement('div');
                maxColDiv.className = 'col-6';

                const maxInputGroup = document.createElement('div');
                maxInputGroup.className = 'input-group';

                const maxSpan = document.createElement('span');
                maxSpan.className = 'input-group-text';
                maxSpan.textContent = '最大';

                const maxInput = document.createElement('input');
                maxInput.type = 'number';
                maxInput.step = '0.01';
                maxInput.className = 'form-control';
                maxInput.name = 'field_totalCost1_max';
                maxInput.placeholder = '最大值';

                maxInputGroup.appendChild(maxSpan);
                maxInputGroup.appendChild(maxInput);
                maxColDiv.appendChild(maxInputGroup);

                // 组装行
                rowDiv.appendChild(minColDiv);
                rowDiv.appendChild(maxColDiv);

                // 添加到容器
                valueContainer.appendChild(rowDiv);
            } else if (fieldName === 'totalCost2') {
                // 总成本2字段使用最小值和最大值输入框
                const rowDiv = document.createElement('div');
                rowDiv.className = 'row';

                // 最小值输入框
                const minColDiv = document.createElement('div');
                minColDiv.className = 'col-6';

                const minInputGroup = document.createElement('div');
                minInputGroup.className = 'input-group';

                const minSpan = document.createElement('span');
                minSpan.className = 'input-group-text';
                minSpan.textContent = '最小';

                const minInput = document.createElement('input');
                minInput.type = 'number';
                minInput.step = '0.01';
                minInput.className = 'form-control';
                minInput.name = 'field_totalCost2_min';
                minInput.placeholder = '最小值';

                minInputGroup.appendChild(minSpan);
                minInputGroup.appendChild(minInput);
                minColDiv.appendChild(minInputGroup);

                // 最大值输入框
                const maxColDiv = document.createElement('div');
                maxColDiv.className = 'col-6';

                const maxInputGroup = document.createElement('div');
                maxInputGroup.className = 'input-group';

                const maxSpan = document.createElement('span');
                maxSpan.className = 'input-group-text';
                maxSpan.textContent = '最大';

                const maxInput = document.createElement('input');
                maxInput.type = 'number';
                maxInput.step = '0.01';
                maxInput.className = 'form-control';
                maxInput.name = 'field_totalCost2_max';
                maxInput.placeholder = '最大值';

                maxInputGroup.appendChild(maxSpan);
                maxInputGroup.appendChild(maxInput);
                maxColDiv.appendChild(maxInputGroup);

                // 组装行
                rowDiv.appendChild(minColDiv);
                rowDiv.appendChild(maxColDiv);

                // 添加到容器
                valueContainer.appendChild(rowDiv);
            } else if (fieldName === 'ratedDurationDays' || fieldName === 'difficultyCoefficient' || fieldName === 'cameraQuantity' || fieldName === 'quantity') {
                // 额定工期、难度系数、相机数量、设备数量字段使用最小值和最大值输入框
                const rowDiv = document.createElement('div');
                rowDiv.className = 'row';

                // 最小值输入框
                const minColDiv = document.createElement('div');
                minColDiv.className = 'col-6';

                const minInputGroup = document.createElement('div');
                minInputGroup.className = 'input-group';

                const minSpan = document.createElement('span');
                minSpan.className = 'input-group-text';
                minSpan.textContent = '最小';

                const minInput = document.createElement('input');
                minInput.type = 'number';
                minInput.step = (fieldName === 'cameraQuantity' || fieldName === 'quantity') ? '1' : '0.01';
                minInput.className = 'form-control';
                minInput.name = 'field_' + fieldName + '_min';
                minInput.placeholder = '最小值';

                minInputGroup.appendChild(minSpan);
                minInputGroup.appendChild(minInput);
                minColDiv.appendChild(minInputGroup);

                // 最大值输入框
                const maxColDiv = document.createElement('div');
                maxColDiv.className = 'col-6';

                const maxInputGroup = document.createElement('div');
                maxInputGroup.className = 'input-group';

                const maxSpan = document.createElement('span');
                maxSpan.className = 'input-group-text';
                maxSpan.textContent = '最大';

                const maxInput = document.createElement('input');
                maxInput.type = 'number';
                maxInput.step = (fieldName === 'cameraQuantity' || fieldName === 'quantity') ? '1' : '0.01';
                maxInput.className = 'form-control';
                maxInput.name = 'field_' + fieldName + '_max';
                maxInput.placeholder = '最大值';

                maxInputGroup.appendChild(maxSpan);
                maxInputGroup.appendChild(maxInput);
                maxColDiv.appendChild(maxInputGroup);

                // 组装行
                rowDiv.appendChild(minColDiv);
                rowDiv.appendChild(maxColDiv);

                // 添加到容器
                valueContainer.appendChild(rowDiv);
            } else {
                // 其他字段使用文本输入框
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'form-control search-value';
                input.name = 'field_' + fieldName;                input.placeholder = '输入' + selectElement.options[selectElement.selectedIndex].text;

                valueContainer.appendChild(input);
            }

            // 添加隐藏字段存储字段名
            const hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.name = 'fieldNames';
            // 对于总成本字段，需要映射到实际的后端字段名
            if (fieldName === 'totalCost1') {
                hiddenField.value = 'totalCost1';
            } else if (fieldName === 'totalCost2') {
                hiddenField.value = 'totalCost2';
            } else {
                hiddenField.value = fieldName;
            }
            valueContainer.appendChild(hiddenField);
        }

        function addSearchCondition() {
            const searchConditions = document.getElementById('searchConditions');
            const newCondition = document.createElement('div');
            newCondition.className = 'search-condition row mb-3';
            newCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select search-field" onchange="updateValueField(this)">                        <option value="">选择字段</option>
                        <option value="projectName">项目名称</option>
                        <option value="projectCode">项目编号</option>
                        <option value="customerName">客户名称</option>
                        <option value="projectType">项目类型</option>                        <option value="visionType">视觉类型</option>                        <option value="status">状态</option>
                        <option value="hasComputerInfo">编码</option>                        <option value="totalCost1">总成本1</option>
                        <option value="totalCost2">总成本2</option>
                        <option value="ratedDurationDays">额定工期</option>
                        <option value="difficultyCoefficient">难度系数</option>
                        <option value="cameraQuantity">相机数量</option>
                        <option value="quantity">设备数量</option>
                        <option value="salesOrderNumber">销售订单号</option>
                        <option value="productPartNumber">料号</option>
                    </select>
                </div>
                <div class="col-10 col-sm-5 col-md-7 value-container">
                    <input type="text" class="form-control search-value" disabled placeholder="请先选择字段">
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            searchConditions.appendChild(newCondition);
        }

        function removeCondition(button) {
            const condition = button.closest('.search-condition');
            if (document.querySelectorAll('.search-condition').length > 1) {
                condition.remove();
            } else {
                // 如果只有一个条件，则清空而不是删除
                const select = condition.querySelector('.search-field');
                select.value = '';
                updateValueField(select);
            }
        }

        // 添加时间条件函数
        function addTimeCondition() {
            const searchConditions = document.getElementById('searchConditions');
            const newCondition = document.createElement('div');
            newCondition.className = 'search-condition row mb-3';
            newCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select" disabled>
                        <option>创建时间</option>
                    </select>
                    <input type="hidden" name="fieldNames" value="createdDate">
                </div>
                <div class="col-6 col-sm-3 col-md-3 mb-2 mb-sm-0">
                    <div class="input-group">
                        <span class="input-group-text">从</span>
                        <input type="date" class="form-control" name="field_createdDate_start"
                               onchange="formatDateToStandard(this)">
                    </div>
                </div>
                <div class="col-6 col-sm-3 col-md-4 mb-2 mb-sm-0">
                    <div class="input-group">
                        <span class="input-group-text">到</span>
                        <input type="date" class="form-control" name="field_createdDate_end"
                               onchange="formatDateToStandard(this)">
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-2 text-end text-md-start mt-2 mt-md-0">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            searchConditions.appendChild(newCondition);
        }        // 表单提交前处理
        document.getElementById('advancedSearchForm').addEventListener('submit', function (event) {
            // 检查是否有有效的搜索条件
            const hasValidCondition = Array.from(document.querySelectorAll('.search-field'))
                .some(select => select.value !== '');

            // 检查是否有时间条件（创建时间或结束时间）
            const hasTimeCondition = Array.from(this.querySelectorAll('input[name="fieldNames"]'))
                .some(input => input.value === 'createdDate' || input.value === 'actualEndDate');

            if (!hasValidCondition && !hasTimeCondition) {
                event.preventDefault();
                alert('请至少选择一个搜索字段');
                return;
            }

            console.log("提交高级搜索表单...");
            console.log("当前表单Action:", this.action);

            // 确保表单提交到正确的URL
            this.action = window.location.origin + '/projects/advanced-search';
            console.log("设置表单Action为:", this.action);

            // 添加当前用户作为负责人的隐藏字段
            const responsibleField = document.createElement('input');
            responsibleField.type = 'hidden';
            responsibleField.name = 'fieldNames';
            responsibleField.value = 'responsible';
            this.appendChild(responsibleField);
            console.log("添加负责人字段:", responsibleField.value);

            const valueField = document.createElement('input');
            valueField.type = 'hidden';
            valueField.name = 'field_responsible';
            valueField.value = '[[${#authentication.name}]]';
            this.appendChild(valueField);
            console.log("添加负责人值:", valueField.value);            // 检查是否有总成本1条件
            const totalCost1Fields = Array.from(this.querySelectorAll('.search-field'))
                .filter(select => select.value === 'totalCost1');

            // 如果有总成本1条件，确保fieldNames中含有totalCost1
            if (totalCost1Fields.length > 0) {
                console.log("检测到总成本1搜索条件");
                // 检查是否已有对应的field_totalCost1_min和field_totalCost1_max字段
                const hasCostMin = this.querySelector('input[name="field_totalCost1_min"]') !== null;
                const hasCostMax = this.querySelector('input[name="field_totalCost1_max"]') !== null;

                // 检查是否已经在fieldNames中有totalCost1
                const existingTotalCost1Field = Array.from(this.querySelectorAll('input[name="fieldNames"]'))
                    .find(input => input.value === 'totalCost1');

                if ((hasCostMin || hasCostMax) && !existingTotalCost1Field) {
                    // 确保fieldNames中有totalCost1
                    const totalCost1Field = document.createElement('input');
                    totalCost1Field.type = 'hidden';
                    totalCost1Field.name = 'fieldNames';
                    totalCost1Field.value = 'totalCost1';
                    this.appendChild(totalCost1Field);
                    console.log("添加总成本1字段名:", totalCost1Field.value);
                }
            }

            // 检查是否有相机数量条件
            const cameraQuantityFields = Array.from(this.querySelectorAll('.search-field'))
                .filter(select => select.value === 'cameraQuantity');

            if (cameraQuantityFields.length > 0) {
                console.log("检测到相机数量搜索条件");
                const hasCameraMin = this.querySelector('input[name="field_cameraQuantity_min"]') !== null;
                const hasCameraMax = this.querySelector('input[name="field_cameraQuantity_max"]') !== null;

                const existingCameraField = Array.from(this.querySelectorAll('input[name="fieldNames"]'))
                    .find(input => input.value === 'cameraQuantity');

                if ((hasCameraMin || hasCameraMax) && !existingCameraField) {
                    const cameraField = document.createElement('input');
                    cameraField.type = 'hidden';
                    cameraField.name = 'fieldNames';
                    cameraField.value = 'cameraQuantity';
                    this.appendChild(cameraField);
                    console.log("添加相机数量字段名:", cameraField.value);
                }
            }

            // 检查是否有额定工期条件
            const ratedDurationFields = Array.from(this.querySelectorAll('.search-field'))
                .filter(select => select.value === 'ratedDurationDays');

            if (ratedDurationFields.length > 0) {
                console.log("检测到额定工期搜索条件");
                const hasDurationMin = this.querySelector('input[name="field_ratedDurationDays_min"]') !== null;
                const hasDurationMax = this.querySelector('input[name="field_ratedDurationDays_max"]') !== null;

                const existingDurationField = Array.from(this.querySelectorAll('input[name="fieldNames"]'))
                    .find(input => input.value === 'ratedDurationDays');

                if ((hasDurationMin || hasDurationMax) && !existingDurationField) {
                    const durationField = document.createElement('input');
                    durationField.type = 'hidden';
                    durationField.name = 'fieldNames';
                    durationField.value = 'ratedDurationDays';
                    this.appendChild(durationField);
                    console.log("添加额定工期字段名:", durationField.value);
                }
            }

            // 检查是否有难度系数条件
            const difficultyFields = Array.from(this.querySelectorAll('.search-field'))
                .filter(select => select.value === 'difficultyCoefficient');

            if (difficultyFields.length > 0) {
                console.log("检测到难度系数搜索条件");
                const hasDifficultyMin = this.querySelector('input[name="field_difficultyCoefficient_min"]') !== null;
                const hasDifficultyMax = this.querySelector('input[name="field_difficultyCoefficient_max"]') !== null;

                const existingDifficultyField = Array.from(this.querySelectorAll('input[name="fieldNames"]'))
                    .find(input => input.value === 'difficultyCoefficient');

                if ((hasDifficultyMin || hasDifficultyMax) && !existingDifficultyField) {
                    const difficultyField = document.createElement('input');
                    difficultyField.type = 'hidden';
                    difficultyField.name = 'fieldNames';
                    difficultyField.value = 'difficultyCoefficient';
                    this.appendChild(difficultyField);
                    console.log("添加难度系数字段名:", difficultyField.value);
                }
            }            // 检查是否有总成本2条件
            const totalCost2Fields = Array.from(this.querySelectorAll('.search-field'))
                .filter(select => select.value === 'totalCost2');

            if (totalCost2Fields.length > 0) {
                console.log("检测到总成本2搜索条件");
                const hasCost2Min = this.querySelector('input[name="field_totalCost2_min"]') !== null;
                const hasCost2Max = this.querySelector('input[name="field_totalCost2_max"]') !== null;

                const existingTotalCost2Field = Array.from(this.querySelectorAll('input[name="fieldNames"]'))
                    .find(input => input.value === 'totalCost2');

                if ((hasCost2Min || hasCost2Max) && !existingTotalCost2Field) {
                    const totalCost2Field = document.createElement('input');
                    totalCost2Field.type = 'hidden';
                    totalCost2Field.name = 'fieldNames';
                    totalCost2Field.value = 'totalCost2';
                    this.appendChild(totalCost2Field);
                    console.log("添加总成本2字段名:", totalCost2Field.value);
                }
            }

            // 检查是否有设备数量条件
            const quantityFields = Array.from(this.querySelectorAll('.search-field'))
                .filter(select => select.value === 'quantity');

            if (quantityFields.length > 0) {
                console.log("检测到设备数量搜索条件");
                const hasQuantityMin = this.querySelector('input[name="field_quantity_min"]') !== null;
                const hasQuantityMax = this.querySelector('input[name="field_quantity_max"]') !== null;

                const existingQuantityField = Array.from(this.querySelectorAll('input[name="fieldNames"]'))
                    .find(input => input.value === 'quantity');

                if ((hasQuantityMin || hasQuantityMax) && !existingQuantityField) {
                    const quantityField = document.createElement('input');
                    quantityField.type = 'hidden';
                    quantityField.name = 'fieldNames';
                    quantityField.value = 'quantity';
                    this.appendChild(quantityField);
                    console.log("添加设备数量字段名:", quantityField.value);
                }
            }

            // 添加视图类型标记
            const viewTypeField = document.createElement('input');
            viewTypeField.type = 'hidden';
            viewTypeField.name = 'viewType';
            viewTypeField.value = 'myProjects';
            this.appendChild(viewTypeField);
            console.log("添加视图类型:", viewTypeField.value);

            // 记录所有表单字段
            const formData = new FormData(this);
            for (const [key, value] of formData.entries()) {
                console.log(`表单字段: ${key} = ${value}`);
            }
        });

        // 分页链接处理
        document.addEventListener('DOMContentLoaded', function () {
            // 记录页面加载信息
            console.log("页面加载完成: 我的项目");
            console.log("当前URL:", window.location.href);
            console.log("当前路径:", window.location.pathname);
            console.log("查询参数:", window.location.search);

            // 处理分页链接点击事件
            document.querySelectorAll('.pagination-link').forEach(link => {
                link.addEventListener('click', function (e) {
                    e.preventDefault();

                    // 获取目标页码
                    const page = this.getAttribute('data-page');
                    if (!page) return;

                    // 获取当前URL和参数
                    const url = new URL(window.location.href);
                    const params = url.searchParams;

                    // 更新页码参数
                    params.set('page', page);

                    // 确保视图类型参数存在
                    if (!params.has('viewType')) {
                        params.set('viewType', 'myProjects');
                    }

                    // 构建新的URL
                    url.search = params.toString();

                    // 跳转到新URL
                    window.location.href = url.toString();
                });
            });

            // 初始化：如果有URL参数，还原搜索条件
            const urlParams = new URLSearchParams(window.location.search);
            const fieldNames = urlParams.getAll('fieldNames');

            if (fieldNames.length > 0) {
                // 清空默认的搜索条件
                document.getElementById('searchConditions').innerHTML = '';

                // 为每个字段创建一个搜索条件
                fieldNames.forEach((fieldName, index) => {
                    // 跳过负责人字段，因为它是隐式添加的
                    if (fieldName === 'responsible') return;

                    if (fieldName === 'createdDate') {
                        // 创建时间条件
                        addTimeCondition();

                        // 设置时间范围值
                        const startDate = urlParams.get('field_createdDate_start');
                        const endDate = urlParams.get('field_createdDate_end');

                        if (startDate) {
                            document.querySelector('input[name="field_createdDate_start"]').value = startDate;
                        }

                        if (endDate) {
                            document.querySelector('input[name="field_createdDate_end"]').value = endDate;
                        }
                    } else if (fieldName === 'totalCost1') {
                        // 总成本1条件（实际字段）
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = 'totalCost1'; // 使用新的字段名
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_totalCost1_min');
                        const maxValue = urlParams.get('field_totalCost1_max');

                        console.log('恢复总成本1搜索条件 - 最小值:', minValue, '最大值:', maxValue);

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_totalCost1_min"]').value = minValue;
                        }

                        if (maxValue) {
                            lastCondition.querySelector('input[name="field_totalCost1_max"]').value = maxValue;
                        }
                    } else if (fieldName === 'totalCost1') {
                        // 总成本1条件（兼容性保留）
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值 - 兼容旧的字段名
                        const minValue = urlParams.get('field_totalCost1_min');
                        const maxValue = urlParams.get('field_totalCost1_max');

                        console.log('恢复总成本1搜索条件（兼容） - 最小值:', minValue, '最大值:', maxValue);

                        if (minValue) {
                            const minInput = lastCondition.querySelector('input[name="field_totalCost1_min"]');
                            if (minInput) {
                                minInput.value = minValue;
                            }
                        } if (maxValue) {
                            const maxInput = lastCondition.querySelector('input[name="field_totalCost1_max"]');
                            if (maxInput) {
                                maxInput.value = maxValue;
                            }
                        }
                    } else if (fieldName === 'totalCost2') {
                        // 总成本2条件（实际字段）
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = 'totalCost2'; // 使用新的字段名
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_totalCost2_min');
                        const maxValue = urlParams.get('field_totalCost2_max');

                        console.log('恢复总成本2搜索条件 - 最小值:', minValue, '最大值:', maxValue);

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_totalCost2_min"]').value = minValue;
                        }

                        if (maxValue) {
                            lastCondition.querySelector('input[name="field_totalCost2_max"]').value = maxValue;
                        }
                    } else if (fieldName === 'totalCost2') {
                        // 总成本2条件（兼容性保留）
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值 - 兼容旧的字段名                        const minValue = urlParams.get('field_totalCost2_min');
                        const maxValue = urlParams.get('field_totalCost2_max');

                        console.log('恢复总成本2搜索条件（兼容） - 最小值:', minValue, '最大值:', maxValue);

                        if (minValue) {
                            const minInput = lastCondition.querySelector('input[name="field_totalCost2_min"]');
                            if (minInput) {
                                minInput.value = minValue;
                            }
                        }

                        if (maxValue) {
                            const maxInput = lastCondition.querySelector('input[name="field_totalCost2_max"]');
                            if (maxInput) {
                                maxInput.value = maxValue;
                            }
                        }
                    } else if (fieldName === 'cameraQuantity') {
                        // 相机数量条件
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_cameraQuantity_min');
                        const maxValue = urlParams.get('field_cameraQuantity_max');

                        console.log('恢复相机数量搜索条件 - 最小值:', minValue, '最大值:', maxValue);

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_cameraQuantity_min"]').value = minValue;
                        }

                        if (maxValue) {
                            lastCondition.querySelector('input[name="field_cameraQuantity_max"]').value = maxValue;
                        }
                    } else if (fieldName === 'ratedDurationDays') {
                        // 额定工期条件
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_ratedDurationDays_min');
                        const maxValue = urlParams.get('field_ratedDurationDays_max');

                        console.log('恢复额定工期搜索条件 - 最小值:', minValue, '最大值:', maxValue);

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_ratedDurationDays_min"]').value = minValue;
                        }

                        if (maxValue) {
                            lastCondition.querySelector('input[name="field_ratedDurationDays_max"]').value = maxValue;
                        }
                    } else if (fieldName === 'difficultyCoefficient') {
                        // 难度系数条件
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_difficultyCoefficient_min');
                        const maxValue = urlParams.get('field_difficultyCoefficient_max');

                        console.log('恢复难度系数搜索条件 - 最小值:', minValue, '最大值:', maxValue);

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_difficultyCoefficient_min"]').value = minValue;
                        } if (maxValue) {
                            lastCondition.querySelector('input[name="field_difficultyCoefficient_max"]').value = maxValue;
                        }
                    } else if (fieldName === 'quantity') {
                        // 设备数量条件
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置最小值和最大值
                        const minValue = urlParams.get('field_quantity_min');
                        const maxValue = urlParams.get('field_quantity_max');

                        console.log('恢复设备数量搜索条件 - 最小值:', minValue, '最大值:', maxValue);

                        if (minValue) {
                            lastCondition.querySelector('input[name="field_quantity_min"]').value = minValue;
                        }

                        if (maxValue) {
                            lastCondition.querySelector('input[name="field_quantity_max"]').value = maxValue;
                        }
                    } else {
                        // 普通条件
                        addSearchCondition();
                        const conditions = document.querySelectorAll('.search-condition');
                        const lastCondition = conditions[conditions.length - 1];
                        const select = lastCondition.querySelector('.search-field');
                        select.value = fieldName;
                        updateValueField(select);

                        // 设置值
                        const valueField = lastCondition.querySelector('.search-value');
                        if (valueField) {
                            valueField.value = urlParams.get('field_' + fieldName) || '';
                        }
                    }
                });
            }
        });
    </script>
</body>

</html>