<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head(${computerInfo.id != null ? '编辑电脑信息' : '新增电脑信息'})}">
    <meta charset="UTF-8">
    <title>电脑信息表单</title>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-3 mb-4 border-bottom">
            <h1 class="h2 text-primary d-flex align-items-center">
                <i class="bi bi-laptop me-2"></i>
                <span th:text="${computerInfo.id != null ? '编辑电脑信息' : '新增电脑信息'}">电脑信息表单</span>
            </h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <a th:href="${projectId != null ? '/projects/' + projectId : '/projects'}"
                        class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i> 返回
                    </a>
                </div>
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white py-3">
                <h5 class="mb-0 d-flex align-items-center">
                    <i class="bi bi-laptop me-2"></i>
                    <span th:text="${computerInfo.id != null ? '编辑电脑信息' : '新增电脑信息'}">电脑信息表单</span>
                </h5>
            </div>
            <div class="card-body p-4">
                <!-- 必填字段说明 -->
                <div class="alert alert-warning mb-4">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>注意：</strong>标有 <span class="text-danger">*</span> 的字段为必填项。在计算注册码之前，请确保所有必填字段都已填写完整（备注字段可以为空）。
                </div>

                <form th:action="@{/computer-info/save}" method="post" th:object="${computerInfo}">
                    <input type="hidden" th:field="*{id}">
                    <input type="hidden" th:field="*{projectId}">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="projectId" class="form-label">项目ID</label>
                                <input type="number" class="form-control" id="projectIdDisplay"
                                    th:value="${computerInfo.projectId}" readonly>
                                <div class="form-text">项目ID是只读的，不能修改</div>
                            </div>

                            <!-- 预填充信息提示 -->
                            <div th:if="${computerInfo.id == null && computerInfo.mainVersion != null}" 
                                 class="alert alert-info mb-3">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>提示：</strong>表单已自动填充该项目的最新电脑信息，您可以根据需要进行修改。
                            </div>

                            <div class="mb-3">
                                <label for="machineCode" class="form-label">机器码 <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="machineCode" th:field="*{machineCode}" rows="1"
                                    placeholder="请输入机器码..." required></textarea>
                            </div>

                            <!-- 计算注册码按钮 -->
                            <div class="mb-3 d-flex justify-content-left">
                                <button type="button" class="btn btn-outline-info btn-lg" onclick="calculateLicense()"
                                    id="calculateLicenseBtn">
                                    <i class="bi bi-key me-2"></i> 计算注册码
                                </button>
                            </div>

                            <div class="mb-3">
                                <label for="licenseCode" class="form-label">注册码</label>
                                <textarea class="form-control" id="licenseCode" th:field="*{licenseCode}" rows="1"
                                    placeholder="请输入注册码..."></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="remark" class="form-label">备注</label>
                                <textarea class="form-control" id="remark" th:field="*{remark}" rows="4"
                                    placeholder="请输入备注信息..."></textarea>
                            </div>

                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="computerModel" class="form-label">电脑型号 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="computerModel" th:field="*{computerModel}"
                                    placeholder="请输入电脑型号" required>
                            </div>

                            <div class="mb-3">
                                <label for="operatingSystem" class="form-label">操作系统 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="operatingSystem"
                                    th:field="*{operatingSystem}" placeholder="请输入操作系统" required>
                            </div>


                            <div class="mb-3">
                                <label for="mainVersion" class="form-label">主程序版本 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="mainVersion" th:field="*{mainVersion}"
                                    placeholder="请输入主程序版本" required>
                            </div>

                            <div class="mb-3">
                                <label for="managerVersion" class="form-label">管理器版本 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="managerVersion" th:field="*{managerVersion}"
                                    placeholder="请输入管理器版本" required>
                            </div>

                            <div class="mb-3">
                                <label for="toolVersion" class="form-label">工具库版本 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="toolVersion" th:field="*{toolVersion}"
                                    placeholder="请输入工具库版本" required>
                            </div>

                            <div class="mb-3">
                                <label for="workstationInfo" class="form-label">工位信息 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="workstationInfo" th:field="*{workstationInfo}"
                                    placeholder="请输入工位信息..." required>
                            </div>


                            <!-- 新建时的创建信息提示 -->
                            <div class="mb-3" th:if="${computerInfo.id == null}">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    保存后，当前登录用户将被记录为创建人，并记录创建时间
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a th:href="${projectId != null ? '/projects/' + projectId : '/projects'}"
                                    class="btn btn-secondary btn-lg px-4">
                                    <i class="bi bi-arrow-left me-2"></i>取消
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg px-4">
                                    <i class="bi bi-save me-2"></i>保存
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function () {
            // 表单提交验证
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                const requiredFields = [
                    { id: 'machineCode', name: '机器码' },
                    { id: 'computerModel', name: '电脑型号' },
                    { id: 'operatingSystem', name: '操作系统' },
                    { id: 'mainVersion', name: '主程序版本' },
                    { id: 'managerVersion', name: '管理器版本' },
                    { id: 'toolVersion', name: '工具库版本' },
                    { id: 'workstationInfo', name: '工位信息' }
                ];

                const missingFields = [];
                
                for (const field of requiredFields) {
                    const fieldElement = document.getElementById(field.id);
                    const value = fieldElement.value.trim();
                    if (!value) {
                        missingFields.push(field.name);
                    }
                }

                if (missingFields.length > 0) {
                    e.preventDefault();
                    alert('请填写以下必填字段：\n' + missingFields.join('、'));
                    // 聚焦到第一个未填写的字段
                    const firstMissingField = requiredFields.find(field => 
                        !document.getElementById(field.id).value.trim()
                    );
                    if (firstMissingField) {
                        document.getElementById(firstMissingField.id).focus();
                    }
                    return false;
                }
            });
        });

        // 计算注册码函数
        function calculateLicense() {
            // 验证所有必填字段
            const requiredFields = [
                { id: 'machineCode', name: '机器码' },
                { id: 'computerModel', name: '电脑型号' },
                { id: 'operatingSystem', name: '操作系统' },
                { id: 'mainVersion', name: '主程序版本' },
                { id: 'managerVersion', name: '管理器版本' },
                { id: 'toolVersion', name: '工具库版本' },
                { id: 'workstationInfo', name: '工位信息' }
            ];

            const missingFields = [];
            
            for (const field of requiredFields) {
                const fieldElement = document.getElementById(field.id);
                const value = fieldElement.value.trim();
                if (!value) {
                    missingFields.push(field.name);
                }
            }

            if (missingFields.length > 0) {
                alert('请先填写以下必填字段：\n' + missingFields.join('、') + '\n\n只有备注字段可以为空。');
                // 聚焦到第一个未填写的字段
                const firstMissingField = requiredFields.find(field => 
                    !document.getElementById(field.id).value.trim()
                );
                if (firstMissingField) {
                    document.getElementById(firstMissingField.id).focus();
                }
                return;
            }

            const machineCodeField = document.getElementById('machineCode');
            const machineCode = machineCodeField.value.trim();

            // 显示加载状态
            const btn = document.getElementById('calculateLicenseBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 计算中...';
            btn.disabled = true;

            // 发起计算注册码请求
            fetch('/projects/calculate-license', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    machineCode: machineCode
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 直接将注册码填入表单中的注册码字段
                        const licenseCodeField = document.getElementById('licenseCode');
                        licenseCodeField.value = data.licenseCode;

                        // 显示成功提示
                        alert('注册码计算成功并已自动填入！');

                        // 聚焦到注册码字段
                        licenseCodeField.focus();
                    } else {
                        // 显示错误信息
                        let errorMessage = data.message || '未知错误';
                        if (errorMessage.includes('网络请求失败') || errorMessage.includes('Connection refused') || errorMessage.includes('连接被拒绝')) {
                            errorMessage = '无法连接到机器码服务！\n\n' +
                                '启动成功后，请重新点击"计算注册码"按钮。\n\n' +
                                '详细说明';
                        }
                        alert('计算注册码失败：\n' + errorMessage);
                    }
                })
                .catch(error => {
                    console.error('计算注册码错误:', error);
                    alert('计算注册码失败，请稍后重试或联系管理员');
                })
                .finally(() => {
                    // 恢复按钮状态
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }
    </script>
</body>

</html>