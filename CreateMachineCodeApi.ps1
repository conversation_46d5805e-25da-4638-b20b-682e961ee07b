# 创建机器码Web API项目的PowerShell脚本
# 运行此脚本前，请确保已安装.NET SDK

param(
    [string]$ProjectPath = ".\MachineCodeApi"
)

Write-Host "正在创建机器码Web API项目..." -ForegroundColor Green

# 创建Web API项目
dotnet new webapi -n MachineCodeApi -o $ProjectPath

# 进入项目目录
Set-Location $ProjectPath

# 创建Models目录
New-Item -ItemType Directory -Path "Models" -Force

# 创建MachineCodeRequest.cs
@"
namespace MachineCodeApi.Models
{
    public class MachineCodeRequest
    {
        public string Key { get; set; } = string.Empty;
    }
}
"@ | Out-File -FilePath "Models\MachineCodeRequest.cs" -Encoding UTF8

# 创建MachineCodeResponse.cs
@"
namespace MachineCodeApi.Models
{
    public class MachineCodeResponse
    {
        public bool Success { get; set; }
        public string MachineCode { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }
}
"@ | Out-File -FilePath "Models\MachineCodeResponse.cs" -Encoding UTF8

# 创建MachineCodeController.cs
@"
using Microsoft.AspNetCore.Mvc;
using MachineCodeApi.Models;

namespace MachineCodeApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MachineCodeController : ControllerBase
    {
        private readonly ILogger<MachineCodeController> _logger;

        public MachineCodeController(ILogger<MachineCodeController> logger)
        {
            _logger = logger;
        }

        [HttpPost]
        public ActionResult<MachineCodeResponse> GetMachineCode([FromBody] MachineCodeRequest request)
        {
            try
            {
                _logger.LogInformation("收到机器码获取请求，参数: {Key}", request.Key);

                // 加载ClassPs.dll并调用方法
                // 注意：这里需要添加对ClassPs.dll的引用
                var simple3Des = new ClassPs.Simple3Des();
                var machineCode = simple3Des.EnumDC(request.Key ?? "tomsorvision");

                _logger.LogInformation("成功获取机器码");

                return Ok(new MachineCodeResponse
                {
                    Success = true,
                    MachineCode = machineCode,
                    Message = "成功获取机器码"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取机器码时发生错误");
                
                return Ok(new MachineCodeResponse
                {
                    Success = false,
                    MachineCode = string.Empty,
                    Message = `$"获取机器码失败: {ex.Message}"
                });
            }
        }

        [HttpGet("health")]
        public ActionResult<object> HealthCheck()
        {
            return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
        }
    }
}
"@ | Out-File -FilePath "Controllers\MachineCodeController.cs" -Encoding UTF8

# 更新Program.cs
@"
var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 配置CORS以允许Java应用访问
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowJavaApp", policy =>
    {
        policy.WithOrigins("http://localhost:8080")
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("AllowJavaApp");
app.UseAuthorization();
app.MapControllers();

app.Run();
"@ | Out-File -FilePath "Program.cs" -Encoding UTF8

Write-Host "项目创建完成！" -ForegroundColor Green
Write-Host "请执行以下步骤：" -ForegroundColor Yellow
Write-Host "1. 将ClassPs.dll复制到项目根目录" -ForegroundColor White
Write-Host "2. 添加对ClassPs.dll的引用（如果需要）" -ForegroundColor White
Write-Host "3. 运行 'dotnet run' 启动API服务" -ForegroundColor White
Write-Host "4. API将在 http://localhost:5000 上运行" -ForegroundColor White
