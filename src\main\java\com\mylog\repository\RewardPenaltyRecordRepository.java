package com.mylog.repository;

import com.mylog.model.RewardPenaltyRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 奖罚记录存储库接口
 * 提供对奖罚记录表的数据库操作
 */
@Repository
public interface RewardPenaltyRecordRepository extends JpaRepository<RewardPenaltyRecord, Long> { /**
                                                                                                   * 根据姓名查询奖罚记录，按发生时间降序排列（最新的在前）
                                                                                                   * 
                                                                                                   * @param name 姓名
                                                                                                   * @return 奖罚记录列表
                                                                                                   */
  // ...existing code...

  /**
   * 根据姓名查询奖罚记录，按创建时间降序排列（最新的在前）
   */
  List<RewardPenaltyRecord> findByNameOrderByCreateTimeDesc(String name);

  /**
   * 根据姓名模糊查询奖罚记录（分页）
   * 
   * @param name     姓名关键字
   * @param pageable 分页参数
   * @return 奖罚记录分页
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.name LIKE %:name%")
  org.springframework.data.domain.Page<RewardPenaltyRecord> findByNameContaining(@Param("name") String name,
      org.springframework.data.domain.Pageable pageable);

  /**
   * 根据奖罚类型查询记录
   * 
   * @param type 奖罚类型
   * @return 奖罚记录列表
   */
  List<RewardPenaltyRecord> findByType(String type);

  /**
   * 查询某个用户的奖励记录（积分为正数）
   * 
   * @param name 用户姓名
   * @return 奖励记录列表
   */
  List<RewardPenaltyRecord> findByNameAndPointsGreaterThan(String name, Integer points);

  /**
   * 查询某个用户的惩罚记录（积分为负数或零）
   * 
   * @param name 用户姓名
   * @return 惩罚记录列表
   */
  List<RewardPenaltyRecord> findByNameAndPointsLessThanEqual(String name, Integer points);

  /**
   * 按照创建时间范围查询记录
   * 
   * @param startTime 开始时间
   * @param endTime   结束时间
   * @return 符合时间范围的记录列表
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.createTime >= :startTime AND r.createTime <= :endTime")
  List<RewardPenaltyRecord> findByCreateTimeBetween(@Param("startTime") String startTime,
      @Param("endTime") String endTime);

  /**
   * 按照发生时间范围查询记录
   * 
   * @param startTime 开始时间
   * @param endTime   结束时间
   * @return 符合时间范围的记录列表
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.occurTime >= :startTime AND r.occurTime <= :endTime")
  List<RewardPenaltyRecord> findByOccurTimeBetween(@Param("startTime") String startTime,
      @Param("endTime") String endTime);

  /**
   * 按照发生时间范围查询记录（分页）
   * 
   * @param startTime 开始时间
   * @param endTime   结束时间
   * @param pageable  分页参数
   * @return 符合时间范围的记录分页
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.occurTime >= :startTime AND r.occurTime <= :endTime")
  org.springframework.data.domain.Page<RewardPenaltyRecord> findByOccurTimeBetween(
      @Param("startTime") String startTime,
      @Param("endTime") String endTime,
      org.springframework.data.domain.Pageable pageable);

  /**
   * 按照姓名和发生时间范围查询记录（分页）
   * 
   * @param name      姓名
   * @param startTime 开始时间
   * @param endTime   结束时间
   * @param pageable  分页参数
   * @return 符合条件的记录分页
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.name LIKE %:name% AND r.occurTime >= :startTime AND r.occurTime <= :endTime")
  org.springframework.data.domain.Page<RewardPenaltyRecord> findByNameContainingAndOccurTimeBetween(
      @Param("name") String name,
      @Param("startTime") String startTime,
      @Param("endTime") String endTime,
      org.springframework.data.domain.Pageable pageable);

  /**
   * 计算用户的总积分
   * 
   * @param name 用户姓名
   * @return 用户的总积分
   */
  @Query("SELECT SUM(r.points) FROM RewardPenaltyRecord r WHERE r.name = :name")
  Integer calculateTotalPointsByName(@Param("name") String name);

  /**
   * 查询所有奖励记录（积分为正数）
   * 
   * @return 奖励记录列表
   */
  List<RewardPenaltyRecord> findByPointsGreaterThan(Integer points);

  /**
   * 查询所有惩罚记录（积分为负数或零）
   * 
   * @return 惩罚记录列表
   */
  List<RewardPenaltyRecord> findByPointsLessThanEqual(Integer points);

  /**
   * 查询用户最新的存量积分记录
   * 
   * @param name 用户姓名
   * @return 用户的最新奖罚记录
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.name = :name ORDER BY r.id DESC")
  List<RewardPenaltyRecord> findLatestRecordByName(@Param("name") String name,
      org.springframework.data.domain.Pageable pageable);

  /**
   * 获取用户的最新存量积分
   * 
   * @param name 用户姓名
   * @return 用户的最新存量积分列表
   */
  @Query("SELECT r.totalPoints FROM RewardPenaltyRecord r WHERE r.name = :name ORDER BY r.id DESC")
  List<Integer> findLatestTotalPointsByName(@Param("name") String name,
      org.springframework.data.domain.Pageable pageable);

  /**
   * 统计用户最近N天的奖罚记录数量
   * 
   * @param name      用户姓名
   * @param startTime 开始时间
   * @return 记录数量
   */
  @Query("SELECT COUNT(r) FROM RewardPenaltyRecord r WHERE r.name = :name AND r.occurTime >= :startTime")
  Long countByNameAndOccurTimeAfter(@Param("name") String name, @Param("startTime") String startTime);

  /**
   * 按类型分页查询
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.type = :type")
  org.springframework.data.domain.Page<RewardPenaltyRecord> findByType(@Param("type") String type,
      org.springframework.data.domain.Pageable pageable);

  /**
   * 按类型和发生时间范围分页查询
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.type = :type AND r.occurTime >= :startTime AND r.occurTime <= :endTime")
  org.springframework.data.domain.Page<RewardPenaltyRecord> findByTypeAndOccurTimeBetween(@Param("type") String type,
      @Param("startTime") String startTime, @Param("endTime") String endTime,
      org.springframework.data.domain.Pageable pageable);

  /**
   * 按类型模糊分页查询
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.type LIKE %:type%")
  org.springframework.data.domain.Page<RewardPenaltyRecord> findByTypeContaining(@Param("type") String type,
      org.springframework.data.domain.Pageable pageable);

  /**
   * 按类型模糊和发生时间范围分页查询
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.type LIKE %:type% AND r.occurTime >= :startTime AND r.occurTime <= :endTime")
  org.springframework.data.domain.Page<RewardPenaltyRecord> findByTypeContainingAndOccurTimeBetween(
      @Param("type") String type, @Param("startTime") String startTime, @Param("endTime") String endTime,
      org.springframework.data.domain.Pageable pageable);

  /**
   * 按姓名模糊、类型模糊和发生时间范围分页查询
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.name LIKE %:name% AND r.type LIKE %:type% AND r.occurTime >= :startTime AND r.occurTime <= :endTime")
  org.springframework.data.domain.Page<RewardPenaltyRecord> findByNameContainingAndTypeContainingAndOccurTimeBetween(
      @Param("name") String name, @Param("type") String type, @Param("startTime") String startTime,
      @Param("endTime") String endTime, org.springframework.data.domain.Pageable pageable);

  /**
   * 按姓名模糊和类型模糊分页查询
   */
  @Query("SELECT r FROM RewardPenaltyRecord r WHERE r.name LIKE %:name% AND r.type LIKE %:type%")
  org.springframework.data.domain.Page<RewardPenaltyRecord> findByNameContainingAndTypeContaining(
      @Param("name") String name, @Param("type") String type, org.springframework.data.domain.Pageable pageable);


      /**
   * 查询所有姓名不同人员的最后一条积分记录
   */
//   @Query("SELECT t1.* FROM RewardPenaltyRecord t1 " +
//       "INNER JOIN (SELECT name, MAX(create_time) as max_time FROM RewardPenaltyRecord GROUP BY name) t2 " +
//       "ON t1.name = t2.name AND t1.create_time = t2.max_time")

  @Query(value = "SELECT t1.* FROM RewardPenaltyRecord t1 " +
               "INNER JOIN (SELECT name, MAX(create_time) as max_time FROM RewardPenaltyRecord GROUP BY name) t2 " +
               "ON t1.name = t2.name AND t1.create_time = t2.max_time",
       nativeQuery = true)
  List<RewardPenaltyRecord> findLatestPointsForAllPersons();
}
