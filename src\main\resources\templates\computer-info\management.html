<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head('电脑信息管理')}">
    <meta charset="UTF-8">
    <title>电脑信息管理</title>
    <style>
        /* 调整表格单元格的内边距到最小 */
        .table td,
        .table th {
            padding: 0.25rem 0.25rem;
            white-space: normal;
            word-wrap: break-word;
            word-break: break-word;
        }

        /* 使所有单元格的内容都能换行显示 */
        .table td {
            max-width: none;
            overflow: visible;
        }

        /* 响应式调整：在小屏幕上进一步优化表格显示 */
        @media (max-width: 992px) {
            .table-responsive {
                overflow-x: auto;
            }

            .table td,
            .table th {
                min-width: 100px;
            }

            /* 机器码列给予更多空间 */
            .table td:nth-child(3) {
                min-width: 150px;
            }
        }

        /* 机器码样式 */
        .machine-code {
            max-height: 100px;
            overflow-y: auto;
            word-break: break-all;
        }

        /* 工位信息样式 */
        .workstation-info {
            max-height: 80px;
            overflow-y: auto;
            word-break: break-all;
        }

        /* 项目ID列样式 - 防止换行 */
        .table td:nth-child(2) {
            white-space: nowrap;
            min-width: 80px;
        }

        /* 创建时间列样式 - 防止换行 */
        .table td:nth-child(12) {
            white-space: nowrap;
            min-width: 120px;
        }
    </style>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">电脑信息管理 <small class="fs-6">（总计 <span class="badge bg-primary rounded-pill"
                        th:text="${totalComputerInfo ?: 0}">0</span> 条记录）</small></h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <a th:href="@{/computer-info/new}" class="btn btn-sm btn-primary" sec:authorize="hasRole('ADMIN')">
                        <i class="bi bi-plus-lg"></i> 新增电脑信息
                    </a>
                </div>
            </div>
        </div>

        <!-- 错误消息 -->
        <div th:if="${error}" class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle-fill"></i> <span th:text="${error}"></span>
        </div>

        <!-- 成功消息 -->
        <div th:if="${message}" class="alert alert-success" role="alert">
            <i class="bi bi-check-circle-fill"></i> <span th:text="${message}"></span>
        </div>

        <!-- 搜索栏 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">电脑信息搜索</h5>
                        <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse"
                            data-bs-target="#searchCollapse" aria-expanded="true" aria-controls="searchCollapse">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                    <div class="collapse show" id="searchCollapse">
                        <div class="card-body">
                            <!-- 高级搜索 -->
                            <form th:action="@{/computer-info/advanced-search}" method="get" class="row g-3"
                                id="advancedSearchForm">
                                <!-- 动态搜索条件 -->
                                <div id="searchConditions">
                                    <div class="search-condition row mb-3">
                                        <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                                            <select class="form-select search-field" onchange="updateValueField(this)">
                                                <option value="">选择字段</option>
                                                <option value="machineCode">机器码</option>
                                                <option value="licenseCode">注册码</option>
                                                <option value="createdBy">创建人</option>
                                                <option value="workstationInfo">工位信息</option>
                                                <option value="projectId">项目ID</option>
                                                <option value="projectName">项目名称</option>
                                                <option value="remark">备注</option>
                                            </select>
                                        </div>
                                        <div class="col-10 col-sm-5 col-md-7 value-container">
                                            <!-- 值输入框将根据选择的字段动态生成 -->
                                            <input type="text" class="form-control search-value" disabled
                                                placeholder="请先选择字段">
                                        </div>
                                        <div class="col-2 col-sm-1 col-md-2">
                                            <button type="button" class="btn btn-outline-danger"
                                                onclick="removeCondition(this)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 按钮组 -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="d-flex flex-wrap gap-2">
                                            <button type="button" class="btn btn-outline-primary"
                                                onclick="addSearchCondition()">
                                                <i class="bi bi-plus"></i> 添加条件
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-search"></i> 应用筛选
                                            </button>
                                            <a th:href="@{/computer-info/management}" class="btn btn-outline-secondary">
                                                <i class="bi bi-arrow-counterclockwise"></i> 重置
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 电脑信息列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">电脑信息列表</h5>
                <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse"
                    data-bs-target="#computerInfoListCollapse" aria-expanded="true" aria-controls="computerInfoListCollapse">
                    <i class="bi bi-chevron-down"></i>
                </button>
            </div>
            <div class="collapse show" id="computerInfoListCollapse">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead>
                                <tr>
                                    <th style="width: 4%">ID</th>
                                    <th style="width: 4%">项目ID</th>
                                    <th style="width: 16%">项目名称</th>
                                    <th style="width: 6%">机器码</th>
                                    <th style="width: 4%">注册码</th>
                                    <th style="width: 5%">主程序</th>
                                    <th style="width: 5%">管理器</th>
                                    <th style="width: 5%">工具库</th>
                                    <th style="width: 12%">电脑型号</th>
                                    <th style="width: 6%">操作系统</th>
                                    <th style="width: 5%">工位</th>
                                    <th style="width: 4%">创建人</th>
                                    <th style="width: 12%">创建时间</th>
                                    <th style="width: 4%">备注</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="computerInfo : ${computerInfoPage.content}">
                                    <td th:text="${computerInfo.id}">1</td>
                                    <td>
                                        <span th:if="${computerInfo.projectId != null}">
                                            <a th:href="@{'/projects/' + ${computerInfo.projectId}}" th:text="${computerInfo.projectId}" target="_blank">项目ID</a>
                                        </span>
                                        <span th:unless="${computerInfo.projectId != null}" class="text-muted">-</span>
                                    </td>
                                    <td>
                                        <span th:if="${computerInfo.projectId != null and projectNameMap.containsKey(computerInfo.projectId)}" 
                                              th:text="${projectNameMap.get(computerInfo.projectId)}" class="text-break">项目名称</span>
                                        <span th:unless="${computerInfo.projectId != null and projectNameMap.containsKey(computerInfo.projectId)}" 
                                              class="text-muted">-</span>
                                    </td>
                                    <td>
                                        <div class="machine-code" th:if="${computerInfo.machineCode != null and !computerInfo.machineCode.isEmpty()}"
                                            th:text="${computerInfo.machineCode}">机器码</div>
                                        <span th:unless="${computerInfo.machineCode != null and !computerInfo.machineCode.isEmpty()}" 
                                            class="text-muted">-</span>
                                    </td>
                                    <td>
                                        <div class="machine-code" th:if="${computerInfo.licenseCode != null and !computerInfo.licenseCode.isEmpty()}"
                                            th:text="${computerInfo.licenseCode}">注册码</div>
                                        <span th:unless="${computerInfo.licenseCode != null and !computerInfo.licenseCode.isEmpty()}" 
                                            class="text-muted">-</span>
                                    </td>
                                    <td>
                                        <span th:if="${computerInfo.mainVersion != null and !computerInfo.mainVersion.isEmpty()}" 
                                            th:text="${computerInfo.mainVersion}">主版本</span>
                                        <span th:unless="${computerInfo.mainVersion != null and !computerInfo.mainVersion.isEmpty()}" 
                                            class="text-muted">-</span>
                                    </td>
                                    <td>
                                        <span th:if="${computerInfo.managerVersion != null and !computerInfo.managerVersion.isEmpty()}" 
                                            th:text="${computerInfo.managerVersion}">管理版本</span>
                                        <span th:unless="${computerInfo.managerVersion != null and !computerInfo.managerVersion.isEmpty()}" 
                                            class="text-muted">-</span>
                                    </td>
                                    <td>
                                        <span th:if="${computerInfo.toolVersion != null and !computerInfo.toolVersion.isEmpty()}" 
                                            th:text="${computerInfo.toolVersion}">工具版本</span>
                                        <span th:unless="${computerInfo.toolVersion != null and !computerInfo.toolVersion.isEmpty()}" 
                                            class="text-muted">-</span>
                                    </td>
                                    <td>
                                        <span th:if="${computerInfo.computerModel != null and !computerInfo.computerModel.isEmpty()}" 
                                            th:text="${computerInfo.computerModel}">电脑型号</span>
                                        <span th:unless="${computerInfo.computerModel != null and !computerInfo.computerModel.isEmpty()}" 
                                            class="text-muted">-</span>
                                    </td>
                                    <td>
                                        <span th:if="${computerInfo.operatingSystem != null and !computerInfo.operatingSystem.isEmpty()}" 
                                            th:text="${computerInfo.operatingSystem}">操作系统</span>
                                        <span th:unless="${computerInfo.operatingSystem != null and !computerInfo.operatingSystem.isEmpty()}" 
                                            class="text-muted">-</span>
                                    </td>
                                    <td>
                                        <div class="workstation-info" th:if="${computerInfo.workstationInfo != null and !computerInfo.workstationInfo.isEmpty()}"
                                            th:text="${computerInfo.workstationInfo}">工位信息</div>
                                        <span th:unless="${computerInfo.workstationInfo != null and !computerInfo.workstationInfo.isEmpty()}" 
                                            class="text-muted">-</span>
                                    </td>
                                    <td th:text="${computerInfo.createdBy ?: '-'}">创建人</td>
                                    <td th:text="${computerInfo.createdTime ?: '-'}">创建时间</td>
                                    <td th:text="${computerInfo.remark ?: '-'}" class="text-break" style="max-width: 100px;">备注</td>
                                </tr>
                                <tr th:if="${computerInfoPage.empty}">
                                    <td colspan="13" class="text-center">暂无电脑信息</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- 分页控件 -->
                <div class="card-footer" th:if="${computerInfoPage != null && computerInfoPage.totalPages > 0}">
                    <div th:replace="~{fragments/pagination :: pagination(${computerInfoPage}, @{/computer-info/management})}"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script th:inline="javascript">
        // 全局变量：人员列表
        const personnelList = /*[[${personnel}]]*/[];
        console.log('人员列表:', personnelList);
    </script>
    <script>
        // 高级搜索相关函数
        function updateValueField(selectField) {
            const valueContainer = selectField.closest('.search-condition').querySelector('.value-container');
            const selectedField = selectField.value;

            if (!selectedField) {
                valueContainer.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';
                return;
            }

            // 获取当前值（如果存在）
            const currentValue = valueContainer.querySelector('input, select')?.value || '';

            let inputHtml = '';

            // 根据选择的字段类型生成不同的输入控件
            switch (selectedField) {
                case 'createdBy':
                    // 创建人下拉选择
                    inputHtml = `
                        <select name="field_${selectedField}" class="form-select search-value">
                            <option value="">请选择创建人</option>
                    `;

                    // 添加人员选项
                    console.log('添加人员选项:', personnelList);
                    personnelList.forEach(person => {
                        inputHtml += `<option value="${person}">${person}</option>`;
                    });

                    inputHtml += `
                        </select>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break;
                case 'projectId':
                    inputHtml = `
                        <input type="text" name="field_${selectedField}" class="form-control search-value" placeholder="请输入项目ID（支持模糊查询）">
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break;
                default:
                    inputHtml = `
                        <input type="text" name="field_${selectedField}" class="form-control search-value" placeholder="请输入${selectField.options[selectField.selectedIndex].text}">
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
            }
            valueContainer.innerHTML = inputHtml;
        }

        function addSearchCondition() {
            const searchConditions = document.getElementById('searchConditions');
            const newCondition = document.createElement('div');
            newCondition.className = 'search-condition row mb-3';
            newCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select search-field" onchange="updateValueField(this)">
                        <option value="">选择字段</option>
                        <option value="machineCode">机器码</option>
                        <option value="licenseCode">注册码</option>
                        <option value="createdBy">创建人</option>
                        <option value="workstationInfo">工位信息</option>
                        <option value="projectId">项目ID</option>
                        <option value="remark">备注</option>
                    </select>
                </div>
                <div class="col-10 col-sm-5 col-md-7 value-container">
                    <input type="text" class="form-control search-value" disabled placeholder="请先选择字段">
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            searchConditions.appendChild(newCondition);
        }

        function removeCondition(button) {
            const condition = button.closest('.search-condition');
            condition.remove();
        }

        // 添加带值的搜索条件函数
        function addSearchConditionWithValues(fieldName, fieldValue) {
            console.log(`添加带值的电脑信息搜索条件: 字段=${fieldName}, 值=${fieldValue}`);
            const conditions = document.getElementById('searchConditions');
            const condition = document.createElement('div');
            condition.className = 'search-condition row mb-3';
            condition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select search-field" onchange="updateValueField(this)">
                        <option value="">选择字段</option>
                        <option value="machineCode">机器码</option>
                        <option value="licenseCode">注册码</option>
                        <option value="createdBy">创建人</option>
                        <option value="workstationInfo">工位信息</option>
                        <option value="projectId">项目ID</option>
                        <option value="remark">备注</option>
                    </select>
                </div>
                <div class="col-10 col-sm-5 col-md-7 value-container">
                    <input type="text" class="form-control search-value" disabled placeholder="请先选择字段">
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            conditions.appendChild(condition);

            // 设置字段值
            const select = condition.querySelector('.search-field');
            select.value = fieldName;
            updateValueField(select);

            // 设置输入值
            setTimeout(() => {
                // 尝试查找输入框
                const input = condition.querySelector('input[name="field_' + fieldName + '"]');
                if (input) {
                    input.value = fieldValue;
                    return;
                }

                // 尝试查找下拉框（用于创建人字段）
                const selectField = condition.querySelector('select[name="field_' + fieldName + '"]');
                if (selectField) {
                    selectField.value = fieldValue;
                }
            }, 100);
        }

        document.addEventListener('DOMContentLoaded', function () {
            console.log('页面加载完成');
        });
    </script>
</body>

</html>
