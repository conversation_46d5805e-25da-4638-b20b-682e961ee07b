using Microsoft.AspNetCore.Mvc;
using MachineCodeApi.Models;
using System.Reflection;

namespace MachineCodeApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MachineCodeController : ControllerBase
    {
        private readonly ILogger<MachineCodeController> _logger;

        public MachineCodeController(ILogger<MachineCodeController> logger)
        {
            _logger = logger;
        }

        [HttpPost]
        public ActionResult<MachineCodeResponse> GetMachineCode([FromBody] MachineCodeRequest request)
        {
            _logger.LogInformation("=== 进入 GetMachineCode 方法 ===");
            try
            {
                _logger.LogInformation("收到机器码获取请求，参数: {Key}", request?.Key ?? "null");

                // 动态加载ClassPs.dll并调用方法
                string dllPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ClassPs.dll");
                
                if (!System.IO.File.Exists(dllPath))
                {
                    _logger.LogError("ClassPs.dll 文件不存在: {Path}", dllPath);
                    return Ok(new MachineCodeResponse
                    {
                        Success = false,
                        MachineCode = string.Empty,
                        Message = $"ClassPs.dll 文件不存在: {dllPath}"
                    });
                }

                // 加载程序集
                var assembly = Assembly.LoadFrom(dllPath);
                var simple3DesType = assembly.GetType("ClassPs.Simple3Des");
                
                if (simple3DesType == null)
                {
                    _logger.LogError("找不到 ClassPs.Simple3Des 类型");
                    return Ok(new MachineCodeResponse
                    {
                        Success = false,
                        MachineCode = string.Empty,
                        Message = "找不到 ClassPs.Simple3Des 类型"
                    });
                }

                // 创建实例并调用方法
                var instance = Activator.CreateInstance(simple3DesType);
                var enumDcMethod = simple3DesType.GetMethod("EnumDC");
                
                if (enumDcMethod == null)
                {
                    _logger.LogError("找不到 EnumDC 方法");
                    return Ok(new MachineCodeResponse
                    {
                        Success = false,
                        MachineCode = string.Empty,
                        Message = "找不到 EnumDC 方法"
                    });
                }

                var result = enumDcMethod.Invoke(instance, new object[] { request?.Key ?? "tomsorvision" });
                var machineCode = result?.ToString() ?? string.Empty;

                _logger.LogInformation("成功获取机器码: {MachineCode}", machineCode);

                return Ok(new MachineCodeResponse
                {
                    Success = true,
                    MachineCode = machineCode,
                    Message = "成功获取机器码"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取机器码时发生错误");
                
                // 如果是反射调用异常，获取内部异常信息
                string detailedMessage = ex.Message;
                if (ex.InnerException != null)
                {
                    detailedMessage += $" 内部异常: {ex.InnerException.Message}";
                    _logger.LogError("内部异常详情: {InnerException}", ex.InnerException);
                }
                
                return Ok(new MachineCodeResponse
                {
                    Success = false,
                    MachineCode = string.Empty,
                    Message = $"获取机器码失败: {detailedMessage}"
                });
            }
        }

        [HttpGet("health")]
        public ActionResult<object> HealthCheck()
        {
            _logger.LogInformation("健康检查端点被调用");
            return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
        }

        [HttpGet("test")]
        public ActionResult<string> Test()
        {
            _logger.LogInformation("测试端点被调用");
            return Ok("API服务正在运行");
        }

        [HttpPost("calculate-license")]
        public ActionResult<MachineCodeResponse> CalculateLicense([FromBody] MachineCodeRequest request)
        {
            _logger.LogInformation("=== 进入 CalculateLicense 方法 ===");
            try
            {
                _logger.LogInformation("收到计算注册码请求，机器码: {MachineCode}", request?.Key ?? "null");

                if (string.IsNullOrEmpty(request?.Key))
                {
                    return Ok(new MachineCodeResponse
                    {
                        Success = false,
                        MachineCode = string.Empty,
                        Message = "机器码不能为空"
                    });
                }

                // 动态加载ClassPs.dll并调用方法
                string dllPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ClassPs.dll");
                
                if (!System.IO.File.Exists(dllPath))
                {
                    _logger.LogError("ClassPs.dll 文件不存在: {Path}", dllPath);
                    return Ok(new MachineCodeResponse
                    {
                        Success = false,
                        MachineCode = string.Empty,
                        Message = $"ClassPs.dll 文件不存在: {dllPath}"
                    });
                }

                // 加载程序集
                var assembly = Assembly.LoadFrom(dllPath);
                var simple3DesType = assembly.GetType("ClassPs.Simple3Des");
                
                if (simple3DesType == null)
                {
                    _logger.LogError("找不到 ClassPs.Simple3Des 类型");
                    return Ok(new MachineCodeResponse
                    {
                        Success = false,
                        MachineCode = string.Empty,
                        Message = "找不到 ClassPs.Simple3Des 类型"
                    });
                }

                // 创建实例并调用EncryptPsw方法
                var instance = Activator.CreateInstance(simple3DesType);
                var encryptPswMethod = simple3DesType.GetMethod("EncryptPsw");
                
                if (encryptPswMethod == null)
                {
                    _logger.LogError("找不到 EncryptPsw 方法");
                    return Ok(new MachineCodeResponse
                    {
                        Success = false,
                        MachineCode = string.Empty,
                        Message = "找不到 EncryptPsw 方法"
                    });
                }

                var result = encryptPswMethod.Invoke(instance, new object[] { request.Key, "tomsorvision" });
                var licenseCode = result?.ToString() ?? string.Empty;

                _logger.LogInformation("成功计算注册码: {LicenseCode}", licenseCode);

                return Ok(new MachineCodeResponse
                {
                    Success = true,
                    MachineCode = licenseCode,
                    Message = "成功计算注册码"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算注册码时发生错误");
                
                // 如果是反射调用异常，获取内部异常信息
                string detailedMessage = ex.Message;
                if (ex.InnerException != null)
                {
                    detailedMessage += $" 内部异常: {ex.InnerException.Message}";
                    _logger.LogError("内部异常详情: {InnerException}", ex.InnerException);
                }
                
                return Ok(new MachineCodeResponse
                {
                    Success = false,
                    MachineCode = string.Empty,
                    Message = $"计算注册码失败: {detailedMessage}"
                });
            }
        }
    }
}
