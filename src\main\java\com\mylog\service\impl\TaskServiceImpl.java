package com.mylog.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mylog.model.Project;
import com.mylog.model.ProjectTask;
import com.mylog.model.SubTask;
import com.mylog.model.Submit2;
import com.mylog.repository.ProjectRepository;
import com.mylog.repository.ProjectTaskRepository;
import com.mylog.service.SubTaskService;
import com.mylog.service.Submit2Service;
import com.mylog.service.TaskService;

import com.mylog.service.WorkHoursLogService;
import com.mylog.service.WorkflowInstanceService;
import com.mylog.util.SecurityUtils;


@Service
public class TaskServiceImpl implements TaskService {

    @Autowired
    private ProjectTaskRepository taskRepository;

    @Autowired
    private SubTaskService subTaskService;

    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private Submit2Service submit2Service;

    @Autowired
    private WorkHoursLogService workHoursLogService;

    @Autowired
    private com.mylog.service.OptionsService optionsService;



    @Autowired
    @Lazy
    private WorkflowInstanceService workflowInstanceService;

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(TaskServiceImpl.class);

    @Override
    public List<ProjectTask> findAllTasks() {
        return taskRepository.findAll();
    }

    @Override
    public Page<ProjectTask> findAllTasks(Pageable pageable) {
        // 归档状态值1表示已归档，首先获取所有未归档项目中的任务（不分页）
        List<ProjectTask> allTasks = taskRepository.findTasksFromNonArchivedProjects(1);

        logger.info("findAllTasks: 原始查询结果共 {} 个任务", allTasks.size());

        // 记录排序前的状态分布
        Map<String, Long> statusCounts = allTasks.stream()
                .collect(Collectors.groupingBy(
                        task -> task.getStatus() != null ? task.getStatus() : "null",
                        Collectors.counting()));
        logger.info("排序前状态分布: {}", statusCounts);

        // 对所有任务进行排序：进行中状态的排在前面，然后按创建时间降序
        List<ProjectTask> sortedTasks = allTasks.stream()
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());

        // 记录排序后前5个任务的状态
        logger.info("排序后前5个任务的状态:");
        for (int i = 0; i < Math.min(5, sortedTasks.size()); i++) {
            ProjectTask task = sortedTasks.get(i);
            logger.info("第{}位: 任务ID={}, 状态={}, 创建时间={}",
                    i + 1, task.getTaskId(), task.getStatus(), task.getCreatedDateTime());
        }

        // 手动分页：计算当前页的起始和结束位置
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), sortedTasks.size());

        List<ProjectTask> pageContent = start < sortedTasks.size() ? sortedTasks.subList(start, end)
                : new ArrayList<>();

        // 创建一个新的Page对象，包含分页后的内容
        return new PageImpl<>(pageContent, pageable, sortedTasks.size());
    }

    @Override
    public Optional<ProjectTask> findTaskById(Long id) {
        return taskRepository.findById(id);
    }

    @Override
    public List<ProjectTask> findTasksByProjectId(Long projectId) {
        return taskRepository.findByProjectIdOrderByCreatedDateDesc(projectId);
    }

    @Override
    public Page<ProjectTask> findTasksByProjectId(Long projectId, Pageable pageable) {
        return taskRepository.findByProjectIdOrderByCreatedDateDescPageable(projectId, pageable);
    }

    @Override
    public List<ProjectTask> findAllTasksByProjectId(Long projectId) {
        return taskRepository.findByProjectId(projectId);
    }

    @Override
    @Transactional
    public ProjectTask saveTask(ProjectTask task) {
        // 首先检查项目是否存在，如果不存在则直接抛出异常
        if (task.getProjectId() != null && task.getProjectId() > 0) {
            Optional<Project> projectOpt = projectRepository.findById(task.getProjectId());
            if (!projectOpt.isPresent()) {
                // String errorMsg = String.format("无法保存任务：关联的项目(ID=%d)不存在",
                // task.getProjectId());
                // logger.error(errorMsg);
                // throw new IllegalArgumentException(errorMsg);
            }
        }

        try {
            // 计算并更新累计工期和剩余工期
            calculateAndUpdateDurations(task);

            BigDecimal bonus = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
            double ratedVal = 0.0;
            double actualVal = 0.0;

            // 获取project对象，增加防错处理，避免懒加载异常
            String visionType = null;
            try {
                if (task.getProject() != null) {
                    visionType = task.getProject().getVisionType();
                }
            } catch (Exception e) {
                logger.warn("获取任务[{}]的Project信息失败，可能已被删除或懒加载异常: {}", task.getTaskId(), e.getMessage());
                visionType = null;
            }

            Double totalRatioForPrefix = 0.0; // 无奖金
            // 如果task.getTaskName()名称前两个字符不为数字，即非订单任务，则归类于有奖金任务

            if (task.getTaskName() != null && task.getTaskName().length() >= 2) {
                String prefix = task.getTaskName().substring(0, 2);
                if (!prefix.matches("\\d+")) { // 非数字前缀有奖金
                    totalRatioForPrefix = 1.0;
                } else { // 数字前缀，进一步查询配置是否有奖金
                    totalRatioForPrefix = optionsService.getTaskDurationRatio("任务名称", task.getTaskName(), visionType);
                }
            }

            if (totalRatioForPrefix != null && totalRatioForPrefix > 0) { // 有奖金任务
                // 计算绩效分（(额定工期*3-实际工期)*50）
                BigDecimal newRated = task.getRatedDurationDays();
                BigDecimal newCumulative = task.getCumulativeDurationDays();
                ratedVal = newRated != null ? newRated.doubleValue() : 0.0;
                actualVal = newCumulative != null ? newCumulative.doubleValue() : 0.0;
                bonus = BigDecimal.valueOf((ratedVal * 3 - actualVal) * 50.0)
                        .setScale(2, RoundingMode.HALF_UP);
            }

            task.setBonus(bonus);

            // 如果任务名称前两个字符以“11”或“12”开头，则获取最后的任务提交时间
            if (task.getTaskName() != null && task.getTaskName().length() >= 2) {
                String prefix = task.getTaskName().substring(0, 2);
                if ("11".equals(prefix) || "12".equals(prefix)) {

                    // 从Submit2Service获取该任务的所有提交记录
                    List<Submit2> submits = submit2Service.findAllSubmitsByTaskId(task.getTaskId());
                    // 找出包含"审批通过"的最后一次提交记录的时间
                    LocalDateTime lastSubmitTime = submits.stream()
                            .filter(submit -> submit.getRemarks() != null && submit.getRemarks().contains("审批通过"))
                            .map(Submit2::getSubmitDateTime)
                            .filter(time -> time != null)
                            .max(LocalDateTime::compareTo)
                            .orElse(task.getCreatedDateTime()); // 如果没有找到审批通过的记录，使用任务创建时间

                    // 更新最后评论时间
                    if (lastSubmitTime != null) {
                        task.setLastCommentDateTime(lastSubmitTime);
                    }
                }
            }

            logger.debug("已根据最新工期计算绩效分: 额定={} 实际={} 绩效分={}", ratedVal, actualVal, bonus);

            return taskRepository.save(task);
        } catch (Exception e) {
            logger.error("保存任务时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    // 判等工具：null 安全且忽略小数精度微小差异
    /**
     * 计算并更新任务的累计工期和剩余工期
     * 
     * @param task 任务对象
     */
    private void calculateAndUpdateDurations(ProjectTask task) {
        try {
            // 这里增加一个空值保护
            if (task == null) {
                // logger.warn("传入的任务对象为null，无法计算工期");
                return;
            }
            // 获取累计工期（从工时记录中）
            Double cumulativeDurationDouble = workHoursLogService.getLatestHoursInventory("任务",
                    task.getTaskId() != null ? task.getTaskId().intValue() : 0);
            BigDecimal cumulativeDuration = BigDecimal.ZERO;
            if (cumulativeDurationDouble != null && cumulativeDurationDouble > 0) {
                cumulativeDuration = BigDecimal.valueOf(cumulativeDurationDouble).setScale(2, RoundingMode.HALF_UP);
            }
            task.setCumulativeDurationDays(cumulativeDuration != null ? cumulativeDuration : BigDecimal.ZERO);
            // 计算剩余工期 = 额定工期 - 累计工期
            BigDecimal ratedDuration = task.getRatedDurationDays() != null ? task.getRatedDurationDays()
                    : BigDecimal.ZERO;
            BigDecimal safeCumulative = cumulativeDuration != null ? cumulativeDuration : BigDecimal.ZERO;
            BigDecimal remainingDuration = ratedDuration.subtract(safeCumulative);
            // 剩余工期允许为负数，不做限制
            task.setRemainingDurationDays(remainingDuration);

            logger.debug("任务 {} 工期计算完成: 额定工期={}, 累计工期={}, 剩余工期={}",
                    task.getTaskId(), task.getRatedDurationDays(), cumulativeDuration, remainingDuration);
        } catch (Exception e) {
            logger.error("计算任务 {} 的累计工期和剩余工期时出错: {}", 
                    task != null ? task.getTaskId() : "null", e.getMessage(), e);
            // 如果计算出错，设置为默认值
            if (task != null) {
                task.setCumulativeDurationDays(BigDecimal.ZERO);
                task.setRemainingDurationDays(
                        task.getRatedDurationDays() != null ? task.getRatedDurationDays() : BigDecimal.ZERO);
            }
        }
    }

    @Override
    @Transactional
    public void deleteTask(Long id) {
        logger.info("开始级联删除任务ID: {}", id);
        try {
            // 获取任务的所有子任务并删除
            List<SubTask> subTasks = subTaskService.findSubTasksByTaskId(id);
            logger.info("任务ID {} 关联的子任务数量: {}", id, subTasks.size());

            for (SubTask subTask : subTasks) {
                try {
                    subTaskService.deleteSubTask(subTask.getSubTaskId());
                    logger.info("成功删除任务ID {} 的子任务ID: {}", id, subTask.getSubTaskId());
                } catch (Exception e) {
                    logger.error("删除任务 {} 的子任务 {} 时出错: {}", id, subTask.getSubTaskId(), e.getMessage(), e);
                    // 继续删除其他子任务，不中断整个过程
                }
            }

            // 删除任务的所有提交记录
            try {
                // 查找该任务的所有提交
                List<Submit2> submits = submit2Service.findAllSubmitsByTaskId(id);
                logger.info("任务ID {} 关联的提交记录数量: {}", id, submits.size());

                // 逐个删除提交记录
                for (Submit2 submit : submits) {
                    try {
                        // 删除提交相关文件
                        if (submit.getFilePath1() != null && !submit.getFilePath1().isEmpty()) {
                            try {
                                java.nio.file.Path filePath = java.nio.file.Paths.get(submit.getFilePath1());
                                if (java.nio.file.Files.exists(filePath)) {
                                    java.nio.file.Files.delete(filePath);
                                    logger.info("删除提交文件1: {}", filePath);
                                }
                            } catch (Exception e) {
                                logger.error("删除提交文件1时出错: {}", e.getMessage());
                            }
                        }

                        if (submit.getFilePath2() != null && !submit.getFilePath2().isEmpty()) {
                            try {
                                java.nio.file.Path filePath = java.nio.file.Paths.get(submit.getFilePath2());
                                if (java.nio.file.Files.exists(filePath)) {
                                    java.nio.file.Files.delete(filePath);
                                    logger.info("删除提交文件2: {}", filePath);
                                }
                            } catch (Exception e) {
                                logger.error("删除提交文件2时出错: {}", e.getMessage());
                            }
                        }

                        // 删除提交记录
                        submit2Service.deleteSubmit(submit.getSubmitId());
                        logger.info("成功删除任务ID {} 的提交记录ID: {}", id, submit.getSubmitId());
                    } catch (Exception e) {
                        logger.error("删除任务 {} 的提交记录 {} 时出错: {}", id, submit.getSubmitId(), e.getMessage(), e);
                        // 继续删除其他提交记录，不中断整个过程
                    }
                }
            } catch (Exception e) {
                logger.error("删除任务 {} 的提交记录时出错: {}", id, e.getMessage(), e);
                // 继续执行删除任务操作
            }

            // 删除任务本身
            taskRepository.deleteById(id);
            logger.info("成功删除任务ID: {}", id);
        } catch (Exception e) {
            logger.error("删除任务ID {} 时出错: {}", id, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public ProjectTask completeTask(Long id) {
        Optional<ProjectTask> taskOpt = findTaskById(id);
        if (!taskOpt.isPresent()) {
            throw new IllegalArgumentException("任务不存在: " + id);
        }

        ProjectTask task = taskOpt.get();

        // 检查任务是否已经是完成状态
        if ("已完成".equals(task.getStatus())) {
            return task; // 如果已经是完成状态，直接返回
        }

        // 更新任务状态为"已完成"
        task.setStatus("已完成");

        // 获取任务的最后一条提交记录的提交时间作为实际结束时间
        LocalDateTime actualEndTime = null;
        try {
            // 从Submit2Service获取该任务的所有提交记录
            List<Submit2> submits = submit2Service.findAllSubmitsByTaskId(task.getTaskId());
            if (submits != null && !submits.isEmpty()) {
                // 找出最后一次提交记录的时间
                actualEndTime = submits.stream()
                        .map(Submit2::getSubmitDateTime)
                        .filter(time -> time != null)
                        .max(LocalDateTime::compareTo)
                        .orElse(null);

                if (actualEndTime != null) {
                    logger.info("任务 {} 使用最后提交时间作为实际结束时间: {}", task.getTaskId(), actualEndTime);
                } else {
                    logger.warn("任务 {} 没有有效的提交时间，使用当前时间", task.getTaskId());
                }
            }
        } catch (Exception e) {
            logger.error("获取任务 {} 的提交记录时出错: {}", task.getTaskId(), e.getMessage());
        }

        // 如果没有找到提交记录或提交时间为空，则使用当前时间
        if (actualEndTime == null) {
            actualEndTime = LocalDateTime.now();
            logger.info("任务 {} 使用当前时间作为实际结束时间: {}", task.getTaskId(), actualEndTime);
        }

        // 设置实际结束日期
        task.setActualEndDateTime(actualEndTime);

        // 设置进度为100%
        task.setProgress(100);

        // 计算工期
        if (task.getActualStartDateTime() != null) {
            long days = java.time.temporal.ChronoUnit.DAYS.between(task.getActualStartDateTime(), actualEndTime);
            long hours = java.time.temporal.ChronoUnit.HOURS.between(task.getActualStartDateTime(), actualEndTime) % 24;
            long minutes = java.time.temporal.ChronoUnit.MINUTES.between(task.getActualStartDateTime(), actualEndTime)
                    % 60;

            // 计算小数部分（小时和分钟转换为天的小数）
            double hoursFraction = hours / 24.0;
            double minutesFraction = minutes / (24.0 * 60);
            double totalDays = days + hoursFraction + minutesFraction;

            // 创建BigDecimal并保留两位小数
            BigDecimal durationDays = new BigDecimal(totalDays).setScale(2, RoundingMode.HALF_UP);

            // 确保工期至少为0
            task.setDurationDays(durationDays.max(BigDecimal.ZERO));
        }
        // 保存并返回更新后的任务
        ProjectTask updatedTask = saveTask(task);

        // 创建工时提交记录
        try {
            // 准备工时记录参数
            String businessType = "任务";
            Integer businessId = task.getTaskId().intValue();
            Double hoursChange = task.getDurationDays() != null ? task.getDurationDays().doubleValue() : 0.0;
            Double daysRated = task.getRatedDurationDays() != null ? task.getRatedDurationDays().doubleValue() : 0.0;
            String reason = String.format("任务【%s】完成，自动提交工时", task.getTaskName() != null ? task.getTaskName() : "未命名任务");
            String creator = task.getResponsible() != null ? task.getResponsible() : "system";
            String remark = String.format("任务完成",
                    task.getDurationDays() != null ? task.getDurationDays().doubleValue() : 0.0);

            String responsiblePerson = task.getResponsible() != null ? task.getResponsible() : "未知责任人";
            // 只有当工期大于0时才创建工时记录
            if (hoursChange > 0) {
                workHoursLogService.addWorkHours(businessType, businessId, hoursChange, daysRated, reason, creator,
                        remark, responsiblePerson);
                logger.info("任务完成，已自动创建工时记录：任务ID={}, 工期变化={}天", task.getTaskId(), hoursChange);
            } else {
                logger.debug("任务 {} 完成但工期为0或null，跳过创建工时记录", task.getTaskId());
            }
        } catch (Exception e) {
            logger.error("任务完成时创建工时记录失败：任务ID={}, 错误：{}", task.getTaskId(), e.getMessage(), e);
            // 不影响任务完成流程，只记录错误日志
        }
        return updatedTask;
    }

    @Override
    @Transactional
    public ProjectTask pauseTask(Long id) {
        return pauseTask(id, false);
    }

    @Override
    @Transactional
    public ProjectTask pauseTask(Long id, boolean isRestartTask) {
        Optional<ProjectTask> taskOpt = findTaskById(id);
        if (!taskOpt.isPresent()) {
            throw new IllegalArgumentException("任务不存在: " + id);
        }

        ProjectTask task = taskOpt.get();

        // 检查任务是否已经是暂停状态
        if ("已暂停".equals(task.getStatus())) {
            return task; // 如果已经是暂停状态，直接返回
        }

        // 更新任务状态为"已暂停"
        task.setStatus("已暂停");

        // 获取任务的最后一条提交记录的提交时间作为实际结束时间
        LocalDateTime actualEndTime = null;
        try {
            // 从Submit2Service获取该任务的所有提交记录
            List<Submit2> submits = submit2Service.findAllSubmitsByTaskId(task.getTaskId());
            if (submits != null && !submits.isEmpty()) {
                // 找出最后一次提交记录的时间
                actualEndTime = submits.stream()
                        .map(Submit2::getSubmitDateTime)
                        .filter(time -> time != null)
                        .max(LocalDateTime::compareTo)
                        .orElse(null);

                if (actualEndTime != null) {
                    logger.info("任务 {} 暂停时使用最后提交时间作为实际结束时间: {}", task.getTaskId(), actualEndTime);
                } else {
                    logger.warn("任务 {} 没有有效的提交时间，使用当前时间", task.getTaskId());
                }
            }
        } catch (Exception e) {
            logger.error("获取任务 {} 的提交记录时出错: {}", task.getTaskId(), e.getMessage());
        }

        // 如果没有找到提交记录或提交时间为空，则使用当前时间
        if (actualEndTime == null) {
            actualEndTime = LocalDateTime.now();
            logger.info("任务 {} 暂停时使用当前时间作为实际结束时间: {}", task.getTaskId(), actualEndTime);
        }

        // 设置实际结束日期
        task.setActualEndDateTime(actualEndTime);

        // 计算工期（如果有开始时间）
        if (task.getActualStartDateTime() != null) {
            long days = java.time.temporal.ChronoUnit.DAYS.between(task.getActualStartDateTime(), actualEndTime);
            long hours = java.time.temporal.ChronoUnit.HOURS.between(task.getActualStartDateTime(), actualEndTime) % 24;
            long minutes = java.time.temporal.ChronoUnit.MINUTES.between(task.getActualStartDateTime(), actualEndTime)
                    % 60;

            // 计算小数部分（小时和分钟转换为天的小数）
            double hoursFraction = hours / 24.0;
            double minutesFraction = minutes / (24.0 * 60);
            double totalDays = days + hoursFraction + minutesFraction;

            // 创建BigDecimal并保留两位小数
            BigDecimal durationDays = new BigDecimal(totalDays).setScale(2, RoundingMode.HALF_UP);

            // 确保工期至少为0
            task.setDurationDays(durationDays.max(BigDecimal.ZERO));
        }
        // 如果isRestart为true，执行重启相关的代码
        if (isRestartTask) {
            // 将状态改回"进行中"
            task.setStatus("进行中");

            // 更新实际开始时间为当前时间
            task.setActualStartDateTime(LocalDateTime.now());
        }

        // 创建工时提交记录
        try {
            // 准备工时记录参数
            String businessType = "任务";
            Integer businessId = task.getTaskId().intValue();
            Double hoursChange = task.getDurationDays() != null ? task.getDurationDays().doubleValue() : 0.0;
            Double daysRated = task.getRatedDurationDays() != null ? task.getRatedDurationDays().doubleValue() : 0.0;
            String reason = String.format("任务【%s】暂停,", task.getTaskName() != null ? task.getTaskName() : "未命名任务");

            if (isRestartTask) {
                reason = String.format("任务【%s】暂停,批量提交工时", task.getTaskName() != null ? task.getTaskName() : "未命名任务");
            }

            String creator = SecurityUtils.getCurrentUsername();
            if (creator == null || creator.isEmpty()) {
                creator = "系统";
            }
            String remark = String.format("任务暂停",
                    task.getDurationDays() != null ? task.getDurationDays().doubleValue() : 0.0);

            String responsiblePerson = task.getResponsible() != null ? task.getResponsible() : "未知责任人";
            // 只有当工期大于0时才创建工时记录
            if (hoursChange > 0) {
                workHoursLogService.addWorkHours(businessType, businessId, hoursChange, daysRated, reason, creator,
                        remark, responsiblePerson);
                logger.info("任务暂停，已自动创建工时记录：任务ID={}, 工期变化={}天", task.getTaskId(), hoursChange);
            } else {
                logger.debug("任务 {} 暂停但工期为0或null，跳过创建工时记录", task.getTaskId());
            }

        } catch (Exception e) {
            logger.error("任务暂停时创建工时记录失败：任务ID={}, 错误：{}", task.getTaskId(), e.getMessage(), e);
            // 不影响任务暂停流程，只记录错误日志
        }

        // 保存并返回更新后的任务（只保存一次）
        ProjectTask updatedTask = saveTask(task);

        return updatedTask;
    }

    @Override
    public List<ProjectTask> findTasksByResponsible(String responsible) {
        return taskRepository.findByResponsible(responsible);
    }

    @Override
    public Page<ProjectTask> findTasksByResponsible(String responsible, Pageable pageable) {
        // 归档状态值1表示已归档，使用新方法排除已归档项目中的任务
        return taskRepository.findByResponsibleFromNonArchivedProjectsOrderByStatusAndDate(responsible, 1, pageable);
    }

    @Override
    public Long countTasksByResponsibleAndStatus(String responsible, String status) {
        return taskRepository.countByResponsibleAndStatus(responsible, status);
    }

    @Override
    public Long countTasksByResponsibleAndCreatedDateAfter(String responsible, String createdAfter) {
        try {
            // 解析日期字符串
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime createdAfterDateTime = LocalDateTime.parse(createdAfter, formatter);

            // 获取该负责人的所有任务
            List<ProjectTask> tasks = taskRepository.findByResponsible(responsible);

            // 筛选创建日期在指定时间之后的任务，并排除类型为"分管"和培训的任务
            long count = tasks.stream()
                    .filter(task -> {
                        LocalDateTime taskCreatedDateTime = task.getCreatedDateTime();
                        String taskType = task.getType();
                        return taskCreatedDateTime != null &&
                                taskCreatedDateTime.isAfter(createdAfterDateTime) &&
                                (!"分管".equals(taskType)) &&
                                (!"培训".equals(taskType));
                    })
                    .count();
            logger.debug("统计负责人 {} 在 {} 之后创建的任务数（不含分管和培训）: {}", responsible, createdAfter, count);
            return count;
        } catch (Exception e) {
            logger.error("统计负责人 {} 半年内新建任务数时出错: {}", responsible, e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public Long countCompletedTasksByResponsibleAndEndDateAfter(String responsible, String completedAfter) {
        try {
            // 解析日期字符串
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime completedAfterDateTime = LocalDateTime.parse(completedAfter, formatter);

            // 获取该负责人的所有任务
            List<ProjectTask> tasks = taskRepository.findByResponsible(responsible);

            // 筛选已完成且完成日期在指定时间之后的任务，并排除类型为"分管"和培训的任务
            long count = tasks.stream()
                    .filter(task -> {
                        // 任务必须是已完成状态
                        if (!"已完成".equals(task.getStatus())) {
                            return false;
                        }

                        // 获取任务的实际结束时间
                        LocalDateTime taskEndDateTime = task.getActualEndDateTime();
                        String taskType = task.getType();

                        // 结束时间必须存在且在指定时间之后，且不是分管类型
                        return taskEndDateTime != null &&
                                taskEndDateTime.isAfter(completedAfterDateTime) &&
                                (!"分管".equals(taskType)) &&
                                (!"培训".equals(taskType));
                    })
                    .count();
            logger.debug("统计负责人 {} 在 {} 之后完成的任务数（不含分管和培训）: {}", responsible, completedAfter, count);
            return count;
        } catch (Exception e) {
            logger.error("统计负责人 {} 半年内完成任务数时出错: {}", responsible, e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public List<ProjectTask> findTasksByStatus(String status) {
        return taskRepository.findByStatus(status);
    }

    @Override
    public Page<ProjectTask> findTasksByStatus(String status, Pageable pageable) {
        return taskRepository.findByStatus(status, pageable);
    }

    @Override
    public List<ProjectTask> findTasksByRisk(String risk) {
        return taskRepository.findByRisk(risk);
    }

    @Override
    public Page<ProjectTask> findTasksByRisk(String risk, Pageable pageable) {
        return taskRepository.findByRisk(risk, pageable);
    }

    @Override
    public List<ProjectTask> searchTasks(String keyword) {
        return taskRepository.findByKeyword(keyword);
    }

    @Override
    public Page<ProjectTask> searchTasks(String keyword, Pageable pageable) {
        // 获取原始查询结果
        Page<ProjectTask> originalPage = taskRepository.findByKeywordPageable(keyword, pageable);

        // 手动对结果进行排序：进行中状态的排在前面，然后按创建时间降序
        List<ProjectTask> sortedTasks = originalPage.getContent().stream()
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());

        // 创建一个新的Page对象，包含排序后的内容
        return new PageImpl<>(sortedTasks, pageable, originalPage.getTotalElements());
    }

    @Override
    public List<ProjectTask> findTasksByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return taskRepository.findByCreatedDateBetween(startDate, endDate);
    }

    @Override
    public Page<ProjectTask> findTasksByDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        return taskRepository.findByCreatedDateBetweenPageable(startDate, endDate, pageable);
    }

    @Override
    public Long countTasksByAssignee(String assignee) {
        return taskRepository.countByResponsible(assignee);
    }

    @Override
    public Long countTasksByStatus(String status) {
        return taskRepository.countByStatus(status);
    }

    @Override
    public List<ProjectTask> dynamicSearchTasks(Map<String, Object> searchCriteria) { // 输出搜索条件信息
        logger.info("开始动态搜索任务，搜索条件数量: {}", searchCriteria.size());
        for (Map.Entry<String, Object> entry : searchCriteria.entrySet()) {
            logger.info("搜索条件: {} = {}", entry.getKey(), entry.getValue());
        }

        // 特别检查评论天数条件
        boolean hasCommentDaysFilter = false;
        if (searchCriteria.containsKey("commentDays_min")) {
            logger.info("包含评论天数最小值条件: {}", searchCriteria.get("commentDays_min"));
            hasCommentDaysFilter = true;
        }
        if (searchCriteria.containsKey("commentDays_max")) {
            logger.info("包含评论天数最大值条件: {}", searchCriteria.get("commentDays_max"));
            hasCommentDaysFilter = true;
        }

        if (hasCommentDaysFilter) {
            logger.info("检测到评论天数筛选条件，将进行评论天数计算和筛选");
        }

        // 获取用户负责的所有任务
        List<ProjectTask> userTasks = new ArrayList<>();
        if (searchCriteria.containsKey("responsible")) {
            String responsible = (String) searchCriteria.get("responsible");
            userTasks = taskRepository.findByResponsible(responsible);
            logger.info("根据负责人 {} 获取到 {} 个任务", responsible, userTasks.size());
        } else {
            userTasks = taskRepository.findAll();
            logger.info("获取所有任务，共 {} 个", userTasks.size());
        }

        // 如果搜索条件包含客户名称或项目名称，需要预先安全加载项目信息
        boolean needsProjectInfo = searchCriteria.containsKey("customerName") ||
                searchCriteria.containsKey("projectName") ||
                searchCriteria.containsKey("visionType");

        if (needsProjectInfo) {
            // 安全地预加载项目信息
            for (ProjectTask task : userTasks) {
                if (task.getProjectId() != null) {
                    try {
                        // 如果项目是懒加载的，先获取项目ID
                        Long projectId = task.getProjectId();

                        // 如果当前项目对象为null或者是懒加载代理，尝试从数据库手动加载项目
                        if (task.getProject() == null) {
                            logger.debug("项目为null，尝试根据ID加载项目: {}", projectId);
                            try {
                                Optional<Project> projectOpt = projectRepository.findById(projectId);
                                projectOpt.ifPresent(task::setProject);
                            } catch (Exception e) {
                                logger.error("尝试加载项目时出错: {}", e.getMessage());
                                // 如果加载失败，设置为null防止后续操作时出错
                                task.setProject(null);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("处理项目ID时出错: {}", e.getMessage());
                        task.setProject(null);
                    }
                }
            }
        } // 如果有评论天数相关条件，先计算所有任务的评论天数
        if (hasCommentDaysFilter) {
            logger.info("计算所有任务的评论天数，以便进行评论天数筛选");
            calculateCommentDays(userTasks);

            // 输出一些任务的评论天数，便于调试
            if (!userTasks.isEmpty()) {
                logger.info("评论天数筛选前，随机抽样任务评论天数：");
                int sampleSize = Math.min(10, userTasks.size());
                for (int i = 0; i < sampleSize; i++) {
                    ProjectTask task = userTasks.get(i);
                    logger.info("任务#{}: ID={}, 名称={}, 评论天数={}",
                            i + 1, task.getTaskId(), task.getTaskName(), task.getCommentDays());
                }
            }
        }

        // 直接使用matchesAllCriteria方法进行统一过滤，包括剩余工期条件
        List<ProjectTask> filteredTasks = userTasks.stream()
                .filter(task -> matchesAllCriteria(task, searchCriteria))
                .collect(Collectors.toList());

        return filteredTasks;
    }

    @Override
    public Page<ProjectTask> dynamicSearchTasks(Map<String, Object> searchCriteria, Pageable pageable) {
        // 检查是否包含评论天数相关条件
        boolean hasCommentDaysFilter = searchCriteria.containsKey("commentDays_min") ||
                searchCriteria.containsKey("commentDays_max");

        if (hasCommentDaysFilter) {
            logger.info("检测到评论天数搜索条件，将确保所有任务的评论天数已计算");
        }

        // 执行搜索获取匹配任务
        List<ProjectTask> filteredTasks = dynamicSearchTasks(searchCriteria);

        if (hasCommentDaysFilter) {
            logger.info("评论天数筛选后，共找到 {} 个匹配任务", filteredTasks.size());
            if (filteredTasks.size() > 0) {
                // 记录几个样本任务的评论天数，便于调试
                int sampleSize = Math.min(5, filteredTasks.size());
                for (int i = 0; i < sampleSize; i++) {
                    ProjectTask task = filteredTasks.get(i);
                    logger.info("样本任务 #{}: ID={}, 名称={}, 评论天数={}",
                            i + 1, task.getTaskId(), task.getTaskName(), task.getCommentDays());
                }
            }
        }

        // 先排序
        filteredTasks.sort((t1, t2) -> {
            // 首先比较状态：进行中排在最前
            boolean t1InProgress = "进行中".equals(t1.getStatus());
            boolean t2InProgress = "进行中".equals(t2.getStatus());

            if (t1InProgress && !t2InProgress) {
                return -1; // t1排在前面
            } else if (!t1InProgress && t2InProgress) {
                return 1; // t2排在前面
            }

            // 状态相同时，按创建日期倒序排序（最近的排在前面）
            if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                return 0;
            }
            return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
        });

        // 如果pageable为null，返回全部数据（不分页）
        if (pageable == null) {
            return new PageImpl<>(filteredTasks);
        }

        // 排序后再计算分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), filteredTasks.size());

        // 确保start不超过列表大小
        if (start >= filteredTasks.size()) {
            // 如果起始位置超过了列表大小，返回空页
            return new PageImpl<>(
                    new ArrayList<>(),
                    pageable,
                    filteredTasks.size());
        }

        // 创建分页结果
        return new PageImpl<>(
                filteredTasks.subList(start, end),
                pageable,
                filteredTasks.size());
    }

    /**
     * 检查任务是否匹配所有搜索条件
     * 
     * @param task           任务
     * @param searchCriteria 搜索条件
     * @return 是否匹配
     */
    private boolean matchesAllCriteria(ProjectTask task, Map<String, Object> searchCriteria) {
        for (Map.Entry<String, Object> entry : searchCriteria.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 跳过空值
            if (value == null) {
                continue;
            }

            switch (key) {
                case "taskName":
                    if (!containsIgnoreCase(task.getTaskName(), value.toString())) {
                        return false;
                    }
                    break;
                case "status":
                    if (!equalsIgnoreCase(task.getStatus(), value.toString())) {
                        return false;
                    }
                    break;
                case "risk":
                    if (!equalsIgnoreCase(task.getRisk(), value.toString())) {
                        return false;
                    }
                    break;
                case "type":
                    if (!equalsIgnoreCase(task.getType(), value.toString())) {
                        return false;
                    }
                    break;
                case "projectName":
                    // 需先加载项目
                    if (task.getProject() == null) {
                        logger.debug("任务 {} 的项目为null，projectName搜索不匹配", task.getTaskId());
                        return false;
                    }

                    try {
                        String projectName = task.getProject().getProjectName();
                        if (projectName == null || !containsIgnoreCase(projectName, value.toString())) {
                            logger.debug("任务 {} 的项目名称 '{}' 与搜索值 '{}' 不匹配",
                                    task.getTaskId(), projectName, value.toString());
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("获取任务 {} 的项目名称时出错: {}", task.getTaskId(), e.getMessage());
                        return false;
                    }
                    break;
                case "customerName":
                    // 需先加载项目
                    if (task.getProject() == null) {
                        logger.debug("任务 {} 的项目为null，customerName搜索不匹配", task.getTaskId());
                        return false;
                    }

                    // 安全地获取客户名称，防止懒加载异常
                    try {
                        String customerName = task.getProject().getCustomerName();
                        if (customerName == null || !containsIgnoreCase(customerName, value.toString())) {
                            logger.debug("任务 {} 的客户名称 '{}' 与搜索值 '{}' 不匹配",
                                    task.getTaskId(), customerName, value.toString());
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("获取任务 {} 的客户名称时出错: {}", task.getTaskId(), e.getMessage());
                        return false;
                    }
                    break;
                case "visionType":
                    // 需先加载项目
                    if (task.getProject() == null) {
                        logger.debug("任务 {} 的项目为null，visionType搜索不匹配", task.getTaskId());
                        return false;
                    }

                    // 安全地获取视觉类型，防止懒加载异常
                    try {
                        String visionType = task.getProject().getVisionType();
                        if (visionType == null) {
                            logger.debug("任务 {} 的视觉类型为null，不匹配", task.getTaskId());
                            return false;
                        }

                        // 检查visionType全文是否包含搜索值（忽略大小写）
                        if (!containsIgnoreCase(visionType, value.toString())) {
                            logger.debug("任务 {} 的视觉类型 '{}' 不包含搜索值 '{}'",
                                    task.getTaskId(), visionType, value.toString());
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("获取任务 {} 的视觉类型时出错: {}", task.getTaskId(), e.getMessage());
                        return false;
                    }
                    break;
                case "responsible":
                    if (!equalsIgnoreCase(task.getResponsible(), value.toString())) {
                        return false;
                    }
                    break;
                case "progress_min":
                    if (task.getProgress() == null || task.getProgress() < (Integer) value) {
                        return false;
                    }
                    break;
                case "progress_max":
                    if (task.getProgress() == null || task.getProgress() > (Integer) value) {
                        return false;
                    }
                    break;
                case "commentDays_min":
                    double minValue = ((Number) value).doubleValue();
                    // 判断条件解释：
                    // 1. 如果评论天数为null，则不满足条件（返回false）
                    // 2. 如果评论天数小于设定的最小值，则不满足条件（返回false）
                    if (task.getCommentDays() == null || task.getCommentDays() < minValue) {
                        logger.debug("任务 [{}] 评论天数 {} 小于最小值 {}, 不匹配", task.getTaskId(), task.getCommentDays(),
                                minValue);
                        return false;
                    }
                    logger.debug("任务 [{}] 评论天数 {} 大于等于最小值 {}, 匹配成功", task.getTaskId(), task.getCommentDays(), minValue);
                    break;
                case "commentDays_max":
                    double maxValue = ((Number) value).doubleValue();
                    // 判断条件解释：
                    // 1. 如果评论天数为null，则不满足条件（返回false）
                    // 2. 如果评论天数大于设定的最大值，则不满足条件（返回false）
                    if (task.getCommentDays() == null || task.getCommentDays() > maxValue) {
                        logger.debug("任务 [{}] 评论天数 {} 大于最大值 {}, 不匹配", task.getTaskId(), task.getCommentDays(),
                                maxValue);
                        return false;
                    }
                    logger.debug("任务 [{}] 评论天数 {} 小于等于最大值 {}, 匹配成功", task.getTaskId(), task.getCommentDays(), maxValue);
                    break;
                case "createdDate_start":
                    if (task.getCreatedDateTime() == null) {
                        return false;
                    }
                    try {
                        LocalDateTime startDateTime;
                        String dateStr = value.toString();
                        if (dateStr.contains(" ")) {
                            // 如果包含时间部分，使用日期时间格式化器
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            startDateTime = LocalDateTime.parse(dateStr, formatter);
                        } else {
                            // 如果只有日期部分，添加时间部分
                            startDateTime = LocalDateTime.parse(dateStr + "T00:00:00");
                        }
                        if (task.getCreatedDateTime().isBefore(startDateTime)) {
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("解析开始日期失败: {} - {}", value, e.getMessage());
                        return false;
                    }
                    break;
                case "createdDate_end":
                    if (task.getCreatedDateTime() == null) {
                        return false;
                    }
                    try {
                        LocalDateTime endDateTime;
                        String dateStr = value.toString();
                        if (dateStr.contains(" ")) {
                            // 如果包含时间部分，使用日期时间格式化器
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            endDateTime = LocalDateTime.parse(dateStr, formatter);
                        } else {
                            // 如果只有日期部分，添加带有结束时间的时间部分
                            endDateTime = LocalDateTime.parse(dateStr + "T23:59:59");
                        }
                        if (task.getCreatedDateTime().isAfter(endDateTime)) {
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("解析结束日期失败: {} - {}", value, e.getMessage());
                        return false;
                    }
                    break;
                case "taskStage_min":
                    // 如果任务名称为空，不匹配
                    if (task.getTaskName() == null || task.getTaskName().length() < 2) {
                        return false;
                    }

                    try {
                        // 获取任务名称前两个字符
                        String stageStr = task.getTaskName().substring(0, 2);
                        // 尝试将前两个字符转换为数字
                        int stage;
                        try {
                            stage = Integer.parseInt(stageStr);
                        } catch (NumberFormatException e) {
                            // 如果不是数字，不匹配
                            return false;
                        }

                        // 检查是否大于等于最小值
                        int minStage = ((Number) value).intValue();
                        if (stage < minStage) {
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("处理任务阶段最小值失败: {} - {}", value, e.getMessage());
                        return false;
                    }
                    break;
                case "taskStage_max":
                    // 如果任务名称为空，不匹配
                    if (task.getTaskName() == null || task.getTaskName().length() < 2) {
                        return false;
                    }

                    try {
                        // 获取任务名称前两个字符
                        String stageStr = task.getTaskName().substring(0, 2);
                        // 尝试将前两个字符转换为数字
                        int stage;
                        try {
                            stage = Integer.parseInt(stageStr);
                        } catch (NumberFormatException e) {
                            // 如果不是数字，不匹配
                            return false;
                        }

                        // 检查是否小于最大值
                        int maxStage = ((Number) value).intValue();
                        if (stage > maxStage) {
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("处理任务阶段最大值失败: {} - {}", value, e.getMessage());
                        return false;
                    }
                    break;
                case "ratedDurationDays_min":
                    // 检查额定工期最小值，null值当作0处理
                    BigDecimal currentDuration = task.getRatedDurationDays();
                    if (currentDuration == null) {
                        currentDuration = BigDecimal.ZERO;
                        logger.debug("任务 [{}] 额定工期为null，当作0处理", task.getTaskId());
                    }

                    try {
                        BigDecimal minDuration = new BigDecimal(value.toString());
                        if (currentDuration.compareTo(minDuration) < 0) {
                            logger.debug("任务 [{}] 额定工期 {} 小于最小值 {}, 不匹配",
                                    task.getTaskId(), currentDuration, minDuration);
                            return false;
                        }
                        logger.debug("任务 [{}] 额定工期 {} 大于等于最小值 {}, 匹配成功",
                                task.getTaskId(), currentDuration, minDuration);
                    } catch (Exception e) {
                        logger.error("处理额定工期最小值失败: {} - {}", value, e.getMessage());
                        return false;
                    }
                    break;
                case "ratedDurationDays_max":
                    // 检查额定工期最大值，null值当作0处理
                    BigDecimal currentMaxDuration = task.getRatedDurationDays();
                    if (currentMaxDuration == null) {
                        currentMaxDuration = BigDecimal.ZERO;
                        logger.debug("任务 [{}] 额定工期为null，当作0处理", task.getTaskId());
                    }

                    try {
                        BigDecimal maxDuration = new BigDecimal(value.toString());
                        if (currentMaxDuration.compareTo(maxDuration) > 0) {
                            logger.info("任务 [{}] 额定工期 {} 大于最大值 {}, 不匹配",
                                    task.getTaskId(), currentMaxDuration, maxDuration);
                            return false;
                        }
                        logger.info("任务 [{}] 额定工期 {} 小于等于最大值 {}, 匹配成功",
                                task.getTaskId(), currentMaxDuration, maxDuration);
                    } catch (Exception e) {
                        logger.error("处理额定工期最大值失败: {} - {}", value, e.getMessage());
                        return false;
                    }
                    break;
                case "actualEndDate_start":
                    if (task.getActualEndDateTime() == null) {
                        return false;
                    }
                    try {
                        LocalDateTime startDateTime;
                        String dateStr = value.toString();
                        if (dateStr.contains(" ")) {
                            // 如果包含时间部分，使用日期时间格式化器
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            startDateTime = LocalDateTime.parse(dateStr, formatter);
                        } else {
                            // 如果只有日期部分，添加时间部分
                            startDateTime = LocalDateTime.parse(dateStr + "T00:00:00");
                        }
                        if (task.getActualEndDateTime().isBefore(startDateTime)) {
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("解析实际结束开始日期失败: {} - {}", value, e.getMessage());
                        return false;
                    }
                    break;
                case "actualEndDate_end":
                    if (task.getActualEndDateTime() == null) {
                        return false;
                    }
                    try {
                        LocalDateTime endDateTime;
                        String dateStr = value.toString();
                        if (dateStr.contains(" ")) {
                            // 如果包含时间部分，使用日期时间格式化器
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            endDateTime = LocalDateTime.parse(dateStr, formatter);
                        } else {
                            // 如果只有日期部分，添加带有结束时间的时间部分
                            endDateTime = LocalDateTime.parse(dateStr + "T23:59:59");
                        }
                        if (task.getActualEndDateTime().isAfter(endDateTime)) {
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("解析实际结束结束日期失败: {} - {}", value, e.getMessage());
                        return false;
                    }
                    break;
                case "remainingDurationDays_min":
                    // 检查剩余工期最小值，直接从数据库字段读取
                    BigDecimal remainingDuration = task.getRemainingDurationDays() != null
                            ? task.getRemainingDurationDays()
                            : BigDecimal.ZERO;

                    try {
                        BigDecimal minRemainingDuration = new BigDecimal(value.toString());
                        if (remainingDuration.compareTo(minRemainingDuration) < 0) {
                            logger.debug("任务 [{}] 剩余工期 {} 小于最小值 {}, 不匹配",
                                    task.getTaskId(), remainingDuration, minRemainingDuration);
                            return false;
                        }
                        logger.debug("任务 [{}] 剩余工期 {} 大于等于最小值 {}, 匹配成功",
                                task.getTaskId(), remainingDuration, minRemainingDuration);
                    } catch (Exception e) {
                        logger.error("处理剩余工期最小值失败: {} - {}", value, e.getMessage());
                        return false;
                    }
                    break;
                case "remainingDurationDays_max":
                    // 检查剩余工期最大值，直接从数据库字段读取
                    BigDecimal remainingDurationMax = task.getRemainingDurationDays() != null
                            ? task.getRemainingDurationDays()
                            : BigDecimal.ZERO;

                    try {
                        BigDecimal maxRemainingDuration = new BigDecimal(value.toString());
                        if (remainingDurationMax.compareTo(maxRemainingDuration) > 0) {
                            logger.debug("任务 [{}] 剩余工期 {} 大于最大值 {}, 不匹配",
                                    task.getTaskId(), remainingDurationMax, maxRemainingDuration);
                            return false;
                        }
                        logger.debug("任务 [{}] 剩余工期 {} 小于等于最大值 {}, 匹配成功",
                                task.getTaskId(), remainingDurationMax, maxRemainingDuration);
                    } catch (Exception e) {
                        logger.error("处理剩余工期最大值失败: {} - {}", value, e.getMessage());
                        return false;
                    }
                    break;
            }
        }
        return true;
    }

    /**
     * 忽略大小写的字符串包含检查
     */
    private boolean containsIgnoreCase(String text, String searchText) {
        if (text == null || searchText == null) {
            return false;
        }
        return text.toLowerCase().contains(searchText.toLowerCase());
    }

    /**
     * 忽略大小写的字符串相等检查
     */
    private boolean equalsIgnoreCase(String text, String searchText) {
        if (text == null || searchText == null) {
            return false;
        }
        return text.equalsIgnoreCase(searchText);
    }

    @Override
    public Page<ProjectTask> findTasksFromNonArchivedProjects(Pageable pageable) {
        // 归档状态值1表示已归档
        Page<ProjectTask> originalPage = taskRepository.findTasksFromNonArchivedProjects(1, pageable);

        // 手动对结果进行排序：进行中状态的排在前面，然后按创建时间降序
        List<ProjectTask> sortedTasks = originalPage.getContent().stream()
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());

        // 创建一个新的Page对象，包含排序后的内容
        return new PageImpl<>(sortedTasks, pageable, originalPage.getTotalElements());
    }

    @Override
    public Page<ProjectTask> findSpecialTasksByResponsible(String responsible, Pageable pageable) {
        // 归档状态值1表示已归档
        Page<ProjectTask> originalPage = taskRepository.findSpecialTasksByResponsible(responsible, 1, pageable);

        // 手动对结果进行排序：进行中状态的排在前面，然后按创建时间降序
        List<ProjectTask> sortedTasks = originalPage.getContent().stream()
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());

        // 创建一个新的Page对象，包含排序后的内容
        return new PageImpl<>(sortedTasks, pageable, originalPage.getTotalElements());
    }

    @Override
    public Page<ProjectTask> findDifficultTasksByResponsible(String responsible, Pageable pageable) {
        // 归档状态值1表示已归档
        Page<ProjectTask> originalPage = taskRepository.findDifficultTasksByResponsible(responsible, 1, pageable);

        // 手动对结果进行排序：进行中状态的排在前面，然后按创建时间降序
        List<ProjectTask> sortedTasks = originalPage.getContent().stream()
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());

        // 创建一个新的Page对象，包含排序后的内容
        return new PageImpl<>(sortedTasks, pageable, originalPage.getTotalElements());
    }

    @Override
    public Long countNonArchivedInProgressTasks() {
        // 归档状态值1表示已归档
        Page<ProjectTask> taskPage = taskRepository.findTasksFromNonArchivedProjectsByStatus(1, "进行中",
                PageRequest.of(0, 1));
        return taskPage.getTotalElements();
    }

    @Override
    public List<ProjectTask> findRecentTasksFromNonArchivedProjects(int limit) {
        // 归档状态值1表示已归档
        Page<ProjectTask> taskPage = taskRepository.findTasksFromNonArchivedProjects(1, PageRequest.of(0, limit));
        return taskPage.getContent();
    }

    @Override
    public Long countInProgressDifficultTasksByResponsible(String responsible) {
        // 归档状态值1表示已归档
        return taskRepository.countDifficultTasksByResponsibleAndStatus(responsible, "进行中", 1);
    }

    @Override
    public Long countInProgressSpecialTasksByResponsible(String responsible) {
        // 归档状态值1表示已归档
        return taskRepository.countSpecialTasksByResponsibleAndStatus(responsible, "进行中", 1);
    }

    @Override
    public boolean hasUncompletedTasks(Long projectId) {
        // 查询项目下所有非"已完成"状态的任务数量
        // 任务状态不等于"已完成"的任务被视为未完成任务
        Long count = taskRepository.countByProjectIdAndStatusNot(projectId, "已完成");
        return count > 0;
    }

    /**
     * 非级联删除任务
     * 只删除任务本身，不删除关联的子任务和提交记录
     * 
     * @param id 任务ID
     */
    @Transactional
    public void deleteTaskNonCascade(Long id) {
        logger.info("开始非级联删除任务ID: {}", id);
        try {
            // 直接删除任务本身
            taskRepository.deleteById(id);
            logger.info("成功删除任务ID: {}", id);
        } catch (Exception e) {
            logger.error("删除任务ID {} 时出错: {}", id, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public void deleteAllTasks() {
        logger.info("开始批量删除所有任务");
        try {
            // 获取所有任务
            List<ProjectTask> allTasks = taskRepository.findAll();
            logger.info("总共需要删除 {} 个任务", allTasks.size());

            int successCount = 0;
            int failCount = 0;

            for (ProjectTask task : allTasks) {
                try {
                    // 使用非级联删除的方法删除每个任务
                    deleteTaskNonCascade(task.getTaskId());
                    successCount++;
                } catch (Exception e) {
                    logger.error("批量删除时，删除任务ID {} 失败: {}", task.getTaskId(), e.getMessage());
                    failCount++;
                    // 继续处理其他任务，不中断整个过程
                }
            }

            logger.info("批量删除任务完成，成功: {}，失败: {}", successCount, failCount);

            try {
                taskRepository.resetAutoIncrement();
                taskRepository.resetSequenceToZero();
                logger.info("成功重置任务表的自增ID");
            } catch (Exception e) {
                logger.error("重置任务ID自增计数器失败: {}", e.getMessage(), e);
                throw new RuntimeException("重置任务ID自增计数器失败: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            logger.error("批量删除任务过程中出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<ProjectTask> saveAllTasks(List<ProjectTask> tasks) {
        return taskRepository.saveAll(tasks);
    }

    @Override
    public List<ProjectTask> findMyDelegatedTasks(String currentUsername) {
        // 1. 获取所有任务
        List<ProjectTask> allTasks = taskRepository.findAll();

        // 2. 按项目ID分组
        Map<Long, List<ProjectTask>> tasksByProject = allTasks.stream()
                .collect(Collectors.groupingBy(ProjectTask::getProjectId));

        // 3. 筛选符合条件的任务
        List<ProjectTask> result = new ArrayList<>();

        for (Map.Entry<Long, List<ProjectTask>> entry : tasksByProject.entrySet()) {
            List<ProjectTask> projectTasks = entry.getValue();

            // 查找该项目下是否有当前用户负责的"分管"类型任务
            Optional<ProjectTask> delegatedTask = projectTasks.stream()
                    .filter(task -> "分管".equals(task.getType()) &&
                            currentUsername.equals(task.getResponsible()))
                    .findFirst();

            // 如果找到了分管任务
            if (delegatedTask.isPresent()) {
                // 添加分管任务本身
                result.add(delegatedTask.get());

                // 添加该项目下所有包含"客户现场调试"的任务
                List<ProjectTask> fieldTasks = projectTasks.stream()
                        .filter(task -> task.getTaskName() != null &&
                                task.getTaskName().contains("客户现场调试"))
                        .collect(Collectors.toList());

                result.addAll(fieldTasks);
            }
        }

        // 按状态和创建时间排序
        result.sort((t1, t2) -> {
            // 首先比较状态：进行中排在最前
            boolean t1InProgress = "进行中".equals(t1.getStatus());
            boolean t2InProgress = "进行中".equals(t2.getStatus());

            if (t1InProgress && !t2InProgress) {
                return -1; // t1排在前面
            } else if (!t1InProgress && t2InProgress) {
                return 1; // t2排在前面
            }

            // 状态相同时，按创建日期倒序排序（最近的排在前面）
            if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                return 0;
            }
            return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
        });

        return result;
    }

    @Override
    public Page<ProjectTask> findMyDelegatedTasks(String currentUsername, Pageable pageable) {
        List<ProjectTask> delegatedTasks = findMyDelegatedTasks(currentUsername);

        // 手动实现分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), delegatedTasks.size());

        return new PageImpl<>(
                delegatedTasks.subList(start, end),
                pageable,
                delegatedTasks.size());
    }

    @Override
    public Long countInProgressDelegatedTasks(String currentUsername) {
        return findMyDelegatedTasks(currentUsername).stream()
                .filter(task -> "进行中".equals(task.getStatus()))
                .count();
    }

    @Override
    public Page<ProjectTask> findMyTrainingTasks(String currentUsername, Pageable pageable) {
        // 获取所有任务
        List<ProjectTask> allTasks = taskRepository.findAll();

        // 筛选出类型为"培训"且负责人为当前用户的任务
        List<ProjectTask> trainingTasks = allTasks.stream()
                .filter(task -> "培训".equals(task.getType()) &&
                        currentUsername.equals(task.getResponsible()))
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());

        // 手动实现分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), trainingTasks.size());

        return new PageImpl<>(
                trainingTasks.subList(start, end),
                pageable,
                trainingTasks.size());
    }

    @Override
    public Long countInProgressTrainingTasks(String currentUsername) {
        // 获取所有任务
        List<ProjectTask> allTasks = taskRepository.findAll();

        // 统计类型为"培训"、状态为"进行中"且负责人为当前用户的任务数量
        return allTasks.stream()
                .filter(task -> "培训".equals(task.getType()) &&
                        "进行中".equals(task.getStatus()) &&
                        currentUsername.equals(task.getResponsible()))
                .count();
    }

    @Override
    public Page<ProjectTask> findMyOrderTasks(String currentUsername, Pageable pageable) {
        // 获取所有任务
        List<ProjectTask> allTasks = taskRepository.findAll();

        // 筛选出类型为"订单"且负责人为当前用户的任务
        List<ProjectTask> orderTasks = allTasks.stream()
                .filter(task -> "订单".equals(task.getType()) &&
                        currentUsername.equals(task.getResponsible()))
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());

        // 手动实现分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), orderTasks.size());

        return new PageImpl<>(
                orderTasks.subList(start, end),
                pageable,
                orderTasks.size());
    }

    @Override
    public Long countInProgressOrderTasks(String currentUsername) {
        // 获取所有任务
        List<ProjectTask> allTasks = taskRepository.findAll();

        // 统计类型为"订单"、状态为"进行中"且负责人为当前用户的任务数量
        return allTasks.stream()
                .filter(task -> "订单".equals(task.getType()) &&
                        "进行中".equals(task.getStatus()) &&
                        currentUsername.equals(task.getResponsible()))
                .count();
    }

    @Override
    public Page<ProjectTask> findAllOrderTasks(Pageable pageable) {
        // 获取所有任务
        List<ProjectTask> allTasks = taskRepository.findAll();

        // 筛选出类型为"订单"的任务
        List<ProjectTask> orderTasks = allTasks.stream()
                .filter(task -> "订单".equals(task.getType()))
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());

        // 手动实现分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), orderTasks.size());

        return new PageImpl<>(
                orderTasks.subList(start, end),
                pageable,
                orderTasks.size());
    }

    @Override
    public Long countAllInProgressOrderTasks() {
        // 获取所有任务
        List<ProjectTask> allTasks = taskRepository.findAll();

        // 统计类型为"订单"且状态为"进行中"的任务数量
        return allTasks.stream()
                .filter(task -> "订单".equals(task.getType()) &&
                        "进行中".equals(task.getStatus()))
                .count();
    }

    @Override
    public Page<ProjectTask> findAllDifficultTasks(Pageable pageable) {
        // 获取所有任务
        List<ProjectTask> allTasks = taskRepository.findAll();

        // 筛选出难点任务
        List<ProjectTask> difficultTasks = allTasks.stream()
                .filter(task -> task.getType() != null && task.getType().contains("难点"))
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());

        // 手动实现分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), difficultTasks.size());

        return new PageImpl<>(
                difficultTasks.subList(start, end),
                pageable,
                difficultTasks.size());
    }

    @Override
    public Page<ProjectTask> findAllSpecialTasks(Pageable pageable) {
        // 获取所有任务
        List<ProjectTask> allTasks = taskRepository.findAll();

        // 筛选出专项任务
        List<ProjectTask> specialTasks = allTasks.stream()
                .filter(task -> task.getType() != null && task.getType().contains("专项"))
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());

        // 手动实现分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), specialTasks.size());

        return new PageImpl<>(
                specialTasks.subList(start, end),
                pageable,
                specialTasks.size());
    }

    @Override
    public Long countAllInProgressDifficultTasks() {
        // 获取所有任务
        List<ProjectTask> allTasks = taskRepository.findAll();

        // 统计进行中的难点任务数量
        return allTasks.stream()
                .filter(task -> task.getType() != null &&
                        task.getType().contains("难点") &&
                        "进行中".equals(task.getStatus()))
                .count();
    }

    @Override
    public Long countAllInProgressSpecialTasks() {
        // 获取所有任务
        List<ProjectTask> allTasks = taskRepository.findAll();

        // 统计进行中的专项任务数量
        return allTasks.stream()
                .filter(task -> task.getType() != null &&
                        task.getType().contains("专项") &&
                        "进行中".equals(task.getStatus()))
                .count();
    }

    @Override
    public Page<ProjectTask> findOrderTasksByResponsible(String responsible, Pageable pageable) {
        // 获取当前用户的订单任务（这里仍然调用原有的Repository方法）
        Page<ProjectTask> originalPage = taskRepository
                .findByResponsibleAndTypeAndProjectArchiveIsNullOrProjectArchiveNot(
                        responsible, "订单", 1, pageable);

        // 手动对结果进行排序：进行中状态的排在前面，然后按创建时间降序
        List<ProjectTask> sortedTasks = originalPage.getContent().stream()
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());

        // 创建一个新的Page对象，包含排序后的内容
        return new PageImpl<>(sortedTasks, pageable, originalPage.getTotalElements());
    }

    @Override
    public Long countOrderTasksByResponsibleAndStatus(String responsible, String status) {
        return taskRepository.countByResponsibleAndTypeAndStatusAndProjectArchiveIsNullOrProjectArchiveNot(
                responsible, "订单", status, 1);
    }

    @Override
    public List<ProjectTask> findAllTasksNoPage() {
        return taskRepository.findAll();
    }

    @Override
    public void calculateCommentDays(List<ProjectTask> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            logger.debug("没有任务需要计算评论天数");
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        logger.info("开始批量计算{}个任务的评论天数，当前时间: {}", tasks.size(), now);

        // 在内存中计算所有任务的评论天数
        for (ProjectTask task : tasks) {
            // 计算评论天数：当前时间减去最后评论日期，保留一位小数
            double days = 0.0;

            // 获取最后评论日期

            LocalDateTime lastCommentDateTime = task.getLastCommentDateTime();

            if (lastCommentDateTime == null) {
                // 如果没有最后评论日期，则使用任务创建时间
                LocalDateTime createdDateTime = task.getCreatedDateTime();
                if (createdDateTime != null) {
                    // 计算相差的天数
                    long diffInDays = java.time.Duration.between(createdDateTime, now).toDays();
                    days = diffInDays + (java.time.Duration.between(createdDateTime, now).toHours() % 24) / 24.0;
                    // 保留1位小数
                    days = Math.round(days * 10) / 10.0;
                } else {
                    // 如果创建时间也为null，则设置为-1.0
                    days = -1.0;
                }
            } else {
                // 计算相差的天数
                long diffInDays = java.time.Duration.between(lastCommentDateTime, now).toDays();
                days = diffInDays + (java.time.Duration.between(lastCommentDateTime, now).toHours() % 24) / 24.0;
                // 保留1位小数
                days = Math.round(days * 10) / 10.0;
            }

            // 设置评论天数
            task.setCommentDays(days);
        } // 批量保存所有任务到数据库
        try {
            // 在保存前检查并修复外键约束问题
            List<ProjectTask> validTasks = new ArrayList<>();
            List<ProjectTask> invalidTasks = new ArrayList<>();
            for (ProjectTask task : tasks) {
                if (task.getProjectId() != null) {
                    // 检查项目是否存在
                    try {
                        Optional<Project> projectOpt = projectRepository.findById(task.getProjectId());
                        if (projectOpt.isPresent()) {
                            validTasks.add(task);
                        } else {
                            invalidTasks.add(task);
                            logger.warn("任务 {} 引用的项目ID {} 不存在，跳过保存", task.getTaskId(), task.getProjectId());
                        }
                    } catch (Exception e) {
                        invalidTasks.add(task);
                        logger.warn("检查任务 {} 的项目ID {} 时出错: {}", task.getTaskId(), task.getProjectId(), e.getMessage());
                    }
                } else {
                    // 项目ID为null也认为是有效的
                    validTasks.add(task);
                }
            }

            if (!validTasks.isEmpty()) {
                saveAllTasks(validTasks);
                logger.debug("已批量保存{}个有效任务的评论天数", validTasks.size());
            }

            if (!invalidTasks.isEmpty()) {
                logger.warn("跳过了{}个无效任务的保存（项目不存在）", invalidTasks.size());
                // 对于无效任务，我们可以选择单独处理或设置projectId为null
                for (ProjectTask invalidTask : invalidTasks) {
                    try {
                        // 将项目ID设置为null并单独保存
                        invalidTask.setProjectId(null);
                        taskRepository.save(invalidTask);
                        logger.info("已将任务 {} 的项目ID设置为null并保存", invalidTask.getTaskId());
                    } catch (Exception e) {
                        logger.error("修复任务 {} 的项目引用时出错: {}", invalidTask.getTaskId(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("批量保存任务评论天数失败 - {}", e.getMessage(), e);
        }
    }

    @Transactional
    @EventListener
    public void handleTaskUpdateEvent(TaskUpdateEvent event) {
        try {
            Long taskId = event.getTaskId();
            Optional<ProjectTask> taskOpt = findTaskById(taskId);

            if (taskOpt.isPresent()) {
                ProjectTask task = taskOpt.get();

                // 如果任务名称前两个字符以“11”或“12”开头，则获取最后的任务提交时间
                if (task.getTaskName() != null && task.getTaskName().length() >= 2) {
                    String prefix = task.getTaskName().substring(0, 2);
                    if ("11".equals(prefix) || "12".equals(prefix)) { // 从Submit2Service获取该任务的所有提交记录
                        List<Submit2> submits = submit2Service.findAllSubmitsByTaskId(task.getTaskId());
                        // 找出包含"审批通过"的最后一次提交记录的时间
                        LocalDateTime lastSubmitTime = submits.stream()
                                .filter(submit -> submit.getRemarks() != null && submit.getRemarks().contains("审批通过"))
                                .map(Submit2::getSubmitDateTime)
                                .filter(time -> time != null)
                                .max(LocalDateTime::compareTo)
                                .orElse(task.getCreatedDateTime()); // 如果没有找到审批通过的记录，使用任务创建时间

                        // 更新最后评论时间
                        if (lastSubmitTime != null) {
                            task.setLastCommentDateTime(lastSubmitTime);
                        }
                    } else {
                        // 如果任务类型不是培训，则使用当前时间作为最后评论日期
                        task.setLastCommentDateTime(LocalDateTime.now());
                    }
                }

                saveTask(task);
                logger.info("通过事件更新了任务ID {}的最后评论日期", taskId);
            }
        } catch (Exception e) {
            logger.error("处理任务更新事件时出错: {}", e.getMessage(), e);
        }
    }

    @Override
    public List<ProjectTask> findTasksByProjectIdAndStatus(Long projectId, String status) {
        return taskRepository.findByProjectIdAndStatus(projectId, status);
    }

    @Override
    public Long findMaxTaskId() {
        return taskRepository.findMaxTaskId();
    }

    @Override
    public boolean isTaskEditable(Long taskId) {
        Optional<ProjectTask> taskOpt = findTaskById(taskId);
        if (!taskOpt.isPresent()) {
            return true; // 如果任务不存在，默认可编辑（新建任务）
        }

        ProjectTask task = taskOpt.get();

        // 检查任务是否需要审批且正在审批中
        // 审批状态：0-无需审批，1-审批中，2-审批通过，3-审批拒绝
        return task.getNeedApproval() == null || task.getNeedApproval() == 0 ||
                task.getApprovalStatus() == null || task.getApprovalStatus() != 1;
    } // 更新任务审批状态
      // approvalStatus: 0-无需审批，1-审批中，2-审批通过，3-审批拒绝

    @Override
    @Transactional
    public ProjectTask updateTaskApprovalStatus(Long taskId, Integer approvalStatus, Long approvalInstanceId,
            String approverName, String approvalComment) {
        Optional<ProjectTask> taskOpt = findTaskById(taskId);
        if (!taskOpt.isPresent()) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }

        ProjectTask task = taskOpt.get();

        // 更新审批状态
        task.setApprovalStatus(approvalStatus);
        task.setApprovalInstanceId(approvalInstanceId);

        // 如果审批通过或拒绝，设置需要审批标志为0（不需要审批，审批撤销或失败）
        if (approvalStatus == 2 || approvalStatus == 3 || approvalStatus == 0 || approvalStatus == 4) {
            task.setNeedApproval(0);

            // 如果审批拒绝，将任务状态改为"进行中"
            if (approvalStatus == 3 || approvalStatus == 0) {
                // 设置任务状态为"进行中"
                task.setStatus("进行中");
                logger.info("审批拒绝，将任务 {} 状态设置为'进行中'", taskId);

            }
            // 如果审批通过，但为4，将任务状态改为"进行中"
            if (approvalStatus == 4) {
                // 设置任务状态为"进行中"
                task.setStatus("进行中");
                logger.info("审批通过，但将任务 {} 状态设置为'进行中'", taskId);

            }

        } else if (approvalStatus == 1) {
            // 如果审批中，设置需要审批标志为1（需要审批）
            task.setNeedApproval(1);
        }

        String commentTemplate = "审批申请已撤回";
        // 获取当前日期时间格式化字符串
        String currentDateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 如果审批状态为1，表示审批中
        // 这里可以添加其他逻辑，比如发送通知等
        switch (approvalStatus) {
            case 1:
                commentTemplate = "审批提交（" + currentDateTime + "）";
                break;
            case 2, 4:
                String approverText = (approverName != null && !approverName.isEmpty()) ? " - " + approverName : "";
                String approvalCommentText = (approvalComment != null && !approvalComment.isEmpty())
                        ? "，审批意见：" + approvalComment
                        : "";
                commentTemplate = "审批通过" + approverText + approvalCommentText + "（" + currentDateTime + "）";
                break;
            case 3:
                String rejecterText = (approverName != null && !approverName.isEmpty()) ? " - " + approverName : "";
                String rejectionCommentText = (approvalComment != null && !approvalComment.isEmpty())
                        ? "，拒绝原因：" + approvalComment
                        : "";
                commentTemplate = "审批被拒" + rejecterText + rejectionCommentText + "（" + currentDateTime + "）";

                // 审批被拒时，删除该任务的最后一条评论
                try {
                    Optional<SubTask> latestSubTask = subTaskService.findLatestSubTaskByTaskId(taskId);
                    if (latestSubTask.isPresent()) {
                        subTaskService.deleteSubTask(latestSubTask.get().getSubTaskId());
                        logger.info("审批被拒，已删除任务 {} 的最后一条评论，评论ID: {}", taskId, latestSubTask.get().getSubTaskId());
                    } else {
                        logger.info("审批被拒，但任务 {} 没有找到最后一条评论", taskId);
                    }
                } catch (Exception e) {
                    logger.error("删除任务 {} 的最后一条评论时出错: {}", taskId, e.getMessage(), e);
                    // 继续执行，不影响审批流程
                }
                break;

            default:
                String withdrawText = (approverName != null && !approverName.isEmpty()) ? " - " + approverName : "";
                commentTemplate = "审批撤回" + withdrawText + "（" + currentDateTime + "）";

                // 审批撤回时，也删除该任务的最后一条评论
                try {
                    Optional<SubTask> latestSubTask = subTaskService.findLatestSubTaskByTaskId(taskId);
                    if (latestSubTask.isPresent()) {
                        subTaskService.deleteSubTask(latestSubTask.get().getSubTaskId());
                        logger.info("审批撤回，已删除任务 {} 的最后一条评论，评论ID: {}", taskId, latestSubTask.get().getSubTaskId());
                    } else {
                        logger.info("审批撤回，但任务 {} 没有找到最后一条评论", taskId);
                    }
                } catch (Exception e) {
                    logger.error("审批撤回时删除任务 {} 的最后一条评论时出错: {}", taskId, e.getMessage(), e);
                    // 继续执行，不影响审批流程
                }
                break;
        }

        // 查找最后一条提交记录并添加审批状态备注
        try {
            List<Submit2> submits = submit2Service.findAllSubmitsByTaskId(taskId);
            Submit2 latestSubmit = null;

            // 找出最新的提交记录
            if (!submits.isEmpty()) {
                latestSubmit = submits.stream()
                        .max((s1, s2) -> s1.getSubmitDateTime().compareTo(s2.getSubmitDateTime()))
                        .orElse(null);
            }

            if (latestSubmit != null) {
                // 如果找到最后一条提交记录，更新其备注，添加审批状态信息
                String originalRemarks = latestSubmit.getRemarks();
                String updatedRemarks = (originalRemarks != null && !originalRemarks.isEmpty())
                        ? originalRemarks + "\n" + commentTemplate
                        : commentTemplate;
                latestSubmit.setRemarks(updatedRemarks);

                latestSubmit.setWorkflowInstanceId(approvalInstanceId);

                // 如果任务名称前两个字符以“11”或“12”开头，则获取最后的任务提交时间
                if (task.getTaskName() != null && task.getTaskName().length() >= 2) {
                    String prefix = task.getTaskName().substring(0, 2);
                    if ("11".equals(prefix) || "12".equals(prefix)) { // 从Submit2Service获取该任务的所有提交记录

                        // 如果updatedRemarks中包含"审批通过"，则保存submit2Service的提交时间到lastSubmitTime中
                        LocalDateTime lastSubmitTime = null;
                        if (updatedRemarks.contains("审批通过")) {
                            lastSubmitTime = latestSubmit.getSubmitDateTime();
                        }

                        // 更新最后评论时间
                        if (lastSubmitTime != null) {
                            task.setLastCommentDateTime(lastSubmitTime);

                        }
                    }
                }

                submit2Service.saveSubmit(latestSubmit);

                logger.info("已更新任务 {} 的最新提交记录，添加审批状态备注", taskId);
            } else {
                // 如果没有找到提交记录，则添加评论记录
                SubTask commentTask = new SubTask();
                commentTask.setTaskId(taskId);
                commentTask.setLogContent("" + commentTemplate);
                commentTask.setCreatedDateTime(LocalDateTime.now());
                commentTask.setCreatedBy("System");

                // 保存评论
                subTaskService.saveSubTask(commentTask);
                logger.info("未找到任务 {} 的提交记录，已添加评论记录", taskId);
            }
        } catch (Exception e) {
            logger.error("为任务 {} 添加审批拒绝评论时出错: {}", taskId, e.getMessage(), e);
            // 继续执行，不影响流程审批
        }

        return saveTask(task);
    }

    /**
     * 使用自定义ID创建任务
     * 此方法允许使用客户端提供的ID来创建任务，而不是依赖数据库自动生成的ID
     * 
     * @param task 包含自定义ID的任务实体
     * @return 创建后的任务
     */
    @Override
    @Transactional
    public ProjectTask createTaskWithCustomId(ProjectTask task) {
        try {
            logger.info("使用自定义ID创建任务，任务ID: {}", task.getTaskId());

            // 检查任务ID是否存在
            if (task.getTaskId() != null) {
                Optional<ProjectTask> existingTask = taskRepository.findById(task.getTaskId());
                if (existingTask.isPresent()) {
                    logger.warn("ID为{}的任务已存在，将使用新ID进行保存", task.getTaskId());
                    // 设置ID为null，让数据库自动生成
                    task.setTaskId(null);
                    return saveTask(task);
                }
            }

            // 使用自定义ID保存任务
            return taskRepository.save(task);
        } catch (Exception e) {
            logger.error("使用自定义ID创建任务时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据项目客户名称统计任务数量
     * 
     * @param customerName 客户名称
     * @return 任务数量
     */
    @Override
    public Long countTasksByProjectCustomerName(String customerName) {
        return taskRepository.countTasksByProjectCustomerName(customerName);
    }

    @Override
    @Transactional
    public String updateAllInProgressTasksDuration() {
        logger.info("开始更新所有进行中任务的实际工期");

        try {
            // 查找所有状态为"进行中"的任务，并排除审批状态为"审批中"的任务
            List<ProjectTask> allInProgressTasks = findTasksByStatus("进行中");
            List<ProjectTask> inProgressTasks = allInProgressTasks.stream()
                    .filter(task -> task.getApprovalStatus() == null || task.getApprovalStatus() != 1)
                    .collect(Collectors.toList());
            logger.info("找到 {} 个进行中的任务（排除 {} 个审批中的任务）",
                    inProgressTasks.size(), allInProgressTasks.size() - inProgressTasks.size());

            if (inProgressTasks.isEmpty()) {
                return "没有找到进行中的任务";
            }

            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();
            for (ProjectTask task : inProgressTasks) {
                try {
                    // 调用pauseTask方法更新工期，isRestart=true会自动处理状态恢复和开始时间更新
                    ProjectTask updatedTask = pauseTask(task.getTaskId(), true);

                    successCount++;
                    logger.info("成功更新任务 {} 的工期：{} 天",
                            task.getTaskId(),
                            updatedTask.getDurationDays());

                } catch (Exception e) {
                    failCount++;
                    String errorMsg = "任务ID " + task.getTaskId() + " 更新失败：" + e.getMessage();
                    errorMessages.append(errorMsg).append("；");
                    logger.error("更新任务 {} 的工期时出错：{}", task.getTaskId(), e.getMessage(), e);
                }
            }

            String result = String.format("批量更新完成。成功：%d 个，失败：%d 个", successCount, failCount);
            if (failCount > 0) {
                result += "。错误信息：" + errorMessages.toString();
            }

            logger.info("批量更新所有进行中任务工期完成：{}", result);
            return result;

        } catch (Exception e) {
            String errorMsg = "批量更新所有进行中任务工期时出错：" + e.getMessage();
            logger.error(errorMsg, e);
            return errorMsg;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProjectTask> findAllTasksForUpdate() {
        return taskRepository.findAll();
    }
}