package com.mylog.service.impl;

import com.mylog.model.ProjectTask;
import com.mylog.model.Submit2;
import com.mylog.service.AttachmentReminderService;
import com.mylog.service.TaskService;
import com.mylog.service.Submit2Service;
import com.mylog.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * 附件提醒服务实现类
 */
@Service
@Transactional(readOnly = true)
public class AttachmentReminderServiceImpl implements AttachmentReminderService {
    
    private static final Logger logger = LoggerFactory.getLogger(AttachmentReminderServiceImpl.class);
    
    @Autowired
    private TaskService taskService;
    
    @Autowired
    private Submit2Service submit2Service;
    
    @Autowired
    private ProjectService projectService;
    
    @Override
    public List<ProjectTask> getTasksWithRecentAttachments() {
        try {
            logger.info("开始检查需要提醒的任务...");
            
            // 获取所有任务
            List<ProjectTask> allTasks = taskService.findAllTasksNoPage();
            logger.info("获取到所有任务数量: {}", allTasks.size());
            
            List<ProjectTask> reminderTasks = new ArrayList<>();
            
            for (ProjectTask task : allTasks) {
                if (shouldRemindTask(task)) {
                    reminderTasks.add(task);
                }
            }
            
            logger.info("找到需要提醒的任务数量: {}", reminderTasks.size());
            return reminderTasks;
            
        } catch (Exception e) {
            logger.error("检查需要提醒的任务时出错: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public boolean shouldRemindTask(ProjectTask task) {
        try {
            // 1. 检查任务是否关联项目
            if (task.getProjectId() == null) {
                return false;
            }
            
            // 2. 检查项目名称是否包含"发布"
            if (task.getProject() == null) {
                // 尝试加载项目信息
                projectService.findProjectById(task.getProjectId()).ifPresent(project -> {
                    task.setProject(project);
                });
            }
            
            if (task.getProject() == null || task.getProject().getProjectName() == null) {
                return false;
            }
            
            String projectName = task.getProject().getProjectName();
            if (!projectName.contains("发布")) {
                return false;
            }
            
            // 3. 检查最近7天是否有新附件提交
            return hasRecentAttachments(task.getTaskId());
            
        } catch (Exception e) {
            logger.error("检查任务 {} 是否需要提醒时出错: {}", task.getTaskId(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查任务最近7天是否有新附件提交
     */
    private boolean hasRecentAttachments(Long taskId) {
        try {
            // 获取任务的所有提交记录
            List<Submit2> submits = submit2Service.findAllSubmitsByTaskId(taskId);
            
            if (submits.isEmpty()) {
                return false;
            }
            
            // 计算7天前的时间
            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
            
            // 检查是否有最近7天的提交且包含附件且备注包含"审批通过"
            for (Submit2 submit : submits) {
                if (submit.getSubmitDateTime() != null && 
                    submit.getSubmitDateTime().isAfter(sevenDaysAgo)) {
                    
                    // 检查是否有附件
                    if (hasAnyAttachment(submit)) {
                        // 检查备注是否包含"审批通过"
                        if (submit.getRemarks() != null && submit.getRemarks().contains("审批通过")) {
                            logger.debug("任务 {} 在最近7天有新附件提交且备注包含'审批通过'，提交时间: {}", 
                                taskId, submit.getSubmitDateTime());
                            return true;
                        }
                    }
                }
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("检查任务 {} 最近附件时出错: {}", taskId, e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查提交记录是否包含任何附件
     */
    private boolean hasAnyAttachment(Submit2 submit) {
        return (submit.getFilePath1() != null && !submit.getFilePath1().trim().isEmpty()) ||
               (submit.getFilePath2() != null && !submit.getFilePath2().trim().isEmpty()) ||
               (submit.getFilePath3() != null && !submit.getFilePath3().trim().isEmpty()) ||
               (submit.getFilePath4() != null && !submit.getFilePath4().trim().isEmpty());
    }
}