-- 创建电脑信息表
CREATE TABLE IF NOT EXISTS computer_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- ID，主键，自增
    project_id INTEGER,                       -- 项目ID（字符串）
    machine_code TEXT,                     -- 机器码（字符串）
    license_code TEXT,                     -- 注册码（字符串）
    main_version TEXT,                     -- 主程序版本（字符串）
    manager_version TEXT,                  -- 管理器版本（字符串）
    tool_version TEXT,                     -- 工具库版本（字符串）
    computer_model TEXT,                   -- 电脑型号（字符串）
    operating_system TEXT,                 -- 操作系统（字符串）
    remark TEXT                            -- 备注（字符串）
);

-- 为项目ID字段创建索引，加速按项目查询
CREATE INDEX IF NOT EXISTS idx_computer_info_project_id ON computer_info (project_id);

-- 为机器码字段创建索引，加速按机器码查询
CREATE INDEX IF NOT EXISTS idx_computer_info_machine_code ON computer_info (machine_code);

-- 为注册码字段创建索引，加速按注册码查询
CREATE INDEX IF NOT EXISTS idx_computer_info_license_code ON computer_info (license_code);
