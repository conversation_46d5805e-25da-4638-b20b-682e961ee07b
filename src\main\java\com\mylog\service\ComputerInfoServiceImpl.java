package com.mylog.service;

import com.mylog.model.ComputerInfo;
import com.mylog.repository.ComputerInfoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class ComputerInfoServiceImpl implements ComputerInfoService {
    
    @Autowired
    private ComputerInfoRepository computerInfoRepository;
    
    @Override
    public List<ComputerInfo> getComputerInfoByProjectId(Integer projectId) {
        return computerInfoRepository.findByProjectIdOrderByIdDesc(projectId);
    }
    
    @Override
    public Optional<ComputerInfo> getComputerInfoById(Long id) {
        return computerInfoRepository.findById(id);
    }
    
    @Override
    public ComputerInfo saveComputerInfo(ComputerInfo computerInfo) {
        return computerInfoRepository.save(computerInfo);
    }
    
    @Override
    public void deleteComputerInfo(Long id) {
        computerInfoRepository.deleteById(id);
    }
    
    @Override
    public List<ComputerInfo> getAllComputerInfo() {
        return computerInfoRepository.findAll();
    }
    
    @Override
    public List<ComputerInfo> findByMachineCode(String machineCode) {
        return computerInfoRepository.findByMachineCode(machineCode);
    }
    
    @Override
    public List<ComputerInfo> findByLicenseCode(String licenseCode) {
        return computerInfoRepository.findByLicenseCode(licenseCode);
    }
    
    @Override
    public Page<ComputerInfo> findAllComputerInfoPaged(Pageable pageable) {
        return computerInfoRepository.findAllComputerInfoPaged(pageable);
    }
    
    @Override
    public Page<ComputerInfo> dynamicSearchComputerInfo(Map<String, Object> searchCriteria, Pageable pageable) {
        // 如果没有搜索条件，返回所有数据
        if (searchCriteria == null || searchCriteria.isEmpty()) {
            return findAllComputerInfoPaged(pageable);
        }
        
        // 处理不同的搜索条件
        if (searchCriteria.containsKey("machineCode")) {
            String machineCode = (String) searchCriteria.get("machineCode");
            return computerInfoRepository.findByMachineCodeContaining(machineCode, pageable);
        }
        
        if (searchCriteria.containsKey("licenseCode")) {
            String licenseCode = (String) searchCriteria.get("licenseCode");
            return computerInfoRepository.findByLicenseCodeContaining(licenseCode, pageable);
        }
        
        if (searchCriteria.containsKey("createdBy")) {
            String createdBy = (String) searchCriteria.get("createdBy");
            return computerInfoRepository.findByCreatedBy(createdBy, pageable);
        }
        
        if (searchCriteria.containsKey("projectId")) {
            String projectId = (String) searchCriteria.get("projectId");
            return computerInfoRepository.findByProjectId(projectId, pageable);
        }
        
        if (searchCriteria.containsKey("projectName")) {
            String projectName = (String) searchCriteria.get("projectName");
            return computerInfoRepository.findByProjectNameContaining(projectName, pageable);
        }
        
        if (searchCriteria.containsKey("workstationInfo")) {
            String workstationInfo = (String) searchCriteria.get("workstationInfo");
            return computerInfoRepository.findByWorkstationInfoContaining(workstationInfo, pageable);
        }
        
        if (searchCriteria.containsKey("remark")) {
            String remark = (String) searchCriteria.get("remark");
            return computerInfoRepository.findByRemarkContaining(remark, pageable);
        }
        
        // 如果没有匹配的搜索条件，返回所有数据
        return findAllComputerInfoPaged(pageable);
    }
    

    
    @Override
    public boolean hasComputerInfoByProjectId(Long projectId) {
        if (projectId == null) {
            return false;
        }
        List<ComputerInfo> computerInfoList = computerInfoRepository.findByProjectIdOrderByIdDesc(projectId.intValue());
        return computerInfoList != null && !computerInfoList.isEmpty();
    }
}
