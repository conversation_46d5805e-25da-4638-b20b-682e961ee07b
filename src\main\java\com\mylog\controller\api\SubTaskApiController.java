package com.mylog.controller.api;

import com.mylog.model.SubTask;
import com.mylog.service.SubTaskService;
import com.mylog.service.TaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 子任务API控制器
 * 提供子任务相关的RESTful API
 */
@RestController
@RequestMapping("/api/subtasks")
public class SubTaskApiController {

    private static final Logger logger = LoggerFactory.getLogger(SubTaskApiController.class);

    @Autowired
    private SubTaskService subTaskService;

    @Autowired
    private TaskService taskService;

    /**
     * 更新子任务状态
     * 
     * @param subTaskId 子任务ID
     * @param status 新状态 (NOT_STARTED, IN_PROGRESS, COMPLETED)
     * @return 更新结果
     */
    @PostMapping("/{subTaskId}/status")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Map<String, Object>> updateSubTaskStatus(
            @PathVariable Long subTaskId,
            @RequestParam("status") String status) {
        
        logger.info("收到更新子任务状态请求: 子任务ID={}, 新状态={}", subTaskId, status);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 查找子任务
            Optional<SubTask> subTaskOpt = subTaskService.findById(subTaskId);
            
            if (!subTaskOpt.isPresent()) {
                logger.warn("子任务不存在: {}", subTaskId);
                response.put("success", false);
                response.put("message", "子任务不存在");
                return ResponseEntity.badRequest().body(response);
            }
            
            SubTask subTask = subTaskOpt.get();
            SubTask.Status newStatus;
            
            // 解析状态字符串
            try {
                newStatus = SubTask.Status.valueOf(status);
            } catch (IllegalArgumentException e) {
                logger.warn("无效的状态值: {}", status);
                response.put("success", false);
                response.put("message", "无效的状态值");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 根据状态设置开始/结束时间
            LocalDateTime now = LocalDateTime.now();
            
            if (newStatus == SubTask.Status.IN_PROGRESS) {
                subTask.setActualStartDate(now);
                subTask.setActualEndDate(null); // 清除结束时间
            } else if (newStatus == SubTask.Status.COMPLETED) {
                // 如果没有开始时间，也设置开始时间
                if (subTask.getActualStartDate() == null) {
                    subTask.setActualStartDate(now);
                }
                subTask.setActualEndDate(now);
            } else if (newStatus == SubTask.Status.NOT_STARTED) {
                // 重置为未开始状态
                subTask.setActualStartDate(null);
                subTask.setActualEndDate(null);
            }
            
            // 更新状态
            subTask.setStatus(newStatus);
            
            // 保存更新后的子任务
            SubTask savedSubTask = subTaskService.saveSubTask(subTask);
            
            logger.info("子任务状态更新成功: 子任务ID={}, 新状态={}", subTaskId, newStatus);
            
            response.put("success", true);
            response.put("message", "状态更新成功");
            response.put("subTask", savedSubTask);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("更新子任务状态时出错: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "更新状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}