<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('活动日志')}">
    <meta charset="UTF-8">
    <title>活动日志</title>
    <!-- CSRF Token -->
    <meta name="_csrf" th:content="${_csrf?.token}">
    <meta name="_csrf_header" th:content="${_csrf?.headerName}">
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">活动日志</h1>
                </div>

                <!-- 显示消息 -->
                <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${message}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 高级搜索表单 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-search"></i>
                        高级搜索
                    </div>
                    <div class="card-body">
                        <form th:action="@{/admin/activity-logs/search}" method="get" class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">用户</label>
                                <select name="userId" class="form-select">
                                    <option value="">全部用户</option>
                                    <option th:each="user : ${users}"
                                            th:value="${user.userId}"
                                            th:text="${user.username}"
                                            th:selected="${userId != null and userId == user.userId}">
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">活动类型</label>
                                <select name="activityType" class="form-select">
                                    <option value="">全部类型</option>
                                    <option th:each="type : ${activityTypes}"
                                            th:value="${type}"
                                            th:text="${type}"
                                            th:selected="${activityType != null and activityType.toString() == type.toString()}">
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">实体类型</label>
                                <select name="entityType" class="form-select">
                                    <option value="">全部类型</option>
                                    <option th:each="type : ${entityTypes}"
                                            th:value="${type}"
                                            th:text="${type}"
                                            th:selected="${entityType != null and entityType == type}">
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">实体ID</label>
                                <input type="number" name="entityId" class="form-control" th:value="${entityId}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">开始时间</label>
                                <input type="datetime-local" name="startTime" class="form-control"
                                       th:value="${startTime != null ? #temporals.format(startTime, 'yyyy-MM-dd''T''HH:mm') : ''}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">结束时间</label>
                                <input type="datetime-local" name="endTime" class="form-control"
                                       th:value="${endTime != null ? #temporals.format(endTime, 'yyyy-MM-dd''T''HH:mm') : ''}">
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i> 搜索
                                </button>
                                <a th:href="@{/admin/activity-logs}" class="btn btn-secondary">
                                    <i class="bi bi-arrow-clockwise"></i> 重置
                                </a>
                            </div>
                            <!-- 保持分页参数 -->
                            <input type="hidden" name="page" th:value="${currentPage}">
                            <input type="hidden" name="size" th:value="${pageSize}">
                            <input type="hidden" name="sort" th:value="${sortField}">
                            <input type="hidden" name="direction" th:value="${sortDirection}">
                        </form>
                    </div>
                </div>

                <!-- 日志列表 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-table"></i>
                        日志列表
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>时间</th>
                                        <th>用户</th>
                                        <th>活动类型</th>
                                        <th>描述</th>
                                        <th>IP地址</th>
                                        <th>终端类型</th>
                                        <th>实体类型</th>
                                        <th>实体ID</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="log : ${logPage.content}">
                                        <td th:text="${log.id}"></td>
                                        <!-- 使用createdDate字段，但保持与timestamp兼容 -->
                                        <td th:text="${log.createdDate}"></td>
                                        <td th:text="${log.username}"></td>
                                        <td>
                                            <span th:class="${'badge ' + (log.activityType == 'CREATE' ? 'bg-success' : (log.activityType == 'UPDATE' ? 'bg-primary' : (log.activityType == 'DELETE' ? 'bg-danger' : 'bg-info')))}"
                                                  th:text="${log.activityType}"></span>
                                        </td>
                                        <td th:text="${log.description}"></td>
                                        <!-- 其他列保持不变 -->
                                        <td th:text="${log.ipAddress}"></td>
                                        <td th:text="${log.accessType ?: '未知设备'}"></td>
                                        <td th:text="${log.entityType}"></td>                                        <td>
                                            <!-- Task或Submit2类型跳转到任务页面 -->
                                            <a th:if="${log.entityType == 'Task' || log.entityType == 'Submit2'}"
                                               th:href="@{/tasks/{id}(id=${log.entityId})}"
                                               th:text="${log.entityId}">
                                            </a>
                                            <!-- Project类型跳转到项目页面 -->
                                            <a th:if="${log.entityType == 'Project'}"
                                               th:href="@{/projects/{id}(id=${log.entityId})}"
                                               th:text="${log.entityId}">
                                            </a>
                                            <!-- WorkflowInstance类型跳转到流程实例页面 -->
                                            <a th:if="${log.entityType == 'WorkflowInstance'}"
                                               th:href="@{/workflow/instances/{id}(id=${log.entityId})}"
                                               th:text="${log.entityId}">
                                            </a>
                                            <!-- ComputerInfo类型显示ID但不可点击（因为没有独立页面） -->
                                            <span th:if="${log.entityType == 'ComputerInfo'}"
                                                  th:text="'电脑信息 ID: ' + ${log.entityId}"
                                                  class="badge bg-info">
                                            </span>
                                            <!-- 其他类型直接显示ID -->
                                            <span th:if="${log.entityType != 'Task' && log.entityType != 'Submit2' && log.entityType != 'Project' && log.entityType != 'WorkflowInstance' && log.entityType != 'ComputerInfo'}"
                                                  th:text="${log.entityId}">
                                            </span>
                                        </td>
                                        <td>
                                            <a th:href="@{/admin/activity-logs/{id}(id=${log.id})}"
                                               class="btn btn-sm btn-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 分页控件 -->
                <div class="card-footer" th:if="${logPage.totalPages > 0}">
                    <div th:replace="~{fragments/pagination :: pagination(${logPage}, ${isSearch ? '/admin/activity-logs/search' : '/admin/activity-logs'})}"></div>
                </div>

    </div>

    <!-- Bootstrap & jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
</body>
</html>