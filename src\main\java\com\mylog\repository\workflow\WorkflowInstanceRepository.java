package com.mylog.repository.workflow;

import com.mylog.model.workflow.WorkflowInstance;
import com.mylog.model.workflow.WorkflowInstance.WorkflowStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 流程实例存储库接口
 */
@Repository
public interface WorkflowInstanceRepository extends JpaRepository<WorkflowInstance, Long> {

    /**
     * 根据模板ID查找实例列表
     */
    List<WorkflowInstance> findByTemplateTemplateId(Long templateId);

    /**
     * 根据状态查找实例列表
     */
    List<WorkflowInstance> findByStatus(WorkflowStatus status);

    /**
     * 使用原生SQL查询所有实例
     * 避免JPQL可能导致的日期时间格式解析错误
     */
    @Query(value = "SELECT * FROM workflow_instances", nativeQuery = true)
    List<WorkflowInstance> findAllByNativeQuery();
    
    /**
     * 根据发起人查找实例列表
     */
    List<WorkflowInstance> findByInitiator(String initiator);

    /**
     * 根据发起人和状态查找实例列表
     */
    List<WorkflowInstance> findByInitiatorAndStatus(String initiator, WorkflowStatus status);

    /**
     * 根据业务类型和业务ID查找实例列表
     */
    List<WorkflowInstance> findByBusinessTypeAndBusinessId(String businessType, Long businessId);

    /**
     * 查找待办任务列表
     * 包括固定用户审批规则的以及被指定为下一步审批人的任务
     */
    @Query("SELECT DISTINCT wi FROM WorkflowInstance wi " +
           "JOIN WorkflowStep ws ON wi.currentStepId = ws.stepId " +
           "WHERE wi.status = 'PROCESSING' " +
           "AND (ws.approverType = 'FIXED_USER' AND ws.approverConfig LIKE %:username% OR wi.currentApprover = :username) " +
           "ORDER BY wi.createdDate DESC")
    List<WorkflowInstance> findTodoTasks(@Param("username") String username);

    /**
     * 查找待办任务列表（分页版本）
     * 包括固定用户审批规则的以及被指定为下一步审批人的任务
     */
    @Query("SELECT DISTINCT wi FROM WorkflowInstance wi " +
           "JOIN WorkflowStep ws ON wi.currentStepId = ws.stepId " +
           "WHERE wi.status = 'PROCESSING' " +
           "AND (ws.approverType = 'FIXED_USER' AND ws.approverConfig LIKE %:username% OR wi.currentApprover = :username) " +
           "ORDER BY wi.createdDate DESC")
    Page<WorkflowInstance> findTodoTasks(@Param("username") String username, Pageable pageable);

    /**
     * 查找已办任务列表
     * 通过审批记录表关联查询用户已经审批过的流程实例，但不包括用户自己发起的流程
     */
    @Query("SELECT DISTINCT wi FROM WorkflowInstance wi " +
           "JOIN ApprovalRecord ar ON ar.instance.instanceId = wi.instanceId " +
           "WHERE ar.approver = :username " +
           "AND wi.initiator != :username " +
           "ORDER BY ar.createdDate DESC")
    List<WorkflowInstance> findDoneTasks(@Param("username") String username);

    /**
     * 查找已办任务列表（分页版本）
     * 通过审批记录表关联查询用户已经审批过的流程实例，但不包括用户自己发起的流程
     */
    @Query("SELECT DISTINCT wi FROM WorkflowInstance wi " +
           "JOIN ApprovalRecord ar ON ar.instance.instanceId = wi.instanceId " +
           "WHERE ar.approver = :username " +
           "AND wi.initiator != :username " +
           "ORDER BY ar.createdDate DESC")
    Page<WorkflowInstance> findDoneTasks(@Param("username") String username, Pageable pageable);

    /**
     * 综合搜索（分页版本）
     */
    @Query("SELECT wi FROM WorkflowInstance wi " +
           "WHERE (:keyword IS NULL OR :keyword = '' OR wi.title LIKE %:keyword%) " +
           "AND (:status IS NULL OR wi.status = :status) " +
           "AND (:initiator IS NULL OR :initiator = '' OR wi.initiator LIKE %:initiator%) " +
           "AND (:templateId IS NULL OR wi.template.templateId = :templateId) " +
           "AND (:businessType IS NULL OR :businessType = '' OR wi.businessType = :businessType) " +
           "AND (:businessIdStr IS NULL OR :businessIdStr = '' OR CONCAT(wi.businessId, '') LIKE %:businessIdStr%) " +
           "ORDER BY wi.createdDate DESC")
    Page<WorkflowInstance> searchInstances(
            @Param("keyword") String keyword,
            @Param("status") WorkflowStatus status,
            @Param("initiator") String initiator,
            @Param("templateId") Long templateId,
            @Param("businessType") String businessType,
            @Param("businessIdStr") String businessIdStr,
            Pageable pageable);
            
    /**
     * 统计指定状态的流程实例数量
     * 
     * @param status 流程状态
     * @return 对应状态的流程实例数量
     */
    long countByStatus(WorkflowStatus status);
    
    /**
     * 统计某个发起人在指定时间范围内发生的包含特定标题关键词的流程数量
     * 
     * @param initiator 发起人用户名
     * @param titleKeyword 流程标题关键词
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 符合条件的流程数量
     */
    @Query("SELECT COUNT(wi) FROM WorkflowInstance wi " +
           "WHERE wi.initiator = :initiator " +
           "AND wi.title LIKE %:titleKeyword% " +
           "AND wi.startTime >= :startDate " +
           "AND wi.startTime < :endDate " +
           "AND wi.startTime IS NOT NULL")
    long countByInitiatorAndTitleContainingAndSubmittedDateTimeBetween(
            @Param("initiator") String initiator,
            @Param("titleKeyword") String titleKeyword,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);
}
