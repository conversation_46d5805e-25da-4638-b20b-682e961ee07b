package com.mylog.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.apache.catalina.connector.Connector;

/**
 * HTTPS重定向配置
 * 
 * 使用方法：
 * 1. 通过环境变量启用：export MYLOG_FORCE_HTTPS=true
 * 2. 通过系统属性启用：-Dmylog.force.https=true
 * 3. 通过application.properties启用：mylog.force.https=true
 */
@Configuration
public class HttpsRedirectConfig {

    /**
     * 当mylog.force.https=true时，配置HTTP到HTTPS的重定向
     */
    @Bean
    @ConditionalOnProperty(name = "mylog.force.https", havingValue = "true")
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> redirectToHttps() {
        return factory -> {
            // 创建HTTP连接器（端口8080）用于重定向到HTTPS
            Connector httpConnector = new Connector(TomcatServletWebServerFactory.DEFAULT_PROTOCOL);
            httpConnector.setScheme("http");
            httpConnector.setPort(8080);
            httpConnector.setSecure(false);
            httpConnector.setRedirectPort(8443); // 重定向到HTTPS端口
            
            factory.addAdditionalTomcatConnectors(httpConnector);
        };
    }
    
    /**
     * 检查是否应该强制使用HTTPS
     * @return true如果应该强制使用HTTPS
     */
    public static boolean shouldForceHttps() {
        String forceHttps = System.getProperty("mylog.force.https", System.getenv("MYLOG_FORCE_HTTPS"));
        return "true".equals(forceHttps);
    }
}
