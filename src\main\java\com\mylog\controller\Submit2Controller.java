package com.mylog.controller;

import com.mylog.model.Submit2;
import com.mylog.model.ProjectTask;
import com.mylog.model.Project;
import com.mylog.model.SubTask;
import com.mylog.model.Message;
import com.mylog.model.workflow.WorkflowInstance;
import com.mylog.service.Submit2Service;
import com.mylog.service.TaskService;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.service.ProjectService;
import com.mylog.service.SubTaskService;
import com.mylog.service.WorkflowInstanceService;
import com.mylog.util.SubmitFileUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Controller
@RequestMapping("/submits2")
public class Submit2Controller extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(Submit2Controller.class);

    @Autowired
    private Submit2Service submit2Service;

    @Autowired
    private TaskService taskService;

    @Autowired
    private UserActivityLogService activityLogService;

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private SubTaskService subTaskService;

    @Autowired
    private WorkflowInstanceService workflowInstanceService;

    @Autowired
    private com.mylog.service.CloudStorageService cloudStorageService;

    @Autowired
    private com.mylog.service.impl.CpolarPreviewService cpolarPreviewService;

    @Autowired
    private com.mylog.util.WeixinMessageUtil weixinMessageUtil;

    @Autowired
    private com.mylog.service.MessageService messageService;

    @Value("${mylog.data.path:data}")
    private String dataPath;

    @PostMapping("/save")
    public String saveSubmit(
            @RequestParam("taskId") Long taskId,
            @RequestParam("remarks") String remarks,
            @RequestParam(value = "submitName", defaultValue = "提交方案书和规格书") String submitName,
            @RequestParam(value = "file1", required = false) MultipartFile file1,
            @RequestParam(value = "file2", required = false) MultipartFile file2,
            RedirectAttributes redirectAttributes) {

        logger.info("接收到任务提交请求: taskId={}", taskId);

        // 验证备注是否为空
        if (remarks == null || remarks.trim().isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "提交备注不能为空");
            return "redirect:/tasks/" + taskId;
        }

        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();

        // 检查任务是否存在
        Optional<ProjectTask> taskOpt = taskService.findTaskById(taskId);
        if (!taskOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "无法找到指定任务");
            return "redirect:/tasks/" + taskId;
        }

        ProjectTask task = taskOpt.get();

        // 获取项目信息
        String projectCode = "unknown";
        String projectName = "未知项目";
        try {
            // 首先尝试从task中获取project
            Project project = task.getProject();
            if (project != null) {
                projectCode = project.getProjectCode();
                projectName = project.getProjectName();
            } else {
                // 如果延迟加载失败，则直接从数据库查询项目信息
                Long projectId = task.getProjectId();
                Optional<Project> projectOpt = projectService.findProjectById(projectId);
                if (projectOpt.isPresent()) {
                    projectCode = projectOpt.get().getProjectCode();
                    projectName = projectOpt.get().getProjectName();
                }
            }
        } catch (Exception e) {
            logger.warn("获取项目信息时出错: {}", e.getMessage());
            // 继续使用默认值
        }

        // 如果提交名称使用的是旧的默认值，则替换为任务名称
        if ("提交方案书和规格书".equals(submitName)) {
            submitName = task.getTaskName();
        }

        String taskName = task.getTaskName();

        Submit2 submit = new Submit2();
        submit.setTaskId(taskId);
        submit.setRemarks(remarks);
        submit.setSubmitName(submitName);
        submit.setSubmitter(currentUsername);
        submit.setSubmitDateTime(LocalDateTime.now());

        // 根据任务名称确定提交目录
        String submitDir = SubmitFileUtils.determineSubmitDirectory(submitName);

        // 创建提交文件目录
        Path submitDirPath = Paths.get(dataPath, submitDir);
        try {
            if (!Files.exists(submitDirPath)) {
                Files.createDirectories(submitDirPath);
                logger.info("创建提交文件目录: {}", submitDirPath);
            }

            // 处理文件1
            if (file1 != null && !file1.isEmpty()) {
                String originalFilename = file1.getOriginalFilename();
                String fileExtension = "";
                if (originalFilename != null && originalFilename.contains(".")) {
                    fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
                }

                // 根据格式创建新文件名
                Path filePath = SubmitFileUtils.getSubmitFilePath(dataPath, submitDir,
                        projectCode, projectName, taskName,
                        1, fileExtension);
                Files.copy(file1.getInputStream(), filePath);
                submit.setFilePath1(filePath.toString());
                logger.info("保存提交文件1: {}", filePath);
            }

            // 处理文件2
            if (file2 != null && !file2.isEmpty()) {
                String originalFilename = file2.getOriginalFilename();
                String fileExtension = "";
                if (originalFilename != null && originalFilename.contains(".")) {
                    fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
                }

                // 根据格式创建新文件名
                Path filePath = SubmitFileUtils.getSubmitFilePath(dataPath, submitDir,
                        projectCode, projectName, taskName,
                        2, fileExtension);
                Files.copy(file2.getInputStream(), filePath);
                submit.setFilePath2(filePath.toString());
                logger.info("保存提交文件2: {}", filePath);
            }

            // 保存提交记录
            Submit2 savedSubmit = submit2Service.saveSubmit(submit);
            logger.info("成功保存提交记录: {}", savedSubmit.getSubmitId());

            // 检查是否需要发送微信群通知
            boolean hasAttachment = (file1 != null && !file1.isEmpty()) || (file2 != null && !file2.isEmpty());
            boolean isReleaseProject = projectName.contains("发布");
            
            if (hasAttachment && isReleaseProject) {
                sendWeixinNotificationForReleaseSubmit(task, projectName, currentUsername, savedSubmit);
            }

            // 如果有备注，自动创建一条评论
            if (remarks != null && !remarks.trim().isEmpty()) {
                SubTask commentTask = new SubTask();
                commentTask.setTaskId(taskId);
                commentTask.setLogContent("提交备注：" + remarks);
                commentTask.setCreatedDateTime(LocalDateTime.now());
                commentTask.setCreatedBy(currentUsername);

                // 保存评论
                subTaskService.saveSubTask(commentTask);
                logger.info("已添加提交备注作为评论");
            }
            // 完成任务
            try {
                taskService.completeTask(taskId);
                logger.info("已将任务 {} 标记为已完成", taskId);
            } catch (Exception e) {
                logger.error("完成任务时出错: {}", e.getMessage());
                // 提交记录已保存，即使任务状态更新失败也继续执行
            }

            // 记录活动
            String ipAddress = getClientIpAddress();

            // 获取当前用户ID
            Long userId = null;
            try {
                com.mylog.model.user.User user = userService.findUserByUsername(currentUsername).orElse(null);
                if (user != null) {
                    userId = user.getUserId();
                }
            } catch (Exception e) {
                logger.error("获取用户ID时出错: {}", e.getMessage());
            }

            activityLogService.logCreate(
                    userId, // 传入用户ID而不是null
                    currentUsername,
                    "为任务 " + task.getTaskName() + " 添加了提交记录",
                    ipAddress,
                    "Task",
                    task.getTaskId(),
                    getAccessType());

            redirectAttributes.addFlashAttribute("message", "提交记录已保存");

        } catch (IOException e) {
            logger.error("保存提交文件时出错", e);
            redirectAttributes.addFlashAttribute("error", "提交记录保存失败: " + e.getMessage());
        }

        return "redirect:/tasks/" + taskId;
    }

    @PostMapping("/delete")
    public String deleteSubmit(
            @RequestParam("submitId") Long submitId,
            @RequestParam("taskId") Long taskId,
            RedirectAttributes redirectAttributes) {

        logger.info("接收到删除提交请求: submitId={}", submitId);
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));

        // 权限检查：只有管理员可以删除
        if (!isAdmin) {
            redirectAttributes.addFlashAttribute("error", "只有管理员才能删除提交记录");
            return "redirect:/tasks/" + taskId;
        }

        // 获取提交记录
        Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitId);
        if (!submitOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "找不到指定的提交记录");
            return "redirect:/tasks/" + taskId;
        }

        Submit2 submit = submitOpt.get();

        try {
            // 删除文件
            if (submit.getFilePath1() != null && !submit.getFilePath1().isEmpty()) {
                Path filePath = Paths.get(submit.getFilePath1());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                    logger.info("删除提交文件1: {}", filePath);
                }
            }

            if (submit.getFilePath2() != null && !submit.getFilePath2().isEmpty()) {
                Path filePath = Paths.get(submit.getFilePath2());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                    logger.info("删除提交文件2: {}", filePath);
                }
            }

            if (submit.getFilePath3() != null && !submit.getFilePath3().isEmpty()) {
                Path filePath = Paths.get(submit.getFilePath3());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                    logger.info("删除提交文件3: {}", filePath);
                }
            }

            if (submit.getFilePath4() != null && !submit.getFilePath4().isEmpty()) {
                Path filePath = Paths.get(submit.getFilePath4());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                    logger.info("删除提交文件4: {}", filePath);
                }
            }

            // 删除提交记录
            submit2Service.deleteSubmit(submitId);
            logger.info("成功删除提交记录: {}", submitId);

            // 记录活动
            String ipAddress = getClientIpAddress();

            // 获取当前用户ID
            Long userId = null;
            try {
                com.mylog.model.user.User user = userService.findUserByUsername(currentUsername).orElse(null);
                if (user != null) {
                    userId = user.getUserId();
                }
            } catch (Exception e) {
                logger.error("获取用户ID时出错: {}", e.getMessage());
            }

            activityLogService.logDelete(
                    userId, // 传入用户ID而不是null
                    currentUsername,
                    "为任务 " + taskId + " 删除了提交记录",
                    ipAddress,
                    "Task",
                    taskId,
                    getAccessType());

            redirectAttributes.addFlashAttribute("message", "提交记录已删除");

        } catch (IOException e) {
            logger.error("删除提交文件时出错", e);
            redirectAttributes.addFlashAttribute("error", "删除提交记录失败: " + e.getMessage());
        }

        return "redirect:/tasks/" + taskId;
    }

    /**
     * 检查文件下载权限
     */
    @GetMapping("/check/{id}")
    public org.springframework.http.ResponseEntity<String> checkDownloadPermission(
            @PathVariable("id") Long submitId,
            @RequestParam("file") int fileNumber) {

        logger.info("检查文件下载权限: submitId={}, fileNumber={}", submitId, fileNumber);

        // 获取当前用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        if (currentUsername == null) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.UNAUTHORIZED)
                    .body("用户未登录");
        }

        // 查找提交记录
        Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitId);
        if (!submitOpt.isPresent()) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.NOT_FOUND)
                    .body("提交记录不存在");
        }

        Submit2 submit = submitOpt.get();

        // 权限检查：只允许管理员和经理下载文件（与原下载方法保持一致）
        if (!isAdmin && !isManager) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.FORBIDDEN)
                    .body("您没有权限下载此文件，请联系管理员或文件提交者获取授权。");
        }

        // 检查文件是否存在
        String filename = getFilePath(submit, fileNumber);
        if (filename == null) {
                return org.springframework.http.ResponseEntity
                        .status(org.springframework.http.HttpStatus.BAD_REQUEST)
                        .body("无效的文件编号");
        }

        if (filename == null || filename.trim().isEmpty()) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.NOT_FOUND)
                    .body("文件不存在");
        }

        // 检查文件路径
        Path filePath = Paths.get(filename);  // 直接使用数据库中的完整路径
        if (!Files.exists(filePath)) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.NOT_FOUND)
                    .body("文件不存在或已被移动：" + filename);
        }

        return org.springframework.http.ResponseEntity.ok("权限检查通过");
    }

    @GetMapping("/download/{id}")
    public org.springframework.http.ResponseEntity<byte[]> downloadFile(
            @PathVariable("id") Long submitId,
            @RequestParam("file") int fileNumber) {

        logger.info("接收到文件下载请求: submitId={}, fileNumber={}", submitId, fileNumber);

        // 获取当前用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 获取提交记录
        Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitId);
        if (!submitOpt.isPresent()) {
            return org.springframework.http.ResponseEntity.notFound().build();
        }

        Submit2 submit = submitOpt.get();

        // 检查是否为当前审批人
        boolean isCurrentApprover = false;

        // 获取与该提交关联的任务ID
        Long taskId = submit.getTaskId();
        if (taskId != null) {
            // 查找与该任务关联的工作流实例
            List<WorkflowInstance> instances = workflowInstanceService.findInstancesByBusiness("任务", taskId);

            // 检查当前用户是否为任一工作流实例的当前审批人
            for (WorkflowInstance instance : instances) {
                if (currentUsername.equals(instance.getCurrentApprover())) {
                    isCurrentApprover = true;
                    logger.info("用户 {} 是任务 {} 的当前审批人，允许下载文件", currentUsername, taskId);
                    break;
                }
            }
        }

        // 权限检查：允许管理员、提交者本人和当前审批人下载
        // if (!isAdmin && !currentUsername.equals(submit.getSubmitter()) &&
        // !isCurrentApprover) {

        if (!isAdmin && !isManager) {// 只允许管理员和经理下载文件
            logger.warn("用户 {} 无权下载提交 {} 的文件", currentUsername, submitId);

            // 返回更优雅的提示信息而不是403状态码
            byte[] messageBytes = "您没有权限下载此文件，请联系管理员或文件提交者获取授权。".getBytes();
            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION, "inline");
            headers.add(org.springframework.http.HttpHeaders.CONTENT_TYPE, "text/plain;charset=UTF-8");

            return org.springframework.http.ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentLength(messageBytes.length)
                    .body(messageBytes);
        }

        // 获取文件路径
        String filePath = getFilePath(submit, fileNumber);
        if (filePath == null || filePath.isEmpty()) {
            logger.warn("提交记录 {} 的文件{} 路径为空", submitId, fileNumber);
            return org.springframework.http.ResponseEntity.notFound().build();
        }

        try {
            // 读取文件并返回
            Path path = Paths.get(filePath);
            
            // 检查文件是否存在
            if (!Files.exists(path)) {
                logger.error("文件不存在: {}", path.toAbsolutePath());
                
                // 尝试使用相对于数据目录的路径
                Path relativePath = Paths.get(dataPath, filePath);
                if (Files.exists(relativePath)) {
                    logger.info("在数据目录中找到文件: {}", relativePath.toAbsolutePath());
                    path = relativePath;
                } else {
                    logger.error("在数据目录中也未找到文件: {}", relativePath.toAbsolutePath());
                    
                    // 返回友好的错误信息
                    String errorMessage = String.format("文件不存在或已被移动：%s", path.getFileName().toString());
                    byte[] messageBytes = errorMessage.getBytes("UTF-8");
                    org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
                    headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION, "inline");
                    headers.add(org.springframework.http.HttpHeaders.CONTENT_TYPE, "text/plain;charset=UTF-8");

                    return org.springframework.http.ResponseEntity
                            .ok()
                            .headers(headers)
                            .contentLength(messageBytes.length)
                            .body(messageBytes);
                }
            }

            // 从文件路径中提取原始文件名，格式为：项目编号_项目名称_任务名称_序号.扩展名
            String filename = path.getFileName().toString();
            String[] parts = filename.split("_");
            if (parts.length >= 4) {
                // 使用正确的项目编号_项目名称_任务名称格式
                filename = String.join("_", parts);
            }

            byte[] data = Files.readAllBytes(path);
            logger.info("成功读取文件: {}, 大小: {} bytes", path.toAbsolutePath(), data.length);

            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            // 处理文件名中可能包含的中文字符，使用标准的RFC 5987编码
            String encodedFilename = java.net.URLEncoder.encode(filename, "UTF-8").replaceAll("\\+", "%20");
            // 为了兼容性，filename部分也使用编码后的文件名，避免HTTP头中的非ASCII字符
            headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename=\"" + encodedFilename + "\"; filename*=UTF-8''" + encodedFilename);

            // 记录下载活动日志
            try {
                // 获取当前用户ID
                Long userId = null;
                Optional<com.mylog.model.user.User> userOpt = userService.findUserByUsername(currentUsername);
                if (userOpt.isPresent()) {
                    userId = userOpt.get().getUserId();
                }

                // 构建下载描述信息
                String description = String.format("下载了提交记录 %d 的文件%d：%s",
                        submitId, fileNumber, path.getFileName().toString());

                // 记录下载活动日志
                activityLogService.logDownload(
                        userId,
                        currentUsername,
                        description,
                        getClientIpAddress(),
                        "Task",
                        taskId,
                        getAccessType());

                logger.info("用户 {} 下载了提交记录 {} 的文件{}：{}", 
                           currentUsername, submitId, fileNumber, path.getFileName().toString());

                // 发送下载通知消息给提交人
                sendDownloadNotificationToSubmitter(submit, currentUsername, fileNumber, path.getFileName().toString());

            } catch (Exception logException) {
                // 日志记录失败不影响文件下载
                logger.error("记录下载活动日志时出错: {}", logException.getMessage(), logException);
            }

            return org.springframework.http.ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentLength(data.length)
                    .contentType(org.springframework.http.MediaType.APPLICATION_OCTET_STREAM)
                    .body(data);

        } catch (IOException e) {
            logger.error("下载文件时出错: {}", e.getMessage(), e);
            
            // 为特定类型的IO异常提供更友好的错误信息
            String errorMessage;
            if (e instanceof java.nio.file.NoSuchFileException) {
                errorMessage = "文件不存在或已被移动，请联系管理员检查文件状态。";
            } else if (e instanceof java.nio.file.AccessDeniedException) {
                errorMessage = "无法访问文件，可能文件正在被其他程序使用。";
            } else {
                errorMessage = "下载文件时发生错误：" + e.getMessage();
            }
            
            // 返回友好的错误信息而不是HTTP 500错误
            try {
                byte[] messageBytes = errorMessage.getBytes("UTF-8");
                org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
                headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION, "inline");
                headers.add(org.springframework.http.HttpHeaders.CONTENT_TYPE, "text/plain;charset=UTF-8");

                return org.springframework.http.ResponseEntity
                        .ok()
                        .headers(headers)
                        .contentLength(messageBytes.length)
                        .body(messageBytes);
            } catch (Exception encodeException) {
                logger.error("编码错误信息时出错", encodeException);
                return org.springframework.http.ResponseEntity
                        .status(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        }
    }

    /**
     * 在线查看文件（通过腾讯在线文档）
     */
    @GetMapping("/view/{id}")
    public org.springframework.http.ResponseEntity<String> viewFileOnline(
            @PathVariable("id") Long submitId,
            @RequestParam("file") int fileNumber) {

        logger.info("接收到在线查看文件请求: submitId={}, fileNumber={}", submitId, fileNumber);

        // 获取当前用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 获取提交记录
        Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitId);
        if (!submitOpt.isPresent()) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.NOT_FOUND)
                    .body("{\"error\": \"提交记录不存在\"}");
        }

        Submit2 submit = submitOpt.get();

        // 权限检查：只允许管理员和经理查看文件
        if (!isAdmin && !isManager) {
            logger.warn("用户 {} 无权查看提交 {} 的文件", currentUsername, submitId);
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.FORBIDDEN)
                    .body("{\"error\": \"您没有权限查看此文件，请联系管理员获取授权。\"}");
        }

        // 获取文件路径
        String filePath = getFilePath(submit, fileNumber);
        if (filePath == null || filePath.isEmpty()) {
            logger.warn("提交记录 {} 的文件{} 路径为空", submitId, fileNumber);
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.NOT_FOUND)
                    .body("{\"error\": \"文件不存在\"}");
        }

        try {
            // 检查文件是否存在
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                // 尝试使用相对于数据目录的路径
                Path relativePath = Paths.get(dataPath, filePath);
                if (Files.exists(relativePath)) {
                    path = relativePath;
                } else {
                    logger.error("文件不存在: {}", path.toAbsolutePath());
                    return org.springframework.http.ResponseEntity
                            .status(org.springframework.http.HttpStatus.NOT_FOUND)
                            .body("{\"error\": \"文件不存在或已被移动\"}");
                }
            }

            // 返回文件信息
            String filename = path.getFileName().toString();
            String jsonResponse = String.format("{\"filename\": \"%s\", \"exists\": true}", filename);
            
            // 记录查看活动日志
            try {
                Long userId = null;
                Optional<com.mylog.model.user.User> userOpt = userService.findUserByUsername(currentUsername);
                if (userOpt.isPresent()) {
                    userId = userOpt.get().getUserId();
                }

                String description = String.format("请求查看了提交记录 %d 的文件%d：%s",
                        submitId, fileNumber, filename);

                activityLogService.logView(
                        userId,
                        currentUsername,
                        description,
                        getClientIpAddress(),
                        "Task",
                        submit.getTaskId(),
                        getAccessType());

                logger.info("用户 {} 请求查看提交记录 {} 的文件{}：{}", 
                           currentUsername, submitId, fileNumber, filename);

                // 发送查看通知消息给提交人（不发送微信）
                sendViewNotificationToSubmitter(submit, currentUsername, fileNumber, filename);

            } catch (Exception logException) {
                logger.error("记录查看活动日志时出错: {}", logException.getMessage(), logException);
            }

            // 返回文件信息
            return org.springframework.http.ResponseEntity
                    .ok()
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(jsonResponse);

        } catch (Exception e) {
            logger.error("获取文件信息时出错: {}", e.getMessage(), e);
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("{\"error\": \"获取文件信息失败：" + e.getMessage() + "\"}");
        }
    }

    /**
     * 提供文件服务用于在线预览
     */
    @GetMapping("/serve/{id}")
    public org.springframework.http.ResponseEntity<byte[]> serveFile(
            @PathVariable("id") Long submitId,
            @RequestParam("file") int fileNumber,
            @RequestParam(value = "temp", required = false) Boolean isTemp,
            @RequestParam(value = "t", required = false) Long timestamp,
            @RequestParam(value = "mode", required = false, defaultValue = "inline") String mode) {

        logger.info("提供文件服务用于在线预览: submitId={}, fileNumber={}, isTemp={}, timestamp={}, mode={}", 
                   submitId, fileNumber, isTemp, timestamp, mode);

        // 获取当前用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 权限检查
        if (!isAdmin && !isManager) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.FORBIDDEN).build();
        }

        // 获取提交记录
        Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitId);
        if (!submitOpt.isPresent()) {
            return org.springframework.http.ResponseEntity.notFound().build();
        }

        Submit2 submit = submitOpt.get();
        String filePath = getFilePath(submit, fileNumber);
        
        if (filePath == null || filePath.isEmpty()) {
            return org.springframework.http.ResponseEntity.notFound().build();
        }

        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                Path relativePath = Paths.get(dataPath, filePath);
                if (Files.exists(relativePath)) {
                    path = relativePath;
                } else {
                    return org.springframework.http.ResponseEntity.notFound().build();
                }
            }

            byte[] data = Files.readAllBytes(path);
            String filename = path.getFileName().toString();

            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            
            // 设置内容类型 - 扩展文件类型支持
            String contentType = "application/octet-stream";
            String lowerFilename = filename.toLowerCase();
            boolean isOfficeDocument = false;
            
            // Office文档
            if (lowerFilename.endsWith(".pdf")) {
                contentType = "application/pdf";
            } else if (lowerFilename.endsWith(".docx")) {
                contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                isOfficeDocument = true;
            } else if (lowerFilename.endsWith(".doc")) {
                contentType = "application/msword";
                isOfficeDocument = true;
            } else if (lowerFilename.endsWith(".xlsx")) {
                contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                isOfficeDocument = true;
            } else if (lowerFilename.endsWith(".xls")) {
                contentType = "application/vnd.ms-excel";
                isOfficeDocument = true;
            } else if (lowerFilename.endsWith(".pptx")) {
                contentType = "application/vnd.openxmlformats-officedocument.presentationml.presentation";
                isOfficeDocument = true;
            } else if (lowerFilename.endsWith(".ppt")) {
                contentType = "application/vnd.ms-powerpoint";
                isOfficeDocument = true;
            // 图片文件
            } else if (lowerFilename.endsWith(".jpg") || lowerFilename.endsWith(".jpeg")) {
                contentType = "image/jpeg";
            } else if (lowerFilename.endsWith(".png")) {
                contentType = "image/png";
            } else if (lowerFilename.endsWith(".gif")) {
                contentType = "image/gif";
            } else if (lowerFilename.endsWith(".bmp")) {
                contentType = "image/bmp";
            } else if (lowerFilename.endsWith(".svg")) {
                contentType = "image/svg+xml";
            // 文本文件
            } else if (lowerFilename.endsWith(".txt")) {
                contentType = "text/plain; charset=utf-8";
            } else if (lowerFilename.endsWith(".csv")) {
                contentType = "text/csv; charset=utf-8";
            } else if (lowerFilename.endsWith(".html") || lowerFilename.endsWith(".htm")) {
                contentType = "text/html; charset=utf-8";
            } else if (lowerFilename.endsWith(".xml")) {
                contentType = "application/xml; charset=utf-8";
            } else if (lowerFilename.endsWith(".json")) {
                contentType = "application/json; charset=utf-8";
            }

            // 对于Office文档的浏览器预览模式，返回HTML包装器而不是原始文档
            if ("browser".equals(mode) && isOfficeDocument) {
                String htmlContent = createOfficePreviewHtml(filename, submitId, fileNumber);
                byte[] htmlBytes = htmlContent.getBytes("UTF-8");
                
                headers.add("Content-Type", "text/html; charset=UTF-8");
                headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
                headers.add("Pragma", "no-cache");
                headers.add("Expires", "0");
                
                return org.springframework.http.ResponseEntity
                        .ok()
                        .headers(headers)
                        .contentLength(htmlBytes.length)
                        .contentType(org.springframework.http.MediaType.TEXT_HTML)
                        .body(htmlBytes);
            }
            
            // 设置响应头 - 根据模式决定Content-Disposition
            String contentDisposition;
            if ("download".equals(mode)) {
                // 强制下载模式
                contentDisposition = createSafeContentDisposition(filename, true);
            } else if ("embed".equals(mode)) {
                // 嵌入式预览 - 不设置Content-Disposition，让浏览器自行判断
                contentDisposition = null;
            } else {
                // 默认inline模式
                contentDisposition = createSafeContentDisposition(filename, false);
            }
            
            if (contentDisposition != null) {
                headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION, contentDisposition);
            }
            
            headers.add("Access-Control-Allow-Origin", "*");
            headers.add("Access-Control-Allow-Methods", "GET, HEAD, OPTIONS");
            headers.add("Access-Control-Allow-Headers", "Content-Type, Authorization, Range");
            headers.add("Access-Control-Expose-Headers", "Content-Length, Content-Range, Content-Type");
            
            // 为Office Web Viewer添加必要的头
            headers.add("X-Frame-Options", "ALLOWALL");
            headers.add("X-Content-Type-Options", "nosniff");
            
            // 如果是临时URL请求，添加缓存控制
            if (Boolean.TRUE.equals(isTemp)) {
                headers.add("Cache-Control", "public, max-age=300"); // 5分钟缓存
                headers.add("X-Preview-Mode", "temporary");
            } else {
                headers.add("Cache-Control", "private, max-age=86400"); // 24小时缓存
            }
            
            // 添加文件信息头
            try {
                String encodedFilenameHeader = java.net.URLEncoder.encode(filename, "UTF-8");
                headers.add("X-File-Name", encodedFilenameHeader);
            } catch (java.io.UnsupportedEncodingException e) {
                headers.add("X-File-Name", "unknown");
            }
            headers.add("X-File-Size", String.valueOf(data.length));

            return org.springframework.http.ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentLength(data.length)
                    .contentType(org.springframework.http.MediaType.parseMediaType(contentType))
                    .body(data);

        } catch (IOException e) {
            logger.error("提供文件服务时出错: {}", e.getMessage(), e);
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 创建Office文档的HTML预览包装器
     */
    private String createOfficePreviewHtml(String filename, Long submitId, int fileNumber) {
        String downloadUrl = "/submits2/download/" + submitId + "?file=" + fileNumber;
        
        return String.format("""
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>文件预览 - %s</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
                <style>
                    body { background: #f8f9fa; }
                    .preview-container { 
                        max-width: 800px; 
                        margin: 2rem auto; 
                        background: white; 
                        border-radius: 10px; 
                        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                        padding: 2rem;
                    }
                    .file-icon { 
                        font-size: 4rem; 
                        color: #0d6efd; 
                        margin-bottom: 1rem;
                    }
                    .btn-download {
                        background: linear-gradient(135deg, #0d6efd, #6610f2);
                        border: none;
                        padding: 12px 30px;
                        font-size: 1.1rem;
                        border-radius: 8px;
                        transition: all 0.3s ease;
                    }
                    .btn-download:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 6px 20px rgba(13,110,253,0.3);
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="preview-container text-center">
                        <div class="file-icon">
                            <i class="bi bi-file-earmark-text"></i>
                        </div>
                        <h3 class="mb-3">%s</h3>
                        <p class="text-muted mb-4">
                            此文档需要下载后使用相应的应用程序打开查看。<br>
                            浏览器无法直接显示Office文档的完整内容。
                        </p>
                        
                        <div class="d-grid gap-3 d-md-block">
                            <a href="%s" class="btn btn-download text-white me-md-2">
                                <i class="bi bi-download me-2"></i>
                                下载文档
                            </a>
                            <button class="btn btn-outline-secondary" onclick="window.close();">
                                <i class="bi bi-x-circle me-2"></i>
                                关闭窗口
                            </button>
                        </div>
                        
                        <div class="mt-4 pt-3 border-top">
                            <small class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                建议使用 Microsoft Office、WPS Office 或其他兼容软件打开查看
                            </small>
                        </div>
                    </div>
                </div>
                
                <script>
                    // 3秒后自动开始下载
                    setTimeout(function() {
                        if (confirm('是否自动下载文档？')) {
                            window.location.href = '%s';
                        }
                    }, 3000);
                </script>
            </body>
            </html>
            """, filename, filename, downloadUrl, downloadUrl);
    }

    /**
     * 安全地处理文件名编码，避免HTTP头中的非ASCII字符问题
     * @param filename 原始文件名
     * @return 编码后的Content-Disposition头值
     */
    private String createSafeContentDisposition(String filename, boolean isAttachment) {
        try {
            // 使用UTF-8编码文件名
            String encodedFilename = java.net.URLEncoder.encode(filename, "UTF-8").replaceAll("\\+", "%20");
            
            // 使用RFC 5987标准格式
            String disposition = isAttachment ? "attachment" : "inline";
            return disposition + "; filename*=UTF-8''" + encodedFilename;
            
        } catch (java.io.UnsupportedEncodingException e) {
            // 如果编码失败，创建安全的ASCII文件名
            String safeFilename = filename.replaceAll("[^\\x00-\\x7F]", "_");
            String disposition = isAttachment ? "attachment" : "inline";
            
            logger.warn("文件名编码失败，使用安全文件名: {} -> {}", filename, safeFilename);
            return disposition + "; filename=\"" + safeFilename + "\"";
        }
    }

    /**
     * 获取文件Base64内容用于Office Web Viewer
     */
    @GetMapping("/office-preview/{id}")
    @ResponseBody
    public org.springframework.http.ResponseEntity<java.util.Map<String, Object>> getOfficePreviewData(
            @PathVariable("id") Long submitId,
            @RequestParam("file") int fileNumber) {
        
        logger.info("生成Office预览数据: submitId={}, fileNumber={}", submitId, fileNumber);
        
        try {
            // 获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            boolean isAdmin = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
            boolean isManager = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

            // 权限检查
            if (!isAdmin && !isManager) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "权限不足");
                return org.springframework.http.ResponseEntity.status(org.springframework.http.HttpStatus.FORBIDDEN).body(errorResponse);
            }

            // 获取提交记录
            Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitId);
            if (!submitOpt.isPresent()) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "提交记录不存在");
                return org.springframework.http.ResponseEntity.notFound().build();
            }

            Submit2 submit = submitOpt.get();
            String filePath = getFilePath(submit, fileNumber);
            
            if (filePath == null || filePath.isEmpty()) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "文件不存在");
                return org.springframework.http.ResponseEntity.notFound().build();
            }

            // 检查文件是否存在
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                Path relativePath = Paths.get(dataPath, filePath);
                if (Files.exists(relativePath)) {
                    path = relativePath;
                } else {
                    java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                    errorResponse.put("success", false);
                    errorResponse.put("message", "文件不存在或已被移动");
                    return org.springframework.http.ResponseEntity.notFound().build();
                }
            }

            String filename = path.getFileName().toString();
            String fileExtension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
            
            // 检查是否为支持的Office文档
            if (!isOfficeDocument(fileExtension)) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "不支持的文件类型: " + fileExtension);
                return org.springframework.http.ResponseEntity.badRequest().body(errorResponse);
            }

            // 读取文件内容并转换为Base64
            byte[] fileContent = Files.readAllBytes(path);
            String base64Content = java.util.Base64.getEncoder().encodeToString(fileContent);
            
            // 获取MIME类型
            String mimeType = getMimeTypeForOffice(fileExtension);
            
            // 记录活动日志
            String ipAddress = getClientIpAddress();
            Long userId = null;
            try {
                com.mylog.model.user.User user = userService.findUserByUsername(currentUsername).orElse(null);
                if (user != null) {
                    userId = user.getUserId();
                }
            } catch (Exception e) {
                logger.error("获取用户ID时出错: {}", e.getMessage());
            }

            activityLogService.logView(
                    userId,
                    currentUsername,
                    "预览了文件: " + filename,
                    ipAddress,
                    "File",
                    submitId,
                    getAccessType());

            // 构建响应数据
            java.util.Map<String, Object> response = new java.util.HashMap<>();
            response.put("success", true);
            response.put("filename", filename);
            response.put("fileExtension", fileExtension);
            response.put("mimeType", mimeType);
            response.put("fileSize", fileContent.length);
            response.put("base64Content", base64Content);
            response.put("timestamp", System.currentTimeMillis());
            
            logger.info("成功生成Office预览数据: {} (大小: {} bytes)", filename, fileContent.length);
            
            return org.springframework.http.ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("生成Office预览数据失败", e);
            java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "生成预览数据时出错: " + e.getMessage());
            return org.springframework.http.ResponseEntity.status(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 上传文件到云存储并返回公网URL
     */
    @PostMapping("/upload-to-cloud/{id}")
    @ResponseBody
    public org.springframework.http.ResponseEntity<java.util.Map<String, Object>> uploadToCloud(
            @PathVariable("id") Long submitId,
            @RequestParam("file") int fileNumber) {
        
        logger.info("上传文件到云存储: submitId={}, fileNumber={}", submitId, fileNumber);
        
        try {
            // 获取当前用户信息和权限检查
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            boolean isAdmin = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
            boolean isManager = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

            if (!isAdmin && !isManager) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "权限不足");
                return org.springframework.http.ResponseEntity.status(org.springframework.http.HttpStatus.FORBIDDEN).body(errorResponse);
            }

            // 获取提交记录
            Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitId);
            if (!submitOpt.isPresent()) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "提交记录不存在");
                return org.springframework.http.ResponseEntity.notFound().build();
            }

            Submit2 submit = submitOpt.get();
            String filePath = getFilePath(submit, fileNumber);
            
            if (filePath == null || filePath.isEmpty()) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "文件不存在");
                return org.springframework.http.ResponseEntity.notFound().build();
            }

            // 检查文件是否存在
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                Path relativePath = Paths.get(dataPath, filePath);
                if (Files.exists(relativePath)) {
                    path = relativePath;
                } else {
                    java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                    errorResponse.put("success", false);
                    errorResponse.put("message", "文件不存在或已被移动");
                    return org.springframework.http.ResponseEntity.notFound().build();
                }
            }

            String filename = path.getFileName().toString();
            String fileExtension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
            
            // 检查是否为Office文档
            if (!isOfficeDocument(fileExtension)) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "不支持的文件类型: " + fileExtension);
                return org.springframework.http.ResponseEntity.badRequest().body(errorResponse);
            }

            logger.info("准备上传文件到云存储: {}, 文件路径: {}", filename, path.toAbsolutePath());
            
            // 调用云存储上传逻辑
            String publicUrl = uploadFileToCloudStorage(path, filename);
            
            logger.info("云存储上传完成，获得公网URL: {}", publicUrl);
            
            // 记录活动日志
            String ipAddress = getClientIpAddress();
            Long userId = null;
            try {
                com.mylog.model.user.User user = userService.findUserByUsername(currentUsername).orElse(null);
                if (user != null) {
                    userId = user.getUserId();
                }
            } catch (Exception e) {
                logger.error("获取用户ID时出错: {}", e.getMessage());
            }

            activityLogService.logView(
                    userId,
                    currentUsername,
                    "上传文件到云存储: " + filename,
                    ipAddress,
                    "CloudUpload",
                    submitId,
                    getAccessType());

            // 构建响应数据
            java.util.Map<String, Object> response = new java.util.HashMap<>();
            response.put("success", true);
            response.put("filename", filename);
            response.put("fileExtension", fileExtension);
            response.put("publicUrl", publicUrl);
            response.put("uploadTime", System.currentTimeMillis());
            
            logger.info("成功上传文件到云存储: {} -> {}", filename, publicUrl);
            
            return org.springframework.http.ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("上传文件到云存储失败", e);
            java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "上传失败: " + e.getMessage());
            return org.springframework.http.ResponseEntity.status(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 上传文件到云存储服务
     */
    private String uploadFileToCloudStorage(Path filePath, String filename) throws Exception {
        logger.info("开始上传文件到云存储: {}", filename);
        logger.info("云存储服务实例: {}", cloudStorageService != null ? cloudStorageService.getClass().getSimpleName() : "null");
        
        if (cloudStorageService == null) {
            throw new UnsupportedOperationException("云存储服务未注入，请检查Spring配置");
        }
        
        logger.info("检查云存储服务可用性...");
        if (!cloudStorageService.isAvailable()) {
            logger.error("云存储服务不可用，服务类型: {}", cloudStorageService.getServiceType());
            throw new UnsupportedOperationException(
                "云存储服务不可用。请检查配置文件中的云存储设置 (cloud.storage.type)。\n" +
                "支持的服务类型：aliyun-oss, tencent-cos, aws-s3\n" +
                "当前服务类型：" + cloudStorageService.getServiceType()
            );
        }
        
        logger.info("使用 {} 服务上传文件: {}", cloudStorageService.getServiceType(), filename);
        
        try {
            String publicUrl = cloudStorageService.uploadFile(filePath, filename);
            logger.info("文件上传成功: {} -> {}", filename, publicUrl);
            return publicUrl;
        } catch (Exception e) {
            logger.error("文件上传失败: {}", filename, e);
            throw new Exception("上传文件到云存储失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查是否为Office文档
     */
    private boolean isOfficeDocument(String fileExtension) {
        String ext = fileExtension.toLowerCase();
        return ext.equals("docx") || ext.equals("doc") || 
               ext.equals("xlsx") || ext.equals("xls") || 
               ext.equals("pptx") || ext.equals("ppt");
    }

    /**
     * 获取Office文档的MIME类型
     */
    private String getMimeTypeForOffice(String fileExtension) {
        String ext = fileExtension.toLowerCase();
        switch (ext) {
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "doc":
                return "application/msword";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "xls":
                return "application/vnd.ms-excel";
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "ppt":
                return "application/vnd.ms-powerpoint";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 生成cpolar预览URL（不上传到云存储，直接通过cpolar隧道访问）
     */
    @PostMapping("/generate-cpolar-preview/{id}")
    @ResponseBody
    public org.springframework.http.ResponseEntity<java.util.Map<String, Object>> generateCpolarPreview(
            @PathVariable("id") Long submitId,
            @RequestParam("file") int fileNumber) {
        
        logger.info("生成cpolar预览URL: submitId={}, fileNumber={}", submitId, fileNumber);
        
        try {
            // 获取当前用户信息和权限检查
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            boolean isAdmin = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
            boolean isManager = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

            if (!isAdmin && !isManager) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "权限不足");
                return org.springframework.http.ResponseEntity.status(org.springframework.http.HttpStatus.FORBIDDEN).body(errorResponse);
            }

            // 检查cpolar预览功能是否启用
            if (!cpolarPreviewService.isEnabled()) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "cpolar预览功能未启用或域名未配置");
                return org.springframework.http.ResponseEntity.badRequest().body(errorResponse);
            }

            // 获取提交记录
            Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitId);
            if (!submitOpt.isPresent()) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "提交记录不存在");
                return org.springframework.http.ResponseEntity.notFound().build();
            }

            Submit2 submit = submitOpt.get();
            String filePath = getFilePath(submit, fileNumber);
            
            if (filePath == null || filePath.isEmpty()) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "文件不存在");
                return org.springframework.http.ResponseEntity.notFound().build();
            }

            // 检查文件是否存在
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                Path relativePath = Paths.get(dataPath, filePath);
                if (Files.exists(relativePath)) {
                    path = relativePath;
                } else {
                    java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                    errorResponse.put("success", false);
                    errorResponse.put("message", "文件不存在或已被移动");
                    return org.springframework.http.ResponseEntity.notFound().build();
                }
            }

            String filename = path.getFileName().toString();
            String fileExtension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
            
            // 检查是否为Office文档
            if (!isOfficeDocument(fileExtension)) {
                java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "不支持的文件类型: " + fileExtension);
                return org.springframework.http.ResponseEntity.badRequest().body(errorResponse);
            }

            logger.info("生成cpolar预览URL: {}", filename);
            
            // 生成cpolar公网URL（包含安全token）
            String publicUrl = cpolarPreviewService.generatePreviewUrl(submitId.toString(), String.valueOf(fileNumber));
            
            logger.info("cpolar预览URL生成完成: {}", publicUrl);
            
            // 记录活动日志
            String ipAddress = getClientIpAddress();
            Long userId = null;
            try {
                com.mylog.model.user.User user = userService.findUserByUsername(currentUsername).orElse(null);
                if (user != null) {
                    userId = user.getUserId();
                }
            } catch (Exception e) {
                logger.error("获取用户ID时出错: {}", e.getMessage());
            }

            activityLogService.logView(
                    userId,
                    currentUsername,
                    "生成cpolar预览URL: " + filename,
                    ipAddress,
                    "CpolarPreview",
                    submitId,
                    getAccessType());

            // 构建响应数据
            java.util.Map<String, Object> response = new java.util.HashMap<>();
            response.put("success", true);
            response.put("filename", filename);
            response.put("fileExtension", fileExtension);
            response.put("publicUrl", publicUrl);
            response.put("previewType", "cpolar");
            response.put("generateTime", System.currentTimeMillis());
            
            logger.info("成功生成cpolar预览URL: {} -> {}", filename, publicUrl);
            
            return org.springframework.http.ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("生成cpolar预览URL失败", e);
            java.util.Map<String, Object> errorResponse = new java.util.HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "生成预览URL失败: " + e.getMessage());
            return org.springframework.http.ResponseEntity.status(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 根据文件编号获取对应的文件路径
     */
    private String getFilePath(Submit2 submit, int fileNumber) {
        switch (fileNumber) {
            case 1: return submit.getFilePath1();
            case 2: return submit.getFilePath2();
            case 3: return submit.getFilePath3();
            case 4: return submit.getFilePath4();
            default: return null;
        }
    }

    /**
     * 发送发布项目提交附件的微信群通知
     */
    private void sendWeixinNotificationForReleaseSubmit(ProjectTask task, String projectName, String submitter, Submit2 submit) {
        try {
            // 构建通知消息
            String title = "📋 发布项目提交通知";
            StringBuilder content = new StringBuilder();
            content.append("<@all>\n");
            content.append("**项目名称**: ").append(projectName).append("\n");
            String taskMarkdownLink = String.format("[%s](https://prj.cpolar.cn/tasks/%d)", 
                task.getTaskName(), task.getTaskId());
            content.append("**任务名称**: ").append(taskMarkdownLink).append("\n");
            content.append("**提交人**: ").append(submitter).append("\n");
            content.append("**提交时间**: ").append(submit.getSubmitDateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n\n");
            
            // 添加附件信息
            content.append("**附件信息**:\n");
            int attachmentCount = 0;
            if (submit.getFilePath1() != null && !submit.getFilePath1().isEmpty()) {
                attachmentCount++;
                content.append("- 附件1: ").append(extractFileName(submit.getFilePath1())).append("\n");
            }
            if (submit.getFilePath2() != null && !submit.getFilePath2().isEmpty()) {
                attachmentCount++;
                content.append("- 附件2: ").append(extractFileName(submit.getFilePath2())).append("\n");
            }
            if (submit.getFilePath3() != null && !submit.getFilePath3().isEmpty()) {
                attachmentCount++;
                content.append("- 附件3: ").append(extractFileName(submit.getFilePath3())).append("\n");
            }
            if (submit.getFilePath4() != null && !submit.getFilePath4().isEmpty()) {
                attachmentCount++;
                content.append("- 附件4: ").append(extractFileName(submit.getFilePath4())).append("\n");
            }
            
            content.append("\n共 ").append(attachmentCount).append(" 个附件\n\n");
            
            if (submit.getRemarks() != null && !submit.getRemarks().trim().isEmpty()) {
                content.append("**备注**: ").append(submit.getRemarks()).append("\n\n");
            }
            
            content.append("请相关人员及时查看和处理。");
            
            // 发送微信群通知，@所有人
            java.util.List<String> mentionList = new java.util.ArrayList<>();
            mentionList.add("@all"); // @所有人
            
            weixinMessageUtil.sendWeixinMessage(title, content.toString(), mentionList);
            
            logger.info("已发送发布项目提交的微信群通知: 项目={}, 任务={}, 提交人={}", 
                       projectName, task.getTaskName(), submitter);
            
            // 发送系统内部消息给相关人员
            sendSystemNotificationForReleaseSubmit(task, projectName, submitter, submit);
            
        } catch (Exception e) {
            logger.error("发送发布项目提交的微信群通知失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从文件路径中提取文件名
     */
    private String extractFileName(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "未知文件";
        }
        try {
            java.nio.file.Path path = java.nio.file.Paths.get(filePath);
            return path.getFileName().toString();
        } catch (Exception e) {
            logger.warn("提取文件名失败: {}", filePath);
            return "文件名获取失败";
        }
    }

    /**
     * 发送附件下载通知给提交人
     * 
     * @param submit 提交记录
     * @param downloader 下载人
     * @param fileNumber 文件序号
     * @param fileName 文件名
     */
    private void sendDownloadNotificationToSubmitter(Submit2 submit, String downloader, int fileNumber, String fileName) {
        try {
            String submitter = submit.getSubmitter();
            
            // 如果下载人就是提交人自己，则不发送通知
            if (downloader.equals(submitter)) {
                logger.debug("下载人就是提交人自己，跳过发送通知");
                return;
            }
            
            // 获取任务信息用于构建更详细的通知内容
            Optional<ProjectTask> taskOpt = taskService.findTaskById(submit.getTaskId());
            String taskName = taskOpt.map(ProjectTask::getTaskName).orElse("未知任务");
            
            // 创建系统消息
            Message message = new Message();
            message.setReceiver(submitter);
            message.setMessageTitle("附件下载通知");
            
            // 构建消息内容
            StringBuilder contentBuilder = new StringBuilder();
            contentBuilder.append("您的提交记录中的附件被下载了。\n\n");
            if (taskOpt.isPresent()) {
                String taskLink = String.format("<a href=\"/tasks/%d\" target=\"_blank\">%s</a>", 
                    taskOpt.get().getTaskId(), taskName);
                contentBuilder.append(taskLink).append("\n");
            } else {
                contentBuilder.append(taskName).append("\n");
            }
            contentBuilder.append("下载人: ").append(downloader).append("\n");
            contentBuilder.append("下载文件: 附件").append(fileNumber).append(" - ").append(fileName).append("\n");            
            
            
            message.setMessageContent(contentBuilder.toString());
            message.setRelatedType("Submit2");
            message.setRelatedId(submit.getSubmitId());
            message.setRead(false);
            message.setCreatedDate(LocalDateTime.now());
            
            // 保存消息
            messageService.saveMessage(message);
            
            logger.info("已发送附件下载通知给提交人: 提交人={}, 下载人={}, 文件={}", 
                       submitter, downloader, fileName);
            
            // 可选：发送企业微信通知（当前已禁用）
            // 如需启用微信通知，可以在此处添加相关代码
            
        } catch (Exception e) {
            logger.error("发送附件下载通知失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送附件查看通知给提交人（仅系统内消息，不发送微信）
     * 
     * @param submit 提交记录
     * @param viewer 查看人
     * @param fileNumber 文件序号
     * @param fileName 文件名
     */
    private void sendViewNotificationToSubmitter(Submit2 submit, String viewer, int fileNumber, String fileName) {
        try {
            String submitter = submit.getSubmitter();
            
            // 如果查看人就是提交人自己，则不发送通知
            if (viewer.equals(submitter)) {
                logger.debug("查看人就是提交人自己，跳过发送通知");
                return;
            }
            
            // 获取任务信息用于构建更详细的通知内容
            Optional<ProjectTask> taskOpt = taskService.findTaskById(submit.getTaskId());
            String taskName = taskOpt.map(ProjectTask::getTaskName).orElse("未知任务");
            
            // 创建系统消息
            Message message = new Message();
            message.setReceiver(submitter);
            message.setMessageTitle("附件查看通知");
            
            // 构建消息内容
            StringBuilder contentBuilder = new StringBuilder();
            contentBuilder.append("您的提交记录中的附件被查看了。\n\n");
            if (taskOpt.isPresent()) {
                String taskLink = String.format("<a href=\"/tasks/%d\" target=\"_blank\">%s</a>", 
                    taskOpt.get().getTaskId(), taskName);
                contentBuilder.append(taskLink).append("\n");
            } else {
                contentBuilder.append(taskName).append("\n");
            }
            contentBuilder.append("查看人: ").append(viewer).append("\n");
            contentBuilder.append("查看文件: 附件").append(fileNumber).append(" - ").append(fileName).append("\n");            
            
            
            message.setMessageContent(contentBuilder.toString());
            message.setRelatedType("Submit2");
            message.setRelatedId(submit.getSubmitId());
            message.setRead(false);
            message.setCreatedDate(LocalDateTime.now());
            
            // 保存消息
            messageService.saveMessage(message);
            
            logger.info("已发送附件查看通知给提交人: 提交人={}, 查看人={}, 文件={}", 
                       submitter, viewer, fileName);
            
        } catch (Exception e) {
            logger.error("发送附件查看通知失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送发布项目提交的系统内部消息给相关人员
     * 
     * @param task 任务
     * @param projectName 项目名称
     * @param submitter 提交人
     * @param submit 提交记录
     */
    private void sendSystemNotificationForReleaseSubmit(ProjectTask task, String projectName, String submitter, Submit2 submit) {
        try {
            // 获取需要通知的人员列表
            java.util.Set<String> recipients = new java.util.HashSet<>();
            
            // 添加任务负责人
            if (task.getResponsible() != null && !task.getResponsible().isEmpty() && 
                !task.getResponsible().equals("未分配") && !task.getResponsible().equals(submitter)) {
                recipients.add(task.getResponsible());
            }
            
            // 添加项目负责人
            try {
                if (task.getProjectId() != null) {
                    Optional<Project> projectOpt = projectService.findProjectById(task.getProjectId());
                    if (projectOpt.isPresent() && projectOpt.get().getResponsible() != null && 
                        !projectOpt.get().getResponsible().isEmpty() && 
                        !projectOpt.get().getResponsible().equals("未分配") && 
                        !projectOpt.get().getResponsible().equals(submitter)) {
                        recipients.add(projectOpt.get().getResponsible());
                    }
                }
            } catch (Exception e) {
                logger.warn("获取项目负责人时出错: {}", e.getMessage());
            }
            
            // 添加所有管理员和经理
            try {
                List<com.mylog.model.user.User> allUsers = userService.findAllUsers();
                for (com.mylog.model.user.User user : allUsers) {
                    if (user.getRole() != null && 
                        (user.getRole().toString().equals("ADMIN") || user.getRole().toString().equals("MANAGER")) &&
                        !user.getUsername().equals(submitter)) {
                        recipients.add(user.getUsername());
                    }
                }
            } catch (Exception e) {
                logger.warn("获取管理员和经理列表时出错: {}", e.getMessage());
            }
            
            // 如果没有找到任何接收者，记录警告并返回
            if (recipients.isEmpty()) {
                logger.warn("发布项目提交通知：未找到任何有效的接收者");
                return;
            }
            
            // 构建系统消息内容
            StringBuilder messageContent = new StringBuilder();
            messageContent.append("发布项目有新的提交记录\n\n");
            messageContent.append("项目名称: ").append(projectName).append("\n");
            
            // 任务名称使用可点击链接
            String taskLink = String.format("<a href=\"/tasks/%d\" target=\"_blank\">%s</a>", 
                task.getTaskId(), task.getTaskName());
            messageContent.append("任务名称: ").append(taskLink).append("\n");
            messageContent.append("提交人: ").append(submitter).append("\n");
            messageContent.append("提交时间: ").append(submit.getSubmitDateTime().format(
                java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n\n");
            
            // 添加附件信息
            messageContent.append("附件信息:\n");
            int attachmentCount = 0;
            if (submit.getFilePath1() != null && !submit.getFilePath1().isEmpty()) {
                attachmentCount++;
                messageContent.append("• 附件1: ").append(extractFileName(submit.getFilePath1())).append("\n");
            }
            if (submit.getFilePath2() != null && !submit.getFilePath2().isEmpty()) {
                attachmentCount++;
                messageContent.append("• 附件2: ").append(extractFileName(submit.getFilePath2())).append("\n");
            }
            if (submit.getFilePath3() != null && !submit.getFilePath3().isEmpty()) {
                attachmentCount++;
                messageContent.append("• 附件3: ").append(extractFileName(submit.getFilePath3())).append("\n");
            }
            if (submit.getFilePath4() != null && !submit.getFilePath4().isEmpty()) {
                attachmentCount++;
                messageContent.append("• 附件4: ").append(extractFileName(submit.getFilePath4())).append("\n");
            }
            
            messageContent.append("\n共 ").append(attachmentCount).append(" 个附件");
            
            if (submit.getRemarks() != null && !submit.getRemarks().trim().isEmpty()) {
                messageContent.append("\n\n备注: ").append(submit.getRemarks());
            }
            
            messageContent.append("\n\n请及时查看和处理相关附件。");
            
            // 给每个接收者发送系统消息
            for (String recipient : recipients) {
                try {
                    Message message = new Message();
                    message.setReceiver(recipient);
                    message.setMessageTitle("发布项目提交通知");
                    message.setMessageContent(messageContent.toString());
                    message.setRelatedType("Submit2");
                    message.setRelatedId(submit.getSubmitId());
                    message.setRead(false);
                    message.setCreatedDate(LocalDateTime.now());
                    
                    messageService.saveMessage(message);
                    
                    logger.debug("已发送系统消息给: {}", recipient);
                } catch (Exception e) {
                    logger.error("给用户 {} 发送系统消息失败: {}", recipient, e.getMessage());
                }
            }
            
            logger.info("已发送发布项目提交的系统通知给 {} 个用户: {}", 
                       recipients.size(), String.join(", ", recipients));
            
        } catch (Exception e) {
            logger.error("发送发布项目提交的系统内部消息失败: {}", e.getMessage(), e);
        }
    }
}