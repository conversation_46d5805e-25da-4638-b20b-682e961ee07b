package com.mylog.service.impl;

import com.mylog.model.Message;
import com.mylog.repository.MessageRepository;
import com.mylog.service.MessageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Service
public class MessageServiceImpl implements MessageService {

    private static final Logger logger = LoggerFactory.getLogger(MessageServiceImpl.class);

    @Autowired
    private MessageRepository messageRepository;

    @Override
    public List<Message> findAllMessages() {
        return messageRepository.findAll();
    }

    @Override
    public Optional<Message> findMessageById(Long id) {
        return messageRepository.findById(id);
    }

    @Override
    public List<Message> findMessagesByReceiver(String receiver) {
        List<Message> messages = messageRepository.findByReceiverOrderByCreatedDateStrDesc(receiver);
        logger.debug("查询到用户 {} 的消息 {} 条", receiver, messages.size());
        return messages;
    }

    @Override
    public Page<Message> findMessagesByReceiver(String receiver, Pageable pageable) {
        Page<Message> messagePage = messageRepository.findByReceiverOrderByCreatedDateStrDesc(receiver, pageable);
        logger.debug("查询到用户 {} 的消息 {} 条（分页）", receiver, messagePage.getContent().size());
        return messagePage;
    }

    @Override
    public List<Message> findUnreadMessagesByReceiver(String receiver) {
        return messageRepository.findByReceiverAndIsReadOrderByCreatedDateStrDesc(receiver, false);
    }

    @Override
    public Long countUnreadMessagesByReceiver(String receiver) {
        return messageRepository.countUnreadMessagesByReceiver(receiver);
    }

    @Override
    public Message saveMessage(Message message) {
        if (message.getCreatedDate() == null) {
            LocalDateTime now = LocalDateTime.now();
            message.setCreatedDate(now);
            logger.debug("为消息设置创建时间");
        }

        Message savedMessage = messageRepository.save(message);
        logger.debug("保存消息成功，ID: {}", savedMessage.getMessageId());

        return savedMessage;
    }

    @Override
    public void deleteMessage(Long id) {
        messageRepository.deleteById(id);
    }

    @Override
    public void markAsRead(Long id) {
        Optional<Message> messageOpt = messageRepository.findById(id);
        if (messageOpt.isPresent()) {
            Message message = messageOpt.get();
            message.setRead(true);
            messageRepository.save(message);
        }
    }

    @Override
    public void markAllAsRead(String receiver) {
        List<Message> unreadMessages = messageRepository.findByReceiverAndIsReadOrderByCreatedDateStrDesc(receiver, false);
        for (Message message : unreadMessages) {
            message.setRead(true);
            messageRepository.save(message);
        }
    }

    @Override
    public List<Message> findMessagesByRelatedTypeAndId(String relatedType, Long relatedId) {
        return messageRepository.findByRelatedTypeAndRelatedId(relatedType, relatedId);
    }

    @Override
    public Long countTotalMessages() {
        return messageRepository.count();
    }

    @Override
    public Page<Message> searchMessagesByContent(String receiver, String keyword, Pageable pageable) {
        if (keyword == null || keyword.trim().isEmpty()) {
            // 如果搜索关键词为空，返回所有消息
            return findMessagesByReceiver(receiver, pageable);
        }
        
        Page<Message> messagePage = messageRepository.findByReceiverAndContentContaining(receiver, keyword.trim(), pageable);
        logger.debug("搜索用户 {} 包含关键词 '{}' 的消息，找到 {} 条", receiver, keyword, messagePage.getContent().size());
        return messagePage;
    }
}