package com.mylog.model;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Entity
@Table(name = "SubTasks")
@Data
public class SubTask {
    
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long subTaskId;
    
    @Column(nullable = false)
    private Long taskId;
    
    @Column(nullable = false)
    private Integer sequenceNumber;
    
    @Column(nullable = false, columnDefinition = "TEXT")
    private String logContent;
    
    @Column(nullable = false, columnDefinition = "TEXT")
    private String CreatedDate;
    
    @Column(nullable = true, length = 50)
    private String createdBy;

    @Column(name = "Status", nullable = false, length = 20)
    private String status = "未开始";

    @Column(name = "ActualStartDate", nullable = true)
    private String actualStartDate;

    @Column(name = "ActualEndDate", nullable = true)
    private String actualEndDate;
        
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "taskId", referencedColumnName = "taskId", insertable = false, updatable = false)
    private ProjectTask task;

    public void setCreatedDateTime(LocalDateTime dateTime) {
        this.CreatedDate = formatDateTime(dateTime);
    }

    public void setActualStartDate(LocalDateTime dateTime) {
        this.actualStartDate = formatDateTime(dateTime);
    }

    public void setActualEndDate(LocalDateTime dateTime) {
        this.actualEndDate = formatDateTime(dateTime);
    }

    public LocalDateTime getActualStartDate() {
        return parseDateTime(this.actualStartDate);
    }

    public LocalDateTime getActualEndDate() {
        return parseDateTime(this.actualEndDate);
    }

    // 返回格式化的字符串，用于前端显示
    public String getActualStartDateString() {
        return this.actualStartDate;
    }

    public String getActualEndDateString() {
        return this.actualEndDate;
    }

    // 返回中文状态描述（现在直接返回status字段，因为已经是中文了）
    public String getStatusDisplayName() {
        return this.status != null ? this.status : "未知状态";
    }


    
    public void setCreatedDate(String dateStr) {
        this.CreatedDate = dateStr;
    }
    
    public String getCreatedDate() {
        return this.CreatedDate;
    }
    
    public LocalDateTime getCreatedDateTime() {
        return parseDateTime(this.CreatedDate);
    }
    
    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, formatter);
        } catch (Exception e) {
            return null;
        }
    }
    
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(formatter) : null;
    }
}