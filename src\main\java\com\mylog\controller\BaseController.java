package com.mylog.controller;

import com.mylog.util.UserAgentUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 基础控制器
 * 提供所有控制器共用的工具方法
 */
public abstract class BaseController {

    /**
     * 获取当前请求对象
     * @return HttpServletRequest对象
     */
    protected HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            return attributes.getRequest();
        }
        return null;
    }
    
    /**
     * 获取客户端IP地址
     * @return IP地址字符串
     */
    protected String getClientIpAddress() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return "unknown";
        }
        
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    /**
     * 获取访问设备的操作系统类型
     * @return 格式化的访问终端类型
     */
    protected String getAccessType() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return "未知设备";
        }
        return UserAgentUtils.getAccessType(request);
    }
    
    /**
     * 构建绝对URL
     * @param path 相对路径，如 "/tasks/123"
     * @return 绝对URL，如 "https://localhost:8080/tasks/123"
     */
    protected String buildAbsoluteUrl(String path) {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return path; // 如果无法获取请求，返回相对路径作为后备
        }
        
        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append("https")  // 强制使用 https
                 .append("://")
                 .append(request.getServerName())  // 服务器名
                 .append(":")
                 .append(getHttpsPort(request))  // HTTPS端口
                 .append(request.getContextPath()); // 上下文路径
        
        // 确保路径以 / 开头
        if (path != null && !path.startsWith("/")) {
            urlBuilder.append("/");
        }
        if (path != null) {
            urlBuilder.append(path);
        }
        
        return urlBuilder.toString();
    }
    
    /**
     * 获取HTTPS端口
     * @param request HTTP请求
     * @return HTTPS端口号
     */
    private int getHttpsPort(HttpServletRequest request) {
        // 如果当前已经是HTTPS请求，直接返回当前端口
        if ("https".equals(request.getScheme())) {
            return request.getServerPort();
        }
        
        // 检查是否有X-Forwarded-Proto头（代理服务器标识）
        String forwardedProto = request.getHeader("X-Forwarded-Proto");
        if ("https".equals(forwardedProto)) {
            return request.getServerPort();
        }
        
        // 默认HTTPS端口映射
        int currentPort = request.getServerPort();
        if (currentPort == 8080) {
            return 8443; // 开发环境：8080 -> 8443
        } else if (currentPort == 80) {
            return 443;  // 生产环境：80 -> 443
        } else {
            // 对于其他端口，假设HTTPS端口是HTTP端口+363
            return currentPort + 363;
        }
    }
}