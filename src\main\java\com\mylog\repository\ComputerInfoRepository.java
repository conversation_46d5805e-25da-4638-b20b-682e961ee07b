package com.mylog.repository;

import com.mylog.model.ComputerInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ComputerInfoRepository extends JpaRepository<ComputerInfo, Long> {
    
    /**
     * 根据项目ID查找电脑信息列表
     */
    @Query("SELECT c FROM ComputerInfo c WHERE c.projectId = :projectId ORDER BY c.id DESC")
    List<ComputerInfo> findByProjectIdOrderByIdDesc(@Param("projectId") Integer projectId);
    
    /**
     * 根据机器码查找电脑信息
     */
    List<ComputerInfo> findByMachineCode(String machineCode);
    
    /**
     * 根据注册码查找电脑信息
     */
    List<ComputerInfo> findByLicenseCode(String licenseCode);
    
    /**
     * 分页查询所有电脑信息
     */
    @Query("SELECT c FROM ComputerInfo c ORDER BY c.id DESC")
    Page<ComputerInfo> findAllComputerInfoPaged(Pageable pageable);
    
    /**
     * 根据机器码模糊查询
     */
    @Query("SELECT c FROM ComputerInfo c WHERE c.machineCode LIKE %:machineCode% ORDER BY c.id DESC")
    Page<ComputerInfo> findByMachineCodeContaining(@Param("machineCode") String machineCode, Pageable pageable);
    
    /**
     * 根据注册码模糊查询
     */
    @Query("SELECT c FROM ComputerInfo c WHERE c.licenseCode LIKE %:licenseCode% ORDER BY c.id DESC")
    Page<ComputerInfo> findByLicenseCodeContaining(@Param("licenseCode") String licenseCode, Pageable pageable);
    
    /**
     * 根据创建人查询
     */
    @Query("SELECT c FROM ComputerInfo c WHERE c.createdBy = :createdBy ORDER BY c.id DESC")
    Page<ComputerInfo> findByCreatedBy(@Param("createdBy") String createdBy, Pageable pageable);
    
    /**
     * 根据项目ID分页查询（精确匹配）
     */
    @Query("SELECT c FROM ComputerInfo c WHERE c.projectId = :projectId ORDER BY c.id DESC")
    Page<ComputerInfo> findByProjectIdExact(@Param("projectId") Integer projectId, Pageable pageable);
    
    /**
     * 根据项目ID分页查询（模糊查询）
     */
    @Query("SELECT c FROM ComputerInfo c WHERE CAST(c.projectId AS string) LIKE %:projectId% ORDER BY c.id DESC")
    Page<ComputerInfo> findByProjectId(@Param("projectId") String projectId, Pageable pageable);
    
    /**
     * 根据工位信息模糊查询
     */
    @Query("SELECT c FROM ComputerInfo c WHERE c.workstationInfo LIKE %:workstationInfo% ORDER BY c.id DESC")
    Page<ComputerInfo> findByWorkstationInfoContaining(@Param("workstationInfo") String workstationInfo, Pageable pageable);
    
    /**
     * 根据备注模糊查询
     */
    @Query("SELECT c FROM ComputerInfo c WHERE c.remark LIKE %:remark% ORDER BY c.id DESC")
    Page<ComputerInfo> findByRemarkContaining(@Param("remark") String remark, Pageable pageable);
    
    /**
     * 根据项目名称模糊查询电脑信息
     * 使用JOIN查询避免循环依赖
     */
    @Query("SELECT c FROM ComputerInfo c JOIN Project p ON c.projectId = p.projectId WHERE p.projectName LIKE %:projectName% ORDER BY c.id DESC")
    Page<ComputerInfo> findByProjectNameContaining(@Param("projectName") String projectName, Pageable pageable);
}
