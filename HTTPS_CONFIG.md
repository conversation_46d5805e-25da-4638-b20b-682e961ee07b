# HTTPS配置说明

## 概述

本项目已配置为支持强制HTTPS重定向，确保所有链接都指向HTTPS协议。

## 配置方式

### 1. 开发环境（默认）

默认情况下，应用运行在HTTP模式下：
- `mylog.force.https=false`
- 端口：8080
- 协议：HTTP

### 2. 生产环境（HTTPS强制）

有多种方式启用HTTPS强制模式：

#### 方式一：使用环境变量
```bash
export MYLOG_FORCE_HTTPS=true
java -jar mylog-web-0.0.1-SNAPSHOT.jar
```

#### 方式二：使用系统属性
```bash
java -Dmylog.force.https=true -jar mylog-web-0.0.1-SNAPSHOT.jar
```

#### 方式三：使用生产环境配置
```bash
java -jar mylog-web-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```

#### 方式四：使用提供的启动脚本
```bash
# Windows
startup-prod-https.bat

# Linux/Mac
./startup-prod-https.sh
```

## SSL证书配置

如果需要配置SSL证书（生产环境推荐），请在`application-prod.properties`中配置：

```properties
# SSL证书配置
server.ssl.key-store=classpath:keystore.p12
server.ssl.key-store-password=your-password
server.ssl.key-store-type=PKCS12
server.ssl.key-alias=mylog-web
```

## 代码层面的变化

1. **BaseController.buildAbsoluteUrl()方法**：
   - 强制使用HTTPS协议
   - 智能端口映射（8080->8443, 80->443）
   - 支持代理服务器的X-Forwarded-Proto头

2. **SecurityConfig**：
   - 添加了条件性的HTTPS强制要求
   - 只在`mylog.force.https=true`时启用

3. **HttpsRedirectConfig**：
   - 自动配置HTTP到HTTPS的重定向
   - 使用Tomcat连接器实现

## 测试

### 开发环境测试
```bash
# 启动应用
java -jar mylog-web-0.0.1-SNAPSHOT.jar

# 访问测试
curl http://localhost:8080/login
```

### HTTPS强制模式测试
```bash
# 启动应用（HTTPS模式）
MYLOG_FORCE_HTTPS=true java -jar mylog-web-0.0.1-SNAPSHOT.jar

# 测试HTTP请求会重定向到HTTPS
curl -v http://localhost:8080/login
# 应该收到重定向响应到https://localhost:8443/login
```

## 注意事项

1. **证书配置**：生产环境需要配置有效的SSL证书
2. **端口开放**：确保防火墙开放了HTTPS端口（默认8443或443）
3. **代理配置**：如果使用反向代理（如Nginx），确保正确配置X-Forwarded-Proto头
4. **Cookie安全**：生产环境自动启用secure和httpOnly cookie选项

## 故障排除

1. **端口冲突**：如果8443端口被占用，可以通过`server.port`配置修改
2. **证书问题**：检查证书路径和密码配置
3. **重定向循环**：检查代理服务器配置，避免多重重定向
