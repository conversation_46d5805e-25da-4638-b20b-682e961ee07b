﻿<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('仪表盘')}">
    <meta charset="UTF-8">
    <title>仪表盘</title>
    <!-- 表格排序功能所需的CSS和JS将通过layout模板引入 -->
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">仪表盘</h1>
        </div>

        <!-- 附件提醒区域 -->
        <div th:if="${attachmentReminderTasks != null and !attachmentReminderTasks.empty}" 
             class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
            <div class="d-flex align-items-center mb-2">
                <i class="bi bi-exclamation-triangle-fill fs-4 me-2"></i>
                <h5 class="mb-0">🔔 重要提醒：发布项目有新附件需要处理</h5>
            </div>
            <p class="mb-2">以下发布项目的任务在最近7天内有新附件提交，请及时查看或下载：</p>
            <div class="row">
                <div th:each="task, iterStat : ${attachmentReminderTasks}" class="col-md-6 mb-2">
                    <div class="card border-warning">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title mb-1">
                                        <i class="bi bi-folder2-open me-1"></i>
                                        <span th:text="${task.project?.projectName ?: '未知项目'}">项目名称</span>
                                    </h6>
                                    <p class="card-text mb-1">
                                        <i class="bi bi-list-task me-1"></i>
                                        <span th:text="${task.taskName}">任务名称</span>
                                    </p>
                                    <small class="text-muted">
                                        <i class="bi bi-person me-1"></i>
                                        负责人: <span th:text="${task.responsible ?: '未指定'}">负责人</span>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <a th:href="@{/tasks/{id}(id=${task.taskId})}" 
                                       class="btn btn-warning btn-sm">
                                        <i class="bi bi-eye me-1"></i>查看任务
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

            <!-- 提示消息 -->
            <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                <span th:text="${message}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <span th:text="${error}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>            <!-- 统计卡片 - 已隐藏 -->
            <!-- 
            <div class="row">
                <div class="col-md-3 mb-4">
                    <a th:href="@{/projects}" class="text-decoration-none">
                        <div class="card bg-success text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">活动项目总数</h6>
                                        <h2 class="card-text" th:text="${nonArchivedProjectCount}">0</h2>
                                    </div>
                                    <i class="bi bi-folder fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-md-3 mb-4">
                    <a th:href="@{/projects/advanced-search(fieldNames='status',field_status='进行中')}" class="text-decoration-none">
                        <div class="card bg-info text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">进行中项目</h6>
                                        <h2 class="card-text" th:text="${nonArchivedInProgressProjectCount}">0</h2>
                                    </div>
                                    <i class="bi bi-hourglass-split fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-md-3 mb-4">
                    <a th:href="@{/tasks/management}" class="text-decoration-none">
                        <div class="card bg-danger text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">活动任务总数</h6>
                                        <h2 class="card-text" th:text="${nonArchivedTaskCount}">0</h2>
                                    </div>
                                    <i class="bi bi-clipboard-check fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>                <div class="col-md-3 mb-4">
                    <a th:href="@{/tasks/advanced-search(fieldNames='status',field_status='进行中',returnTo='management',originalSource='dashboard')}" class="text-decoration-none">
                        <div class="card bg-primary text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">进行中任务</h6>
                                        <h2 class="card-text" th:text="${nonArchivedInProgressTaskCount}">0</h2>
                                    </div>
                                    <i class="bi bi-list-task fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            -->            <!-- 第二行卡片 -->

            
            <div class="row">
                  <!-- 程序发布任务 -->
                <div class="col-md-6 mb-4">
                    <a th:href="@{/tasks/advanced-search(fieldNames='customerName',field_customerName='视觉程序发布',returnTo='management',originalSource='dashboard')}" class="text-decoration-none">
                        <div class="card bg-danger text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">                                    <div>
                                        <h6 class="card-title">程序发布任务</h6>
                                        <h2 class="card-text" th:text="${visionProgramTaskCount}">0</h2>
                                    </div>
                                    <i class="bi bi-cpu fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>                <!-- 技术资料任务 -->
                <div class="col-md-6 mb-4">
                    <a th:href="@{/tasks/advanced-search(fieldNames='customerName',field_customerName='技术资料发布',returnTo='management',originalSource='dashboard')}" class="text-decoration-none">
                        <div class="card bg-success text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">技术资料任务</h6>
                                        <h2 class="card-text" th:text="${technicalDocumentTaskCount}">0</h2>
                                    </div>
                                    <i class="bi bi-file-text fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- 人员状态卡片列表 -->
            <div class="row mt-4">
                <!-- 人员状态 -->
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                人员状态（任务数量不包含“分管”和“培训”类型）
                                <span class="ms-2 badge rounded-pill bg-secondary"
                                    th:text="${#lists.size(validPersonnelStatus)}">0</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="personnelStatusTable">
                                    <thead>
                                        <tr>
                                            <th class="sortable" data-sort="name">姓名 <i
                                                    class="bi bi-arrow-down-up sort-icon"></i></th>
                                            <th class="sortable" data-sort="status">工作状态 <i
                                                    class="bi bi-arrow-down-up sort-icon"></i></th>
                                            <th class="sortable" data-sort="location">所在地 <i
                                                    class="bi bi-arrow-down-up sort-icon"></i></th>
                                            <th class="sortable" data-sort="inProgressProjectCount">进行中项目数 <i
                                                    class="bi bi-arrow-down-up sort-icon"></i></th>
                                            <th class="sortable" data-sort="inProgressTaskCount">进行中任务数 <i
                                                    class="bi bi-arrow-down-up sort-icon"></i></th>
                                            <th class="sortable" data-sort="newProjectsInSixMonths">半年内新建项目数 <i
                                                    class="bi bi-arrow-down-up sort-icon"></i></th>
                                            <th class="sortable" data-sort="newTasksInSixMonths">半年内新建任务数 <i
                                                    class="bi bi-arrow-down-up sort-icon"></i></th>
                                            <th class="sortable" data-sort="completedProjectsInSixMonths">半年内完成项目数 <i
                                                    class="bi bi-arrow-down-up sort-icon"></i></th>
                                            <th class="sortable" data-sort="completedTasksInSixMonths">半年内完成任务数 <i
                                                    class="bi bi-arrow-down-up sort-icon"></i></th>
                                            <th class="sortable" data-sort="effectiveTime" style="min-width: 160px;">
                                                生效时间 <i class="bi bi-arrow-down-up sort-icon"></i></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="personnel : ${validPersonnelStatus}">
                                            <td th:attr="data-value=${personnel.name}">
                                                <!-- 如果有工作流实例ID，显示为链接 -->
                                                <a th:if="${personnel.workflowInstanceId != null}"
                                                    th:href="@{/workflow/instances/{id}(id=${personnel.workflowInstanceId})}"
                                                    class="text-decoration-underline"
                                                    th:text="${personnel.name}">姓名</a>
                                                <!-- 如果没有工作流实例ID，显示为普通文本 -->
                                                <span th:if="${personnel.workflowInstanceId == null}"
                                                    th:text="${personnel.name}">姓名</span>
                                            </td>
                                            <td th:attr="data-value=${personnel.status}">
                                                <!-- 如果有工作流实例ID，显示为链接 -->
                                                <a th:if="${personnel.workflowInstanceId != null}"
                                                    th:href="@{/workflow/instances/{id}(id=${personnel.workflowInstanceId})}"
                                                    th:class="${'badge text-decoration-underline ' + 
                                                    (personnel.status == '厂内上班' ? 'bg-success' : 
                                                    (personnel.status == '出差中' ? 'bg-primary' : 
                                                    (personnel.status == '请假中' ? 'bg-mycolor' : 'bg-secondary')))}"
                                                    th:text="${personnel.status}">状态</a>
                                                <!-- 如果没有工作流实例ID，显示为普通标签 -->
                                                <span th:if="${personnel.workflowInstanceId == null}"
                                                    th:class="${'badge ' + 
                                                    (personnel.status == '厂内上班' ? 'bg-success' : 
                                                    (personnel.status == '出差中' ? 'bg-primary' : 
                                                    (personnel.status == '请假中' ? 'bg-mycolor' : 'bg-secondary')))}"
                                                    th:text="${personnel.status}">状态</span>
                                            </td>
                                            <td th:attr="data-value=${personnel.location}"
                                                th:text="${personnel.location}">
                                                所在地</td>


                                            <td th:attr="data-value=${personnel.inProgressProjectCount}">
                                                <span th:if="${personnel.inProgressProjectCount == 0}"
                                                    th:text="${personnel.inProgressProjectCount}"></span>
                                                <a th:if="${personnel.inProgressProjectCount > 0}"
                                                    th:href="@{/projects/advanced-search(fieldNames='responsible',fieldNames='status',field_responsible=${personnel.name},field_status='进行中',returnTo='management')}"
                                                    th:class="'badge bg-info text-decoration-underline'"
                                                    th:text="${personnel.inProgressProjectCount}"></a>
                                            </td>
                                            <td th:attr="data-value=${personnel.inProgressTaskCount}">
                                                <span th:if="${personnel.inProgressTaskCount == 0}"
                                                    th:text="${personnel.inProgressTaskCount}"></span>                                                <a th:if="${personnel.inProgressTaskCount > 0}"
                                                    th:href="@{/tasks/advanced-search(fieldNames='responsible',fieldNames='status',field_responsible=${personnel.name},field_status='进行中',returnTo='management',originalSource='dashboard')}"
                                                    th:class="'badge bg-primary text-decoration-underline'"
                                                    th:text="${personnel.inProgressTaskCount}"></a>
                                            </td>
                                            <td th:attr="data-value=${personnel.newProjectsInSixMonths}">
                                                <a th:if="${personnel.newProjectsInSixMonths == 0}"
                                                    th:href="@{/projects/advanced-search(fieldNames='responsible',fieldNames='createdDate',field_responsible=${personnel.name},field_createdDate_start=${#temporals.format(#temporals.createNow().minusMonths(6), 'yyyy-MM-dd')},field_createdDate_end=${#temporals.format(#temporals.createNow(), 'yyyy-MM-dd')})}"
                                                    th:text="${personnel.newProjectsInSixMonths}"
                                                    class="text-decoration-underline text-muted">0</a>
                                                <a th:if="${personnel.newProjectsInSixMonths > 0}"
                                                    th:href="@{/projects/advanced-search(fieldNames='responsible',fieldNames='createdDate',field_responsible=${personnel.name},field_createdDate_start=${#temporals.format(#temporals.createNow().minusMonths(6), 'yyyy-MM-dd')},field_createdDate_end=${#temporals.format(#temporals.createNow(), 'yyyy-MM-dd')})}"
                                                    th:class="'badge bg-info text-decoration-underline'"
                                                    th:text="${personnel.newProjectsInSixMonths}"></a>
                                            </td>                                            <td th:attr="data-value=${personnel.newTasksInSixMonths}">
                                                <a th:if="${personnel.newTasksInSixMonths == 0}"
                                                    th:href="@{/tasks/advanced-search(fieldNames='responsible',fieldNames='createdDate',field_responsible=${personnel.name},field_createdDate_start=${#temporals.format(#temporals.createNow().minusMonths(6), 'yyyy-MM-dd')},field_createdDate_end=${#temporals.format(#temporals.createNow(), 'yyyy-MM-dd')},returnTo='management',originalSource='dashboard')}"
                                                    th:text="${personnel.newTasksInSixMonths}"
                                                    class="text-decoration-underline text-muted">0</a>
                                                <a th:if="${personnel.newTasksInSixMonths > 0}"
                                                    th:href="@{/tasks/advanced-search(fieldNames='responsible',fieldNames='createdDate',field_responsible=${personnel.name},field_createdDate_start=${#temporals.format(#temporals.createNow().minusMonths(6), 'yyyy-MM-dd')},field_createdDate_end=${#temporals.format(#temporals.createNow(), 'yyyy-MM-dd')},returnTo='management',originalSource='dashboard')}"
                                                    th:class="'badge bg-primary text-decoration-underline'"
                                                    th:text="${personnel.newTasksInSixMonths}"></a>
                                            </td>
                                            <td th:attr="data-value=${personnel.completedProjectsInSixMonths}">
                                                <a th:if="${personnel.completedProjectsInSixMonths == 0}"
                                                    th:href="@{/projects/advanced-search(fieldNames='responsible',fieldNames='status',fieldNames='actualEndDate',field_responsible=${personnel.name},field_status='已完成',field_actualEndDate_start=${#temporals.format(#temporals.createNow().minusMonths(6), 'yyyy-MM-dd')},field_actualEndDate_end=${#temporals.format(#temporals.createNow(), 'yyyy-MM-dd')})}"
                                                    th:text="${personnel.completedProjectsInSixMonths}"
                                                    class="text-decoration-underline text-muted">0</a>
                                                <a th:if="${personnel.completedProjectsInSixMonths > 0}"
                                                    th:href="@{/projects/advanced-search(fieldNames='responsible',fieldNames='status',fieldNames='actualEndDate',field_responsible=${personnel.name},field_status='已完成',field_actualEndDate_start=${#temporals.format(#temporals.createNow().minusMonths(6), 'yyyy-MM-dd')},field_actualEndDate_end=${#temporals.format(#temporals.createNow(), 'yyyy-MM-dd')})}"
                                                    th:class="'badge bg-info text-decoration-underline'"
                                                    th:text="${personnel.completedProjectsInSixMonths}"></a>
                                            </td>                                            <td th:attr="data-value=${personnel.completedTasksInSixMonths}">
                                                <a th:if="${personnel.completedTasksInSixMonths == 0}"
                                                    th:href="@{/tasks/advanced-search(fieldNames='responsible',fieldNames='actualEndDate',fieldNames='status',field_responsible=${personnel.name},field_actualEndDate_start=${#temporals.format(#temporals.createNow().minusMonths(6), 'yyyy-MM-dd')},field_actualEndDate_end=${#temporals.format(#temporals.createNow(), 'yyyy-MM-dd')},field_status='已完成',returnTo='management',originalSource='dashboard')}"
                                                    th:text="${personnel.completedTasksInSixMonths}"
                                                    class="text-decoration-underline text-muted">0</a>
                                                <a th:if="${personnel.completedTasksInSixMonths > 0}"
                                                    th:href="@{/tasks/advanced-search(fieldNames='responsible',fieldNames='actualEndDate',fieldNames='status',field_responsible=${personnel.name},field_actualEndDate_start=${#temporals.format(#temporals.createNow().minusMonths(6), 'yyyy-MM-dd')},field_actualEndDate_end=${#temporals.format(#temporals.createNow(), 'yyyy-MM-dd')},field_status='已完成',returnTo='management',originalSource='dashboard')}"
                                                    th:class="'badge bg-primary text-decoration-underline'"
                                                    th:text="${personnel.completedTasksInSixMonths}"></a>
                                            </td>
                                            <td th:attr="data-value=${personnel.effectiveTime}"
                                                th:text="${personnel.effectiveTime}">生效时间</td>
                                        </tr>
                                        <tr th:if="${#lists.isEmpty(validPersonnelStatus)}">
                                            <td colspan="11" class="text-center">暂无人员状态记录</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近项目和最近任务 -->
            <div class="row mt-4">
                <!-- 最近项目 -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">最近项目</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>状态</th>
                                            <th style="min-width: 100px;">创建时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="project : ${recentProjects}">
                                            <td>
                                                <a th:href="@{/projects/{id}(id=${project.projectId}, from='dashboard')}"
                                                    th:text="${project.projectName}">项目名称</a>
                                            </td>
                                            <td>
                                                <span th:class="${'badge ' +
                                                (project.status == '进行中' ? 'bg-primary' :
                                                (project.status == '已完成' ? 'bg-success' :
                                                (project.status == '未开始' ? 'bg-secondary' : 'bg-secondary')))}"
                                                    th:text="${project.status}">状态</span>
                                            </td>
                                            <td th:text="${#temporals.format(project.createdDateTime, 'yyyy-MM-dd')}">
                                                2025-01-01
                                            </td>
                                        </tr>
                                        <tr th:if="${#lists.isEmpty(recentProjects)}">
                                            <td colspan="3" class="text-center">暂无最近项目</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近任务 -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">最近任务</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>任务名称</th>
                                            <th>所属项目</th>
                                            <th>状态</th>
                                            <th style="min-width: 100px;">创建时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="task : ${recentTasks}">
                                            <td>
                                                <a th:href="@{/tasks/{id}(id=${task.taskId}, from='dashboard')}"
                                                    th:text="${task.taskName}">任务名称</a>
                                            </td>
                                            <td>
                                                <a th:if="${task.project != null}"
                                                    th:href="@{/projects/{id}(id=${task.projectId}, from='dashboard')}"
                                                    th:text="${task.project.projectName}">项目名称</a>
                                                <span th:unless="${task.project != null}">-</span>
                                            </td>
                                            <td>
                                                <span th:class="${'badge ' +
                                                (task.status == '进行中' ? 'bg-primary' :
                                                (task.status == '已完成' ? 'bg-success' :
                                                (task.status == '未开始' ? 'bg-secondary' :
                                                (task.status == '已暂停' ? 'bg-dark' : 'bg-secondary'))))}"
                                                    th:text="${task.status}">状态</span>
                                            </td>
                                            <td th:text="${#temporals.format(task.createdDateTime, 'yyyy-MM-dd')}">
                                                2025-01-01
                                            </td>
                                        </tr>
                                        <tr th:if="${#lists.isEmpty(recentTasks)}">
                                            <td colspan="4" class="text-center">暂无最近任务</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 表格排序功能脚本 -->
        <script>
            $(document).ready(function () {
                console.log('仪表板页面加载完成，初始化表格排序功能');

                // 人员状态表排序功能
                const personnelTable = $('#personnelStatusTable');

                if (personnelTable.length > 0) {
                    console.log('找到人员状态表，初始化排序功能');

                    // 为可排序的表头添加点击事件
                    personnelTable.find('th.sortable').on('click', function () {
                        console.log('点击表头:', $(this).text().trim());

                        const $header = $(this);
                        const sortField = $header.data('sort');

                        if (!sortField) {
                            console.warn('表头缺少 data-sort 属性');
                            return;
                        }

                        // 确定排序方向
                        const currentDirection = $header.attr('data-direction');
                        const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';

                        console.log(`排序字段: ${sortField}, 方向: ${newDirection}`);

                        // 重置所有表头的排序状态
                        personnelTable.find('th.sortable').each(function () {
                            $(this).attr('data-direction', '');
                            $(this).find('.sort-icon').removeClass('bi-arrow-up bi-arrow-down').addClass('bi-arrow-down-up');
                        });

                        // 设置当前表头的排序状态
                        $header.attr('data-direction', newDirection);
                        const $icon = $header.find('.sort-icon');
                        $icon.removeClass('bi-arrow-down-up bi-arrow-up bi-arrow-down');
                        $icon.addClass(newDirection === 'asc' ? 'bi-arrow-up' : 'bi-arrow-down');

                        // 执行排序
                        sortPersonnelTable(sortField, newDirection);
                    });

                    // 默认按进行中任务数排序（从大到小）
                    setTimeout(() => {
                        const taskCountHeader = personnelTable.find('th[data-sort="inProgressTaskCount"]');
                        if (taskCountHeader.length > 0) {
                            console.log('执行默认排序：按进行中任务数降序');
                            // 设置为降序并执行排序
                            taskCountHeader.attr('data-direction', 'asc'); // 设置为asc，这样点击后会变成desc
                            taskCountHeader.click(); // 第一次点击会变成desc（从大到小）
                        }
                    }, 100);
                } else {
                    console.log('未找到人员状态表');
                }

                /**
                 * 对人员状态表进行排序
                 * @param {string} sortField - 排序字段
                 * @param {string} direction - 排序方向 ('asc' 或 'desc')
                 */
                function sortPersonnelTable(sortField, direction) {
                    console.log(`开始排序 - 字段: ${sortField}, 方向: ${direction}`);

                    const $tbody = personnelTable.find('tbody');
                    const $allRows = $tbody.find('tr');

                    // 分离数据行和"暂无数据"行
                    const $dataRows = $allRows.filter(function () {
                        const $firstTd = $(this).find('td').first();
                        // 检查是否是"暂无数据"行（有colspan属性）
                        return !$firstTd.attr('colspan');
                    });

                    const $noDataRows = $allRows.filter(function () {
                        const $firstTd = $(this).find('td').first();
                        // 检查是否是"暂无数据"行（有colspan属性）
                        return $firstTd.attr('colspan');
                    });

                    console.log(`找到 ${$dataRows.length} 行数据，${$noDataRows.length} 行无数据提示`);

                    if ($dataRows.length === 0) {
                        console.log('没有数据行需要排序');
                        return;
                    }

                    // 获取列索引
                    const headers = personnelTable.find('th');
                    let columnIndex = -1;

                    headers.each(function (index) {
                        if ($(this).data('sort') === sortField) {
                            columnIndex = index;
                            return false; // 跳出循环
                        }
                    });

                    if (columnIndex === -1) {
                        console.error(`未找到排序字段 ${sortField} 对应的列`);
                        return;
                    }

                    console.log(`排序列索引: ${columnIndex}`);

                    // 对行进行排序
                    const sortedRows = $dataRows.toArray().sort((a, b) => {
                        const $cellA = $(a).find('td').eq(columnIndex);
                        const $cellB = $(b).find('td').eq(columnIndex);

                        // 获取排序值
                        let valueA = getSortValue($cellA, sortField);
                        let valueB = getSortValue($cellB, sortField);

                        // 处理空值
                        if (valueA === null && valueB === null) return 0;
                        if (valueA === null) return 1;
                        if (valueB === null) return -1;

                        // 根据字段类型进行比较
                        let result = 0;

                        if (sortField === 'effectiveTime') {
                            // 日期排序
                            const dateA = new Date(valueA);
                            const dateB = new Date(valueB);
                            result = dateA.getTime() - dateB.getTime();
                        } else if (sortField.includes('Count') || sortField.includes('InSixMonths')) {
                            // 数值排序
                            const numA = parseInt(valueA) || 0;
                            const numB = parseInt(valueB) || 0;
                            result = numA - numB;
                        } else {
                            // 文本排序（支持中文）
                            result = valueA.localeCompare(valueB, 'zh-CN');
                        }

                        return direction === 'asc' ? result : -result;
                    });

                    console.log(`排序完成，重新排列 ${sortedRows.length} 行数据`);

                    // 重新排列行
                    $tbody.empty();

                    // 先添加排序后的数据行
                    $(sortedRows).each(function () {
                        const $row = $(this);
                        $row.addClass('sort-highlight');
                        $tbody.append($row);

                        // 移除高亮效果
                        setTimeout(() => {
                            $row.removeClass('sort-highlight');
                        }, 1000);
                    });

                    // 最后添加"暂无数据"行（如果存在）
                    $noDataRows.each(function () {
                        $tbody.append($(this));
                    });

                    console.log(`表格排序完成，最终行数: ${$tbody.find('tr').length}`);
                }

                /**
                 * 获取单元格的排序值
                 * @param {jQuery} $cell - 单元格jQuery对象
                 * @param {string} sortField - 排序字段
                 * @returns {string|null} 排序值
                 */
                function getSortValue($cell, sortField) {
                    // 首先尝试从 data-value 属性获取
                    let value = $cell.attr('data-value');

                    if (value !== undefined && value !== null && value !== '') {
                        return value.toString().trim();
                    }

                    // 如果没有 data-value，从文本内容获取
                    value = $cell.text().trim();

                    if (value === '' || value === '-') {
                        return null;
                    }

                    return value;
                }
            });
        </script>
    </body>

</html>